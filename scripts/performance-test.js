#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Running Performance Tests...\n');

// Test 1: Bundle Analysis
console.log('📦 Analyzing bundle size...');
try {
  execSync('npm run build:analyze', { stdio: 'inherit' });
  console.log('✅ Bundle analysis complete\n');
} catch (error) {
  console.log('❌ Bundle analysis failed:', error.message);
}

// Test 2: Check for performance optimizations
console.log('🔍 Checking performance optimizations...');

const optimizations = [
  {
    name: 'Next.js Image Optimization',
    check: () => {
      const nextConfig = fs.readFileSync('next.config.ts', 'utf8');
      return nextConfig.includes('images:') && nextConfig.includes('formats:');
    }
  },
  {
    name: 'Dynamic Imports',
    check: () => {
      const files = [
        'components/dashboard/analytics-chart-dynamic.tsx',
        'components/dashboard/theme-customizer-dynamic.tsx'
      ];
      return files.every(file => fs.existsSync(file));
    }
  },
  {
    name: 'Loading States',
    check: () => {
      const files = [
        'components/profile/profile-skeleton.tsx',
        'app/[username]/loading.tsx'
      ];
      return files.every(file => fs.existsSync(file));
    }
  },
  {
    name: 'Performance Monitoring',
    check: () => {
      const files = [
        'lib/utils/performance-optimizer.ts',
        'lib/hooks/use-performance.ts',
        'components/providers/performance-provider.tsx'
      ];
      return files.every(file => fs.existsSync(file));
    }
  },
  {
    name: 'Caching Headers',
    check: () => {
      const middleware = fs.readFileSync('middleware.ts', 'utf8');
      return middleware.includes('Cache-Control') && middleware.includes('max-age');
    }
  },
  {
    name: 'Static Generation',
    check: () => {
      const profilePage = fs.readFileSync('app/[username]/page.tsx', 'utf8');
      return profilePage.includes('generateStaticParams') && profilePage.includes('revalidate');
    }
  }
];

optimizations.forEach(opt => {
  const passed = opt.check();
  console.log(`${passed ? '✅' : '❌'} ${opt.name}`);
});

console.log('\n🎯 Performance optimization summary:');
const passedCount = optimizations.filter(opt => opt.check()).length;
console.log(`${passedCount}/${optimizations.length} optimizations implemented`);

if (passedCount === optimizations.length) {
  console.log('🎉 All performance optimizations are in place!');
} else {
  console.log('⚠️  Some optimizations are missing. Please review the failed checks.');
}

// Test 3: Check bundle size limits
console.log('\n📊 Checking bundle size limits...');
const buildDir = '.next';
if (fs.existsSync(buildDir)) {
  try {
    const stats = execSync('find .next/static -name "*.js" -exec wc -c {} + | tail -1', { encoding: 'utf8' });
    const totalSize = parseInt(stats.trim().split(' ')[0]);
    const sizeMB = (totalSize / 1024 / 1024).toFixed(2);
    
    console.log(`Total JS bundle size: ${sizeMB} MB`);
    
    if (totalSize < 5 * 1024 * 1024) { // 5MB limit
      console.log('✅ Bundle size is within acceptable limits');
    } else {
      console.log('⚠️  Bundle size is large, consider further optimization');
    }
  } catch (error) {
    console.log('❌ Could not analyze bundle size:', error.message);
  }
} else {
  console.log('⚠️  Build directory not found. Run "npm run build" first.');
}

console.log('\n✨ Performance test complete!');