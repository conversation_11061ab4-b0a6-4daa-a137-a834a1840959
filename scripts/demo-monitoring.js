#!/usr/bin/env node

/**
 * Demo script to showcase the username availability monitoring functionality
 * This script simulates various scenarios and shows the monitoring data
 */

const { performanceMonitor } = require('../lib/utils/performance-monitor')
const { monitoringDashboard } = require('../lib/utils/monitoring-dashboard')

console.log('🚀 Username Availability Monitoring Demo\n')

// Simulate various performance scenarios
console.log('📊 Simulating performance metrics...')

// Successful operations
for (let i = 0; i < 50; i++) {
  const duration = Math.random() * 500 + 100 // 100-600ms
  performanceMonitor.recordPerformance(
    'username-availability-check',
    duration,
    true,
    undefined,
    { username: `user${i}`, cached: Math.random() > 0.7 }
  )
}

// Some failed operations
for (let i = 0; i < 5; i++) {
  const duration = Math.random() * 1000 + 200 // 200-1200ms
  const errorTypes = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'SERVER_ERROR']
  const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)]
  
  performanceMonitor.recordPerformance(
    'username-availability-check',
    duration,
    false,
    `Test ${errorType}`,
    { username: `failuser${i}`, errorType }
  )
  
  performanceMonitor.recordError(
    'username-availability-check',
    `Test ${errorType}`,
    errorType,
    { username: `failuser${i}` }
  )
}

// Some slow operations
for (let i = 0; i < 3; i++) {
  const duration = Math.random() * 2000 + 1500 // 1500-3500ms (slow)
  performanceMonitor.recordPerformance(
    'username-availability-check',
    duration,
    true,
    undefined,
    { username: `slowuser${i}`, slow: true }
  )
}

// Cache operations
for (let i = 0; i < 30; i++) {
  const operation = Math.random() > 0.3 ? 'hit' : 'miss'
  performanceMonitor.recordCache(operation, `user${i}`, { 
    available: Math.random() > 0.5 
  })
}

console.log('✅ Performance metrics simulated\n')

// Display monitoring statistics
console.log('📈 Current Monitoring Statistics:')
console.log('=' .repeat(50))

const stats = performanceMonitor.getStats()

console.log(`\n🎯 Performance Metrics:`)
console.log(`  Total Requests: ${stats.performance.totalRequests}`)
console.log(`  Average Response Time: ${stats.performance.averageResponseTime}ms`)
console.log(`  Success Rate: ${stats.performance.successRate}%`)
console.log(`  Slow Queries: ${stats.performance.slowQueries}`)
console.log(`  Timeouts: ${stats.performance.timeouts}`)

console.log(`\n💾 Cache Metrics:`)
console.log(`  Total Operations: ${stats.cache.totalOperations}`)
console.log(`  Hit Rate: ${stats.cache.hitRate}%`)
console.log(`  Miss Rate: ${stats.cache.missRate}%`)
console.log(`  Total Hits: ${stats.cache.totalHits}`)
console.log(`  Total Misses: ${stats.cache.totalMisses}`)

console.log(`\n❌ Error Metrics:`)
console.log(`  Total Errors: ${stats.errors.totalErrors}`)
console.log(`  Error Rate: ${stats.errors.errorRate}%`)
console.log(`  Errors by Type:`)
Object.entries(stats.errors.errorsByType).forEach(([type, count]) => {
  console.log(`    ${type}: ${count}`)
})

// Display dashboard data
console.log('\n🎛️  Dashboard Summary:')
console.log('=' .repeat(50))

const dashboardData = monitoringDashboard.getDashboardData()

console.log(`\n🚦 System Status: ${dashboardData.summary.status.toUpperCase()}`)
console.log(`⏱️  Uptime: ${dashboardData.summary.uptime}`)

console.log(`\n📊 Performance Dashboard:`)
console.log(`  P95 Response Time: ${dashboardData.performance.p95ResponseTime}ms`)
console.log(`  P99 Response Time: ${dashboardData.performance.p99ResponseTime}ms`)

console.log(`\n💽 Cache Efficiency: ${dashboardData.cache.efficiency.toUpperCase()}`)

console.log(`\n🔝 Top Errors:`)
dashboardData.errors.topErrors.slice(0, 3).forEach((error, index) => {
  console.log(`  ${index + 1}. ${error.type}: ${error.count} (${error.percentage}%)`)
})

// Generate and display report
console.log('\n📋 Monitoring Report:')
console.log('=' .repeat(50))
console.log(monitoringDashboard.generateReport())

// Health check
console.log('\n🏥 Health Check:')
console.log('=' .repeat(50))
const healthCheck = monitoringDashboard.getHealthCheck()
console.log(`Status: ${healthCheck.status.toUpperCase()}`)
console.log('Individual Checks:')
Object.entries(healthCheck.checks).forEach(([check, passing]) => {
  const status = passing ? '✅' : '❌'
  console.log(`  ${status} ${check}: ${passing ? 'PASS' : 'FAIL'}`)
})

console.log('\n🎉 Demo completed! The monitoring system is working correctly.')
console.log('\n💡 In a real application, this data would be:')
console.log('   - Sent to external monitoring services (DataDog, New Relic, etc.)')
console.log('   - Displayed in a web dashboard')
console.log('   - Used for alerting and notifications')
console.log('   - Stored in time-series databases for historical analysis')