import { auth } from '@/auth'
import { NextResponse } from 'next/server'

export default auth((req) => {
  const response = NextResponse.next()
  
  // Add caching headers for static assets
  if (req.nextUrl.pathname.startsWith('/_next/static/') || 
      req.nextUrl.pathname.match(/\.(jpg|jpeg|png|gif|webp|avif|ico|svg)$/)) {
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  }
  
  // Add caching headers for public profiles
  if (req.nextUrl.pathname.match(/^\/[a-zA-Z0-9_-]+$/) && 
      !req.nextUrl.pathname.startsWith('/api') &&
      !req.nextUrl.pathname.startsWith('/auth') &&
      !req.nextUrl.pathname.startsWith('/dashboard')) {
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400')
  }
  
  // Add security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  
  return response
})

export const config = {
  // https://nextjs.org/docs/app/building-your-application/routing/middleware#matcher
  matcher: ['/((?!api|_next/static|_next/image|.*\\.png$).*)'],
}