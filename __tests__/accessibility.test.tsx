/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent } from '@testing-library/react'
import { SkipLinks } from '@/components/ui/skip-links'
import { PublicProfilePage } from '@/components/profile/public-profile-page'
import { DashboardNav } from '@/components/dashboard/dashboard-nav'
import { LinkForm } from '@/components/dashboard/link-form'

// Mock the analytics action to avoid database dependencies
jest.mock('@/lib/actions/analytics', () => ({
  trackLinkClickClient: jest.fn().mockResolvedValue(undefined)
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/dashboard',
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}))

// Mock link actions
jest.mock('@/lib/actions/links', () => ({
  createLink: jest.fn().mockResolvedValue({ success: true }),
  updateLink: jest.fn().mockResolvedValue({ success: true })
}))

// Mock profile data
const mockProfile = {
  id: '1',
  userId: '1',
  slug: 'testuser',
  theme: {
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    fontFamily: 'Inter',
    preset: 'modern'
  },
  backgroundType: 'color' as const,
  backgroundValue: '#ffffff',
  isPublic: true,
  viewCount: 100,
  user: {
    id: '1',
    displayName: 'Test User',
    bio: 'This is a test bio',
    profileImage: null,
    username: 'testuser'
  },
  links: [
    {
      id: '1',
      profileId: '1',
      title: 'My Website',
      url: 'https://example.com',
      icon: 'globe',
      isVisible: true,
      order: 1,
      clickCount: 10,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
}

describe('Accessibility Tests', () => {
  describe('Skip Links', () => {
    it('should render skip links with proper accessibility attributes', () => {
      render(<SkipLinks />)
      
      const skipNav = screen.getByRole('navigation', { name: /skip navigation/i })
      expect(skipNav).toBeInTheDocument()
      
      const skipToMain = screen.getByRole('link', { name: /skip to main content/i })
      expect(skipToMain).toBeInTheDocument()
      expect(skipToMain).toHaveAttribute('href', '#main-content')
    })

    it('should be keyboard accessible', () => {
      render(<SkipLinks />)
      
      const skipToMain = screen.getByRole('link', { name: /skip to main content/i })
      
      // Should be focusable
      skipToMain.focus()
      expect(document.activeElement).toBe(skipToMain)
    })
  })

  // Simplified tests focusing on core accessibility features without complex component dependencies

  describe('Mobile Responsiveness', () => {
    beforeEach(() => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      })
    })

    it('should have proper touch target sizes', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const linkButton = screen.getByRole('button', { name: /visit my website/i })
      
      // Check minimum touch target size (44px)
      expect(linkButton).toHaveClass('min-h-[44px]')
    })

    it('should have responsive text sizing', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const heading = screen.getByRole('heading', { level: 1 })
      expect(heading).toHaveClass('text-xl', 'sm:text-2xl')
    })
  })

  describe('Keyboard Navigation', () => {
    it('should support keyboard navigation in navigation menu', () => {
      // Mock window.innerWidth for desktop view
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      })
      
      render(<DashboardNav />)
      
      const menuItems = screen.getAllByRole('menuitem')
      
      // On desktop, menu items should be focusable
      menuItems.forEach(item => {
        expect(item).toHaveAttribute('tabIndex')
        // The tabIndex might be 0 or -1 depending on mobile menu state
        const tabIndex = item.getAttribute('tabIndex')
        expect(['0', '-1']).toContain(tabIndex)
      })
    })

    it('should support Enter and Space key activation', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const linkButton = screen.getByRole('button', { name: /visit my website/i })
      
      // Test Enter key
      linkButton.focus()
      linkButton.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter' }))
      
      // Test Space key
      linkButton.dispatchEvent(new KeyboardEvent('keydown', { key: ' ' }))
    })
  })

  describe('Screen Reader Support', () => {
    it('should provide proper ARIA labels and descriptions', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const linkButton = screen.getByRole('button', { name: /visit my website/i })
      expect(linkButton).toHaveAttribute('aria-label')
      expect(linkButton).toHaveAttribute('aria-describedby')
      
      const linkDescription = document.getElementById(linkButton.getAttribute('aria-describedby')!)
      expect(linkDescription).toBeInTheDocument()
    })

    it('should have proper live regions for dynamic content', () => {
      render(<LinkForm mode="create" />)
      
      // Check that the trigger button is accessible
      const triggerButton = screen.getByRole('button', { name: /add link/i })
      expect(triggerButton).toBeInTheDocument()
      expect(triggerButton).toHaveAttribute('aria-haspopup', 'dialog')
      expect(triggerButton).toHaveAttribute('aria-expanded', 'false')
      
      // The live regions are added when form submission occurs
      // This test verifies the component structure is accessible
    })

    it('should have proper heading hierarchy', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const h1 = screen.getByRole('heading', { level: 1 })
      expect(h1).toBeInTheDocument()
      expect(h1).toHaveTextContent('Test User')
    })

    it('should provide proper navigation landmarks', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      const main = screen.getByRole('main')
      expect(main).toBeInTheDocument()
      expect(main).toHaveAttribute('id', 'main-content')
      
      const navigation = screen.getByRole('navigation', { name: /test user's links/i })
      expect(navigation).toBeInTheDocument()
    })
  })

  describe('WCAG 2.1 AA Compliance', () => {
    it('should have sufficient color contrast', () => {
      // This would typically be tested with automated tools like axe-core
      // For now, we'll check that our theme colors are applied
      render(<PublicProfilePage profile={mockProfile} />)
      
      const linkButton = screen.getByRole('button', { name: /visit my website/i })
      expect(linkButton).toHaveStyle({
        backgroundColor: '#3b82f6',
        color: '#ffffff'
      })
    })

    it('should support keyboard-only navigation', () => {
      render(<DashboardNav />)
      
      const menuItems = screen.getAllByRole('menuitem')
      
      // All menu items should be keyboard accessible
      menuItems.forEach(item => {
        expect(item).toHaveAttribute('tabIndex')
      })
    })

    it('should provide alternative text for images', () => {
      render(<PublicProfilePage profile={mockProfile} />)
      
      // Check that avatar fallback has proper aria-label
      const avatar = screen.getByLabelText(/profile picture/i)
      expect(avatar).toBeInTheDocument()
    })
  })
})