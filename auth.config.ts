import type { NextAuthConfig } from 'next-auth'
import Google from 'next-auth/providers/google'
import GitHub from 'next-auth/providers/github'
import Credentials from 'next-auth/providers/credentials'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
})

export default {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID,
      clientSecret: process.env.GITHUB_CLIENT_SECRET,
    }),
    Credentials({
      async authorize(credentials) {
        const parsedCredentials = loginSchema.safeParse(credentials)

        if (parsedCredentials.success) {
          const { email, password } = parsedCredentials.data
          
          try {
            const user = await prisma.user.findUnique({
              where: { email },
            })

            if (user && user.password) {
              const passwordsMatch = await bcrypt.compare(password, user.password)
              
              if (passwordsMatch) {
                return {
                  id: user.id,
                  email: user.email,
                  name: user.displayName,
                  image: user.profileImage,
                }
              }
            }
          } catch (error) {
            console.error('Authentication error:', error)
          }
        }

        return null
      },
    }),
  ],
  pages: {
    signIn: '/auth/signin',
  },
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user
      const isOnDashboard = nextUrl.pathname.startsWith('/dashboard')
      const isOnAuth = nextUrl.pathname.startsWith('/auth')
      
      if (isOnDashboard) {
        if (isLoggedIn) return true
        return false // Redirect unauthenticated users to login page
      } else if (isLoggedIn && isOnAuth) {
        return Response.redirect(new URL('/dashboard', nextUrl))
      }
      
      return true
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'credentials') {
        return true
      }

      // Handle OAuth providers (Google, GitHub)
      if (account?.provider === 'google' || account?.provider === 'github') {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
          })

          if (!existingUser) {
            // Create new user and profile for OAuth users
            const username = user.email!.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '')
            let uniqueUsername = username
            let counter = 1

            // Ensure username is unique
            while (await prisma.user.findUnique({ where: { username: uniqueUsername } })) {
              uniqueUsername = `${username}${counter}`
              counter++
            }

            const newUser = await prisma.user.create({
              data: {
                email: user.email!,
                username: uniqueUsername,
                displayName: user.name || uniqueUsername,
                profileImage: user.image,
                profile: {
                  create: {
                    slug: uniqueUsername,
                    theme: {
                      primaryColor: '#3b82f6',
                      secondaryColor: '#1e40af',
                      backgroundColor: '#ffffff',
                      textColor: '#1f2937',
                      fontFamily: 'Inter',
                    },
                  },
                },
              },
            })

            user.id = newUser.id
          }
          return true
        } catch (error) {
          console.error('OAuth sign-in error:', error)
          return false
        }
      }

      return true
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token.id) {
        session.user.id = token.id as string
      }
      return session
    },
  },
} satisfies NextAuthConfig