import { auth } from '@/auth'
import { UserRepository } from '@/lib/repositories/user'
import { ProfileEditor } from '@/components/dashboard/profile-editor'
import { PageHeader } from '@/components/ui/page-header'
import { redirect } from 'next/navigation'

export default async function ProfilePage() {
  const session = await auth()
  
  if (!session?.user?.id) {
    redirect('/auth/signin')
  }

  const user = await UserRepository.findByIdOrThrow(session.user.id)

  return (
    <div className="space-y-6">
      <PageHeader
        title="Profile Settings"
        description="Manage your profile information and appearance."
      />
      
      <ProfileEditor user={user} />
    </div>
  )
}