import { auth } from '@/auth'
import { UserRepository } from '@/lib/repositories/user'
import { PageHeader } from '@/components/ui/page-header'
import { DynamicThemeCustomizer } from '@/components/dashboard/theme-customizer-dynamic'
import { redirect } from 'next/navigation'

export default async function CustomizePage() {
  const session = await auth()
  
  if (!session?.user?.id) {
    redirect('/auth/signin')
  }

  // Get user data for preview
  const user = await UserRepository.findById(session.user.id)
  
  if (!user) {
    redirect('/auth/signin')
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Customize Theme"
        description="Personalize your profile with colors, fonts, and backgrounds"
      />

      <DynamicThemeCustomizer 
        user={{
          displayName: user.displayName,
          bio: user.bio,
          profileImage: user.profileImage,
        }}
      />
    </div>
  )
}