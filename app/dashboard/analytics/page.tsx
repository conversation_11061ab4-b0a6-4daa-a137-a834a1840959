"use client"

import { useState, useEffect, useCallback } from 'react'
import { PageHeader } from '@/components/ui/page-header'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DynamicAnalyticsChart } from '@/components/dashboard/analytics-chart-dynamic'
import { AnalyticsErrorBoundary } from '@/components/dashboard/analytics-error-boundary'
import { analyticsLogger } from '@/lib/utils/analytics-logger'
import { AnalyticsErrorFactory } from '@/lib/errors/analytics-errors'
import { 
  BarChart3, 
  Eye, 
  MousePointer, 
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalViews: number
    totalClicks: number
    clickRate: number
    periodViews: number
    periodClicks: number
  }
  charts: {
    viewsByDay: Array<{ date: string; count: number }>
    clicksByDay: Array<{ date: string; count: number }>
  }
  linkPerformance: Array<{
    id: string
    title: string
    url: string
    totalClicks: number
    periodClicks: number
    isVisible: boolean
  }>
  topReferrers: Array<{
    referrer: string
    count: number
  }>
}

export default function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [period, setPeriod] = useState('30')

  const fetchAnalytics = useCallback(async () => {
    const startTime = Date.now()
    
    try {
      setLoading(true)
      setError(null)
      
      analyticsLogger.info('Fetching analytics data', { period })
      
      const response = await fetch(`/api/analytics/dashboard?period=${period}`)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || `HTTP ${response.status}: ${response.statusText}`
        const errorCode = errorData.code || 'FETCH_ERROR'
        
        analyticsLogger.error('Analytics API request failed', {
          period,
          status: response.status,
          statusText: response.statusText,
          errorCode,
          duration: Date.now() - startTime
        })
        
        throw AnalyticsErrorFactory.networkError(new Error(errorMessage))
      }
      
      const analyticsData = await response.json()
      
      // Validate response structure
      if (!analyticsData || typeof analyticsData !== 'object') {
        throw AnalyticsErrorFactory.dataValidationError('response', analyticsData, 'object')
      }
      
      const requiredFields = ['overview', 'charts', 'linkPerformance', 'topReferrers']
      const missingFields = requiredFields.filter(field => !(field in analyticsData))
      
      if (missingFields.length > 0) {
        throw AnalyticsErrorFactory.dataValidationError('response structure', missingFields, 'complete analytics data')
      }
      
      setData(analyticsData)
      
      analyticsLogger.info('Analytics data fetched successfully', {
        period,
        duration: Date.now() - startTime,
        dataSize: JSON.stringify(analyticsData).length,
        totalViews: analyticsData.overview?.totalViews,
        totalClicks: analyticsData.overview?.totalClicks
      })
      
    } catch (err) {
      const duration = Date.now() - startTime
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics'
      
      analyticsLogger.error('Failed to fetch analytics data', {
        period,
        duration,
        error: errorMessage
      })
      
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [period])

  useEffect(() => {
    fetchAnalytics()
  }, [fetchAnalytics])

  const handleExportData = async () => {
    if (!data) {
      analyticsLogger.warn('Export attempted with no data available')
      return
    }
    
    try {
      analyticsLogger.info('Starting analytics data export', { period })
      
      const exportData = {
        exportDate: new Date().toISOString(),
        period: `${period} days`,
        ...data
      }
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `analytics-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      analyticsLogger.info('Analytics data export completed', {
        period,
        fileSize: blob.size,
        fileName: a.download
      })
      
    } catch (error) {
      analyticsLogger.error('Failed to export analytics data', { period }, error as Error)
      alert('Failed to export data. Please try again.')
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
    return num.toString()
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Analytics"
        description="Track your profile performance and engagement"
      >
        <div className="flex space-x-2">
          <Button onClick={fetchAnalytics} variant="outline" size="sm" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {data && (
            <Button onClick={handleExportData} variant="outline" size="sm" disabled={loading}>
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
          )}
        </div>
      </PageHeader>

      {/* Time Period Selector */}
      <Card>
        <CardHeader>
          <CardTitle>Time Period</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {[
              { value: '7', label: 'Last 7 days' },
              { value: '30', label: 'Last 30 days' },
              { value: '90', label: 'Last 90 days' },
              { value: '365', label: 'Last year' }
            ].map((option) => (
              <Badge
                key={option.value}
                variant={period === option.value ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => !loading && setPeriod(option.value)}
              >
                {option.label}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Overview Stats */}
      {data && !loading && !error && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Views</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(data.overview.totalViews)}
                  </p>
                </div>
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Eye className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-500">
                  {data.overview.periodViews} in last {period} days
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(data.overview.totalClicks)}
                  </p>
                </div>
                <div className="p-2 bg-green-100 rounded-lg">
                  <MousePointer className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-500">
                  {data.overview.periodClicks} in last {period} days
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Click Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.overview.clickRate}%
                  </p>
                </div>
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-500">
                  Clicks per view
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Links</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {data.linkPerformance.filter(link => link.isVisible).length}
                  </p>
                </div>
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-500">
                  of {data.linkPerformance.length} total links
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <AnalyticsErrorBoundary chartType="Analytics Dashboard">
        <DynamicAnalyticsChart
          viewsData={data?.charts.viewsByDay || []}
          clicksData={data?.charts.clicksByDay || []}
          linkPerformance={data?.linkPerformance || []}
          topReferrers={data?.topReferrers || []}
          period={period}
          isLoading={loading}
          error={error || undefined}
          onRetry={fetchAnalytics}
        />
      </AnalyticsErrorBoundary>
    </div>
  )
}