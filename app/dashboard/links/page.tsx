'use client'

import { useEffect, useState } from 'react'
import { PageHeader } from '@/components/ui/page-header'
import { Card, CardContent } from '@/components/ui/card'
import { EmptyState } from '@/components/ui/empty-state'
import { LinkForm } from '@/components/dashboard/link-form'
import { LinkList } from '@/components/dashboard/link-list'
import { getUserLinks } from '@/lib/actions/links'
import { Link as LinkIcon } from 'lucide-react'
import { toast } from 'sonner'

interface Link {
  id: string
  title: string
  url: string
  icon?: string | null
  isVisible: boolean
  order: number
  clickCount: number
}

export default function LinksPage() {
  const [links, setLinks] = useState<Link[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const fetchLinks = async () => {
    try {
      const result = await getUserLinks()
      if (result.success) {
        setLinks(result.data || [])
      } else {
        toast.error(result.error || 'Failed to load links')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchLinks()
  }, [])

  const handleUpdate = () => {
    fetchLinks()
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <PageHeader
          title="Manage Links"
          description="Add, edit, and organize your social media links"
        />
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Loading...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Manage Links"
        description="Add, edit, and organize your social media links"
      >
        <LinkForm mode="create" onSuccess={handleUpdate} />
      </PageHeader>

      {links.length === 0 ? (
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-center text-center p-8 space-y-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <LinkIcon className="h-6 w-6" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">No links yet</h3>
                <p className="text-sm text-muted-foreground max-w-sm">
                  Start building your profile by adding your first link
                </p>
              </div>
              <LinkForm mode="create" onSuccess={handleUpdate} />
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <LinkList links={links} onUpdate={handleUpdate} />
        </div>
      )}
    </div>
  )
}