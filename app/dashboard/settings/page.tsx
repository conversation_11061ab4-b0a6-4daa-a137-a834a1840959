import { Suspense } from 'react'
import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import { UserRepository } from '@/lib/repositories/user'
import { SettingsForm } from '@/components/dashboard/settings-form'
import { PageHeader } from '@/components/ui/page-header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default async function SettingsPage() {
  const session = await auth()
  
  if (!session?.user?.id) {
    redirect('/auth/signin')
  }

  const user = await UserRepository.findById(session.user.id)
  
  if (!user) {
    redirect('/auth/signin')
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Account Settings"
        description="Manage your account settings and preferences"
      />
      
      <Suspense fallback={<LoadingSpinner />}>
        <SettingsForm user={user} />
      </Suspense>
    </div>
  )
}