import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { ProfileRepository } from '@/lib/repositories/profile'
import { PublicProfilePage } from '@/components/profile/public-profile-page'
import { ProfileViewTracker } from '@/components/profile/profile-view-tracker'

// Enable static generation for public profiles
export const dynamic = 'force-static'
export const revalidate = 3600 // Revalidate every hour

interface PublicProfilePageProps {
  params: Promise<{ username: string }>
}

export async function generateMetadata({ params }: PublicProfilePageProps): Promise<Metadata> {
  const { username } = await params

  const profile = await ProfileRepository.findBySlugWithUser(username)

  if (!profile) {
    return {
      title: 'Profile Not Found',
      description: 'The requested profile could not be found.',
    }
  }

  const title = `${profile.user.displayName} | LinksInBio`
  const description = profile.user.bio || `Check out ${profile.user.displayName}'s links`
  const profileUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://linksinbio.com'}/${username}`

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      url: profileUrl,
      siteName: 'LinksInBio',
      images: profile.user.profileImage ? [
        {
          url: profile.user.profileImage,
          width: 400,
          height: 400,
          alt: `${profile.user.displayName}'s profile picture`,
        }
      ] : [],
      locale: 'en_US',
      type: 'profile',
    },
    twitter: {
      card: 'summary',
      title,
      description,
      images: profile.user.profileImage ? [profile.user.profileImage] : [],
    },
    alternates: {
      canonical: profileUrl,
    },
  }
}

// Generate static params for popular profiles
export async function generateStaticParams() {
  try {
    // Get the most viewed profiles for static generation
    const profiles = await ProfileRepository.getMostViewed(50)
    return profiles.map((profile) => ({
      username: profile.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function PublicProfile({ params }: PublicProfilePageProps) {
  const { username } = await params

  const profile = await ProfileRepository.findBySlugWithLinks(username)

  if (!profile) {
    notFound()
  }

  return (
    <>
      <ProfileViewTracker profileId={profile.id} />
      <PublicProfilePage profile={profile} />
    </>
  )
}