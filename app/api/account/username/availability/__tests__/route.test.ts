/**
 * Integration tests for username availability API route
 * Tests the enhanced functionality including timeout handling, rate limiting,
 * error handling, and response caching.
 */

import { usernameSchema } from '@/lib/validations'

// Test the error codes constant
describe('Username Availability API Route - Error Codes', () => {
  const ERROR_CODES = {
    UNAUTHORIZED: 'UNAUTHORIZED',
    INVALID_FORMAT: 'INVALID_FORMAT',
    RATE_LIMITED: 'RATE_LIMITED',
    TIMEOUT: 'TIMEOUT',
    SERVER_ERROR: 'SERVER_ERROR',
    MISSING_PARAMETER: 'MISSING_PARAMETER'
  } as const

  it('should have all required error codes defined', () => {
    expect(ERROR_CODES.UNAUTHORIZED).toBe('UNAUTHORIZED')
    expect(ERROR_CODES.INVALID_FORMAT).toBe('INVALID_FORMAT')
    expect(ERROR_CODES.RATE_LIMITED).toBe('RATE_LIMITED')
    expect(ERROR_CODES.TIMEOUT).toBe('TIMEOUT')
    expect(ERROR_CODES.SERVER_ERROR).toBe('SERVER_ERROR')
    expect(ERROR_CODES.MISSING_PARAMETER).toBe('MISSING_PARAMETER')
  })
})

// Test the username validation logic
describe('Username Availability API Route - Validation Logic', () => {
  describe('Username format validation', () => {
    it('should accept valid usernames', () => {
      const validUsernames = [
        'user123',
        'test_user',
        'my-username',
        'user_123',
        'test-123',
        'a'.repeat(30) // max length
      ]

      validUsernames.forEach(username => {
        expect(() => usernameSchema.parse(username)).not.toThrow()
      })
    })

    it('should reject usernames that are too short', () => {
      expect(() => usernameSchema.parse('ab')).toThrow('Username must be at least 3 characters')
    })

    it('should reject usernames that are too long', () => {
      const longUsername = 'a'.repeat(31)
      expect(() => usernameSchema.parse(longUsername)).toThrow('Username must be less than 30 characters')
    })

    it('should reject usernames with invalid characters', () => {
      const invalidUsernames = [
        'user@name',
        'user.name',
        'user name',
        'user+name',
        'user#name'
      ]

      invalidUsernames.forEach(username => {
        expect(() => usernameSchema.parse(username)).toThrow('Username can only contain letters, numbers, hyphens, and underscores')
      })
    })

    it('should reject usernames starting with special characters', () => {
      const invalidUsernames = [
        '-username',
        '_username'
      ]

      invalidUsernames.forEach(username => {
        expect(() => usernameSchema.parse(username)).toThrow('Username cannot start with special characters')
      })
    })

    it('should reject usernames ending with special characters', () => {
      const invalidUsernames = [
        'username-',
        'username_'
      ]

      invalidUsernames.forEach(username => {
        expect(() => usernameSchema.parse(username)).toThrow('Username cannot end with special characters')
      })
    })

    it('should reject usernames with consecutive special characters', () => {
      const invalidUsernames = [
        'user--name',
        'user__name',
        'user-_name',
        'user_-name'
      ]

      invalidUsernames.forEach(username => {
        expect(() => usernameSchema.parse(username)).toThrow('Username cannot contain consecutive special characters')
      })
    })
  })
})

// Test rate limiting logic
describe('Username Availability API Route - Rate Limiting Logic', () => {
  // Mock rate limiting functionality
  const rateLimitMap = new Map<string, { count: number; resetTime: number }>()
  const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
  const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute per user

  function checkRateLimit(userId: string): { allowed: boolean; retryAfter?: number } {
    const now = Date.now()
    const userLimit = rateLimitMap.get(userId)

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize rate limit
      rateLimitMap.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
      return { allowed: true }
    }

    if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
      const retryAfter = Math.ceil((userLimit.resetTime - now) / 1000)
      return { allowed: false, retryAfter }
    }

    // Increment count
    userLimit.count++
    return { allowed: true }
  }

  beforeEach(() => {
    rateLimitMap.clear()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should allow requests within rate limit', () => {
    const userId = 'test-user-1'
    
    // Make multiple requests within limit
    for (let i = 0; i < 29; i++) {
      const result = checkRateLimit(userId)
      expect(result.allowed).toBe(true)
    }
  })

  it('should block requests when rate limit is exceeded', () => {
    const userId = 'test-user-2'
    
    // Make requests to reach limit
    for (let i = 0; i < 30; i++) {
      checkRateLimit(userId)
    }
    
    // This request should be blocked
    const result = checkRateLimit(userId)
    expect(result.allowed).toBe(false)
    expect(result.retryAfter).toBeGreaterThan(0)
  })

  it('should reset rate limit after time window', () => {
    const userId = 'test-user-3'
    
    // Exceed rate limit
    for (let i = 0; i < 31; i++) {
      checkRateLimit(userId)
    }
    
    // Advance time by 1 minute + 1 second
    jest.advanceTimersByTime(61 * 1000)
    
    // Should be allowed again
    const result = checkRateLimit(userId)
    expect(result.allowed).toBe(true)
  })

  it('should track rate limits per user independently', () => {
    const user1 = 'test-user-4'
    const user2 = 'test-user-5'
    
    // User 1 exceeds limit
    for (let i = 0; i < 31; i++) {
      checkRateLimit(user1)
    }
    
    // User 1 should be blocked
    const user1Result = checkRateLimit(user1)
    expect(user1Result.allowed).toBe(false)
    
    // User 2 should still be allowed
    const user2Result = checkRateLimit(user2)
    expect(user2Result.allowed).toBe(true)
  })
})

// Test timeout functionality
describe('Username Availability API Route - Timeout Logic', () => {
  function createTimeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), ms)
    })
  }

  beforeEach(() => {
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should timeout after specified duration', async () => {
    const timeoutPromise = createTimeoutPromise(5000)
    
    // Advance time to trigger timeout
    jest.advanceTimersByTime(5001)
    
    await expect(timeoutPromise).rejects.toThrow('Request timeout')
  })

  it('should not timeout if resolved before duration', async () => {
    const quickPromise = new Promise(resolve => {
      setTimeout(() => resolve('success'), 1000)
    })
    
    const timeoutPromise = createTimeoutPromise(5000)
    
    // Advance time but stay within timeout
    jest.advanceTimersByTime(1001)
    
    const result = await Promise.race([quickPromise, timeoutPromise])
    expect(result).toBe('success')
  })
})

// Test response format interfaces
describe('Username Availability API Route - Response Interfaces', () => {
  interface UsernameAvailabilityResponse {
    username: string
    available: boolean
    cached?: boolean
  }

  interface UsernameAvailabilityError {
    error: string
    code: string
    details?: string[]
    retryAfter?: number
  }

  it('should have correct success response format', () => {
    const response: UsernameAvailabilityResponse = {
      username: 'testuser',
      available: true,
      cached: false
    }

    expect(response.username).toBe('testuser')
    expect(response.available).toBe(true)
    expect(response.cached).toBe(false)
  })

  it('should have correct error response format', () => {
    const errorResponse: UsernameAvailabilityError = {
      error: 'Invalid username format',
      code: 'INVALID_FORMAT',
      details: ['Username must be at least 3 characters'],
      retryAfter: 60
    }

    expect(errorResponse.error).toBe('Invalid username format')
    expect(errorResponse.code).toBe('INVALID_FORMAT')
    expect(errorResponse.details).toEqual(['Username must be at least 3 characters'])
    expect(errorResponse.retryAfter).toBe(60)
  })
})

// Test logging functionality
describe('Username Availability API Route - Logging Logic', () => {
  let consoleSpy: jest.SpyInstance

  beforeEach(() => {
    consoleSpy = jest.spyOn(console, 'log').mockImplementation()
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  function logRequest(
    userId: string,
    username: string,
    duration: number,
    success: boolean,
    error?: string
  ) {
    const logData = {
      timestamp: new Date().toISOString(),
      userId,
      username,
      duration,
      success,
      error,
      endpoint: '/api/account/username/availability'
    }
    
    console.log('Username availability check:', JSON.stringify(logData))
  }

  it('should log successful requests with correct format', () => {
    logRequest('user123', 'testuser', 150, true)

    expect(consoleSpy).toHaveBeenCalledTimes(1)
    
    const logCall = consoleSpy.mock.calls[0]
    expect(logCall[0]).toBe('Username availability check:')
    
    const logData = JSON.parse(logCall[1])
    expect(logData.userId).toBe('user123')
    expect(logData.username).toBe('testuser')
    expect(logData.duration).toBe(150)
    expect(logData.success).toBe(true)
    expect(logData.endpoint).toBe('/api/account/username/availability')
  })

  it('should log failed requests with error details', () => {
    logRequest('user123', 'testuser', 200, false, 'Database error')

    expect(consoleSpy).toHaveBeenCalledTimes(1)
    
    const logCall = consoleSpy.mock.calls[0]
    const logData = JSON.parse(logCall[1])
    
    expect(logData.success).toBe(false)
    expect(logData.error).toBe('Database error')
  })
})

// Test cache header generation
describe('Username Availability API Route - Cache Headers', () => {
  it('should generate correct cache headers for successful responses', () => {
    const username = 'testuser'
    const available = true
    const timestamp = Math.floor(Date.now() / 30000)
    
    const expectedHeaders = {
      'Cache-Control': 'private, max-age=30, stale-while-revalidate=60',
      'ETag': `"${username}-${available}-${timestamp}"`,
      'Vary': 'Authorization'
    }

    expect(expectedHeaders['Cache-Control']).toBe('private, max-age=30, stale-while-revalidate=60')
    expect(expectedHeaders['ETag']).toMatch(/^"testuser-(true|false)-\d+"$/)
    expect(expectedHeaders['Vary']).toBe('Authorization')
  })

  it('should generate correct no-cache headers for error responses', () => {
    const errorHeaders = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }

    expect(errorHeaders['Cache-Control']).toBe('no-cache, no-store, must-revalidate')
    expect(errorHeaders['Pragma']).toBe('no-cache')
    expect(errorHeaders['Expires']).toBe('0')
  })
})