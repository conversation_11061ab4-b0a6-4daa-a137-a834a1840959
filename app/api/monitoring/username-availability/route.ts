import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { monitoringDashboard } from '@/lib/utils/monitoring-dashboard'
import { performanceMonitor } from '@/lib/utils/performance-monitor'

/**
 * GET /api/monitoring/username-availability
 * Returns monitoring dashboard data for username availability feature
 * Requires authentication and admin privileges
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // In a real application, you would check for admin privileges here
    // For now, we'll allow any authenticated user to access monitoring data
    // if (!session.user.isAdmin) {
    //   return NextResponse.json(
    //     { error: 'Admin privileges required' },
    //     { status: 403 }
    //   )
    // }

    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'json'

    if (format === 'report') {
      // Return text report
      const report = monitoringDashboard.generateReport()
      return new NextResponse(report, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    if (format === 'health') {
      // Return health check status
      const healthCheck = monitoringDashboard.getHealthCheck()
      return NextResponse.json(healthCheck, {
        status: healthCheck.status === 'healthy' ? 200 : 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    if (format === 'stats') {
      // Return raw statistics
      const stats = performanceMonitor.getStats()
      return NextResponse.json(stats, {
        status: 200,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      })
    }

    // Default: return full dashboard data
    const dashboardData = monitoringDashboard.getDashboardData()
    
    return NextResponse.json(dashboardData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })

  } catch (error) {
    console.error('Monitoring dashboard error:', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/monitoring/username-availability
 * Clears monitoring metrics (useful for testing)
 * Requires authentication and admin privileges
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // In a real application, you would check for admin privileges here
    // For now, we'll allow any authenticated user to clear metrics
    // if (!session.user.isAdmin) {
    //   return NextResponse.json(
    //     { error: 'Admin privileges required' },
    //     { status: 403 }
    //   )
    // }

    // Clear all metrics
    performanceMonitor.clearMetrics()
    
    return NextResponse.json(
      { message: 'Monitoring metrics cleared successfully' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Clear monitoring metrics error:', error)
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}