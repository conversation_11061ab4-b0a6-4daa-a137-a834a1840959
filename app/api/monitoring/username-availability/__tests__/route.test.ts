/**
 * @jest-environment node
 */
import { NextRequest } from 'next/server'
import { GET, DELETE } from '../route'
import { auth } from '@/auth'
import { performanceMonitor } from '@/lib/utils/performance-monitor'
import { monitoringDashboard } from '@/lib/utils/monitoring-dashboard'

// Mock global Request if not available
if (typeof global.Request === 'undefined') {
  global.Request = class Request {
    constructor(public url: string, public init?: any) {}
  } as any
}

// Mock the auth function
jest.mock('@/auth', () => ({
  auth: jest.fn()
}))

// Mock the monitoring utilities
jest.mock('@/lib/utils/performance-monitor', () => ({
  performanceMonitor: {
    getStats: jest.fn(),
    clearMetrics: jest.fn()
  }
}))

jest.mock('@/lib/utils/monitoring-dashboard', () => ({
  monitoringDashboard: {
    getDashboardData: jest.fn(),
    generateReport: jest.fn(),
    getHealthCheck: jest.fn()
  }
}))

const mockAuth = auth as jest.MockedFunction<typeof auth>
const mockPerformanceMonitor = performanceMonitor as jest.Mocked<typeof performanceMonitor>
const mockMonitoringDashboard = monitoringDashboard as jest.Mocked<typeof monitoringDashboard>

describe('/api/monitoring/username-availability', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET', () => {
    it('should require authentication', async () => {
      mockAuth.mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability')
      const response = await GET(request)

      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Authentication required')
    })

    it('should return dashboard data by default', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      
      const mockDashboardData = {
        summary: { status: 'healthy', uptime: '1h 30m', lastUpdated: '2023-01-01T00:00:00Z' },
        performance: { averageResponseTime: 500, successRate: 95 },
        cache: { hitRate: 80, efficiency: 'excellent' },
        errors: { totalErrors: 2, errorRate: 1.5 },
        trends: { responseTimeHistory: [], errorRateHistory: [], cacheHitRateHistory: [] }
      }
      
      mockMonitoringDashboard.getDashboardData.mockReturnValue(mockDashboardData as any)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability')
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toEqual(mockDashboardData)
      expect(mockMonitoringDashboard.getDashboardData).toHaveBeenCalled()
    })

    it('should return text report when format=report', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      
      const mockReport = 'Username Availability Monitoring Report\n\nSystem Status: HEALTHY'
      mockMonitoringDashboard.generateReport.mockReturnValue(mockReport)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability?format=report')
      const response = await GET(request)

      expect(response.status).toBe(200)
      expect(response.headers.get('Content-Type')).toBe('text/plain')
      
      const text = await response.text()
      expect(text).toBe(mockReport)
      expect(mockMonitoringDashboard.generateReport).toHaveBeenCalled()
    })

    it('should return health check when format=health', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      
      const mockHealthCheck = {
        status: 'healthy',
        checks: {
          responseTime: true,
          successRate: true,
          errorRate: true,
          cacheEfficiency: true
        }
      }
      
      mockMonitoringDashboard.getHealthCheck.mockReturnValue(mockHealthCheck)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability?format=health')
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toEqual(mockHealthCheck)
      expect(mockMonitoringDashboard.getHealthCheck).toHaveBeenCalled()
    })

    it('should return 503 for unhealthy status when format=health', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      
      const mockHealthCheck = {
        status: 'unhealthy',
        checks: {
          responseTime: false,
          successRate: false,
          errorRate: true,
          cacheEfficiency: true
        }
      }
      
      mockMonitoringDashboard.getHealthCheck.mockReturnValue(mockHealthCheck)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability?format=health')
      const response = await GET(request)

      expect(response.status).toBe(503)
      const data = await response.json()
      expect(data).toEqual(mockHealthCheck)
    })

    it('should return raw stats when format=stats', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      
      const mockStats = {
        performance: { totalRequests: 100, averageResponseTime: 500, successRate: 95 },
        cache: { totalOperations: 50, hitRate: 80, missRate: 20 },
        errors: { totalErrors: 5, errorsByType: { 'TIMEOUT': 2, 'NETWORK': 3 }, errorRate: 5 }
      }
      
      mockPerformanceMonitor.getStats.mockReturnValue(mockStats as any)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability?format=stats')
      const response = await GET(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data).toEqual(mockStats)
      expect(mockPerformanceMonitor.getStats).toHaveBeenCalled()
    })

    it('should handle errors gracefully', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      mockMonitoringDashboard.getDashboardData.mockImplementation(() => {
        throw new Error('Test error')
      })

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      const request = new NextRequest('http://localhost/api/monitoring/username-availability')
      const response = await GET(request)

      expect(response.status).toBe(500)
      const data = await response.json()
      expect(data.error).toBe('Internal server error')
      expect(consoleSpy).toHaveBeenCalledWith('Monitoring dashboard error:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })

  describe('DELETE', () => {
    it('should require authentication', async () => {
      mockAuth.mockResolvedValue(null)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability', {
        method: 'DELETE'
      })
      const response = await DELETE(request)

      expect(response.status).toBe(401)
      const data = await response.json()
      expect(data.error).toBe('Authentication required')
    })

    it('should clear metrics successfully', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability', {
        method: 'DELETE'
      })
      const response = await DELETE(request)

      expect(response.status).toBe(200)
      const data = await response.json()
      expect(data.message).toBe('Monitoring metrics cleared successfully')
      expect(mockPerformanceMonitor.clearMetrics).toHaveBeenCalled()
    })

    it('should handle errors gracefully', async () => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
      mockPerformanceMonitor.clearMetrics.mockImplementation(() => {
        throw new Error('Test error')
      })

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      const request = new NextRequest('http://localhost/api/monitoring/username-availability', {
        method: 'DELETE'
      })
      const response = await DELETE(request)

      expect(response.status).toBe(500)
      const data = await response.json()
      expect(data.error).toBe('Internal server error')
      expect(consoleSpy).toHaveBeenCalledWith('Clear monitoring metrics error:', expect.any(Error))

      consoleSpy.mockRestore()
    })
  })

  describe('response headers', () => {
    beforeEach(() => {
      mockAuth.mockResolvedValue({ user: { id: 'user123' } } as any)
    })

    it('should set no-cache headers for all responses', async () => {
      mockMonitoringDashboard.getDashboardData.mockReturnValue({} as any)

      const request = new NextRequest('http://localhost/api/monitoring/username-availability')
      const response = await GET(request)

      expect(response.headers.get('Cache-Control')).toBe('no-cache, no-store, must-revalidate')
    })

    it('should set correct content type for report format', async () => {
      mockMonitoringDashboard.generateReport.mockReturnValue('test report')

      const request = new NextRequest('http://localhost/api/monitoring/username-availability?format=report')
      const response = await GET(request)

      expect(response.headers.get('Content-Type')).toBe('text/plain')
    })
  })
})