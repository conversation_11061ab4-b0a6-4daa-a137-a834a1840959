import { NextRequest } from 'next/server'
import { errorLogger, withErrorContext, handleApiError } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'
import { auth } from '@/auth'

interface ErrorReport {
  errorId: string
  message: string
  stack?: string
  componentStack?: string
  level?: string
  context?: string
  url?: string
  userAgent?: string
  timestamp: string
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    const body = await request.json() as ErrorReport

    // Validate required fields
    if (!body.errorId || !body.message) {
      throw ErrorFactory.validationError('errorId and message', 'Both errorId and message are required')
    }

    // Create context for logging
    const context = withErrorContext(request, {
      userId: session?.user?.id,
      errorId: body.errorId,
      reportedLevel: body.level,
      reportedContext: body.context
    })

    // Log the reported error
    errorLogger.error(`Client-reported error: ${body.message}`, context, new Error(body.message))

    // In a production environment, you might want to:
    // 1. Store error reports in a database for analysis
    // 2. Send to external monitoring service (Sentry, DataDog, etc.)
    // 3. Trigger alerts for critical errors
    // 4. Aggregate similar errors for pattern detection

    // Example: Store in database (uncomment if you have an ErrorReport model)
    /*
    await prisma.errorReport.create({
      data: {
        id: body.errorId,
        message: body.message,
        stack: body.stack,
        componentStack: body.componentStack,
        level: body.level,
        context: body.context,
        url: body.url,
        userAgent: body.userAgent,
        userId: session?.user?.id,
        timestamp: new Date(body.timestamp)
      }
    })
    */

    // Example: Send to external monitoring service
    if (process.env.MONITORING_WEBHOOK_URL) {
      try {
        await fetch(process.env.MONITORING_WEBHOOK_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.MONITORING_API_KEY}`
          },
          body: JSON.stringify({
            ...body,
            userId: session?.user?.id,
            environment: process.env.NODE_ENV,
            timestamp: new Date().toISOString()
          })
        })
      } catch (webhookError) {
        errorLogger.warn('Failed to send error to monitoring webhook', context, webhookError as Error)
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Error report received',
        errorId: body.errorId
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

  } catch (error) {
    return handleApiError(error, 'error reporting', withErrorContext(request))
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    // Only allow authenticated users to view error logs
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Only allow in development or for admin users
    if (process.env.NODE_ENV !== 'development') {
      // In production, you might want to check for admin role
      // const user = await getUserById(session.user.id)
      // if (!user?.isAdmin) {
      //   throw ErrorFactory.forbidden()
      // }
      throw ErrorFactory.forbidden({ reason: 'Error logs only available in development' })
    }

    const url = new URL(request.url)
    const level = url.searchParams.get('level') as 'error' | 'warn' | 'info' | 'debug' | null
    const count = parseInt(url.searchParams.get('count') || '50')
    const fingerprint = url.searchParams.get('fingerprint')

    let logs
    if (fingerprint) {
      logs = errorLogger.getLogsByFingerprint(fingerprint)
    } else if (level) {
      logs = errorLogger.getLogsByLevel(level)
    } else {
      logs = errorLogger.getRecentLogs(count)
    }

    return new Response(
      JSON.stringify({
        logs,
        total: logs.length,
        level: errorLogger.getLogLevel()
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

  } catch (error) {
    return handleApiError(error, 'error log retrieval', withErrorContext(request))
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    
    // Only allow authenticated users to clear error logs
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      throw ErrorFactory.forbidden({ reason: 'Error log clearing only available in development' })
    }

    errorLogger.clearLogs()

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Error logs cleared'
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

  } catch (error) {
    return handleApiError(error, 'error log clearing', withErrorContext(request))
  }
}