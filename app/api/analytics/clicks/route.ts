import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { headers } from 'next/headers'
import { AnalyticsRepository } from '@/lib/repositories/analytics'

/**
 * Track a link click
 */
export async function POST(request: NextRequest) {
  try {
    const { linkId } = await request.json()

    if (!linkId) {
      return NextResponse.json(
        { error: 'Link ID is required' },
        { status: 400 }
      )
    }

    // Get the link to find the profile ID
    const link = await db.link.findUnique({
      where: { id: linkId },
      select: { id: true, profileId: true }
    })

    if (!link) {
      return NextResponse.json(
        { error: 'Link not found' },
        { status: 404 }
      )
    }

    // Get request headers for analytics
    const headersList = await headers()
    const userAgent = headersList.get('user-agent') || undefined
    const forwardedFor = headersList.get('x-forwarded-for')
    const realIp = headersList.get('x-real-ip')
    const referrer = headersList.get('referer') || undefined

    // Get IP address (prioritize x-forwarded-for for proxied requests)
    const ip = forwardedFor?.split(',')[0] || realIp || request.ip || 'unknown'

    // Track the click using the repository
    await AnalyticsRepository.trackLinkClick(linkId, link.profileId, {
      userAgent,
      ip: ip !== 'unknown' ? ip : undefined,
      referrer,
      // Note: Country detection would require a GeoIP service
      country: undefined
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to track link click:', error)
    return NextResponse.json(
      { error: 'Failed to track click' },
      { status: 500 }
    )
  }
}