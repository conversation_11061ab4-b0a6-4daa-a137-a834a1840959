import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { headers } from 'next/headers'
import { AnalyticsRepository } from '@/lib/repositories/analytics'

/**
 * Track a link click with enhanced visitor context detection
 */
export async function POST(request: NextRequest) {
  try {
    const { linkId } = await request.json()

    if (!linkId) {
      return NextResponse.json(
        { error: 'Link ID is required' },
        { status: 400 }
      )
    }

    // Get the link to find the profile ID
    const link = await db.link.findUnique({
      where: { id: linkId },
      select: { id: true, profileId: true }
    })

    if (!link) {
      return NextResponse.json(
        { error: 'Link not found' },
        { status: 404 }
      )
    }

    // Get request headers for enhanced analytics
    const headersList = await headers()
    const userAgent = headersList.get('user-agent') || ''
    const referrer = headersList.get('referer') || undefined
    
    // Get current domain for referrer analysis
    const host = headersList.get('host') || 'localhost'
    const currentDomain = host.split(':')[0] // Remove port if present

    // Convert headers to Headers object for visitor context detection
    const requestHeaders = new Headers()
    headersList.forEach((value, key) => {
      requestHeaders.set(key, value)
    })

    // Track the click using enhanced visitor context
    await AnalyticsRepository.trackLinkClick(
      linkId,
      link.profileId,
      userAgent,
      requestHeaders,
      referrer,
      currentDomain
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to track link click:', error)
    return NextResponse.json(
      { error: 'Failed to track click' },
      { status: 500 }
    )
  }
}