import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { db } from '@/lib/db'
import { AnalyticsRepository } from '@/lib/repositories/analytics'
import { AnalyticsErrorFactory, isAnalyticsError } from '@/lib/errors/analytics-errors'
import { analyticsLogger, createTimer } from '@/lib/utils/analytics-logger'
import { 
  validateApiParameters, 
  validateResponseData,
  isValidAnalyticsDashboardResponse 
} from '@/lib/validations/analytics'

export async function GET(request: NextRequest) {
  const timer = createTimer('analyticsApiRequest')
  const { searchParams } = new URL(request.url)
  
  try {
    // Extract and validate API parameters
    const rawParams = {
      period: searchParams.get('period') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined
    }

    analyticsLogger.apiRequest('GET', '/api/analytics/dashboard', undefined, { 
      operation: 'validateApiParameters',
      rawParams 
    })

    // Validate API parameters using comprehensive validation
    let validatedParams
    try {
      validatedParams = validateApiParameters(rawParams)
    } catch (validationError) {
      const duration = timer.end()
      analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 400, duration, {
        operation: 'parameterValidation',
        error: validationError
      })
      
      if (isAnalyticsError(validationError)) {
        return NextResponse.json(
          { 
            error: validationError.getUserMessage(),
            code: validationError.code,
            details: validationError.details
          },
          { status: validationError.statusCode }
        )
      }
      
      return NextResponse.json(
        { error: 'Invalid request parameters' },
        { status: 400 }
      )
    }

    const session = await auth()
    
    if (!session?.user?.id) {
      const error = AnalyticsErrorFactory.unauthorized()
      analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 401, timer.end())
      return NextResponse.json(
        { 
          error: error.getUserMessage(),
          code: error.code 
        },
        { status: 401 }
      )
    }

    const userId = session.user.id
    analyticsLogger.debug('User authenticated', { userId, operation: 'authentication' })

    // Get user's profile
    const profileTimer = createTimer('getProfile')
    const profile = await db.profile.findUnique({
      where: { userId }
    })
    profileTimer.end()

    if (!profile) {
      const error = AnalyticsErrorFactory.profileNotFound(userId)
      analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 404, timer.end(), { 
        userId,
        operation: 'profileLookup'
      })
      return NextResponse.json(
        { 
          error: error.getUserMessage(),
          code: error.code 
        },
        { status: 404 }
      )
    }

    analyticsLogger.debug('Profile found', { 
      userId, 
      profileId: profile.id,
      operation: 'profileLookup'
    })
    
    // Use validated period (default is 30 if not provided)
    const periodDays = validatedParams.period || 30

    analyticsLogger.debug('Parameters validated', { 
      userId, 
      profileId: profile.id, 
      periodDays,
      operation: 'parameterValidation'
    })

    // Get comprehensive analytics data with optimized parallel queries and retry logic
    const rawAnalyticsData = await AnalyticsRepository.getDashboardData(
      profile.id,
      periodDays
    )

    // Validate and sanitize response data structure
    let analyticsData
    try {
      analyticsData = validateResponseData(rawAnalyticsData)
      
      // Additional type guard check for extra safety
      if (!isValidAnalyticsDashboardResponse(analyticsData)) {
        throw AnalyticsErrorFactory.dataValidationError(
          'response structure validation',
          analyticsData,
          'valid analytics dashboard response'
        )
      }
    } catch (validationError) {
      const duration = timer.end()
      analyticsLogger.error('Analytics data validation failed', { 
        userId, 
        profileId: profile.id,
        operation: 'responseValidation',
        dataType: typeof rawAnalyticsData
      }, validationError as Error)
      
      analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 500, duration, { 
        userId, 
        profileId: profile.id,
        operation: 'responseValidation'
      })
      
      if (isAnalyticsError(validationError)) {
        return NextResponse.json(
          { 
            error: validationError.getUserMessage(),
            code: validationError.code,
            details: validationError.details
          },
          { status: validationError.statusCode }
        )
      }
      
      return NextResponse.json(
        { 
          error: 'Invalid analytics data structure',
          code: 'DATA_VALIDATION_ERROR'
        },
        { status: 500 }
      )
    }

    const duration = timer.end()
    analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 200, duration, { 
      userId, 
      profileId: profile.id,
      periodDays,
      dataSize: JSON.stringify(analyticsData).length,
      operation: 'success'
    })

    return NextResponse.json(analyticsData)
  } catch (error) {
    const duration = timer.end()
    
    if (isAnalyticsError(error)) {
      analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', error.statusCode, duration)
      return NextResponse.json(
        { 
          error: error.getUserMessage(),
          code: error.code,
          details: error.details
        },
        { status: error.statusCode }
      )
    }

    // Handle unexpected errors
    const analyticsError = AnalyticsErrorFactory.dataProcessingError('API request', error as Error)
    analyticsLogger.error('Unexpected error in analytics API', undefined, analyticsError)
    analyticsLogger.apiResponse('GET', '/api/analytics/dashboard', 500, duration)
    
    return NextResponse.json(
      { 
        error: analyticsError.getUserMessage(),
        code: analyticsError.code
      },
      { status: 500 }
    )
  }
}