import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's profile
    const profile = await db.profile.findUnique({
      where: { userId: session.user.id },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            username: true,
            displayName: true,
            bio: true,
            createdAt: true
          }
        },
        links: {
          select: {
            id: true,
            title: true,
            url: true,
            icon: true,
            isVisible: true,
            order: true,
            clickCount: true,
            createdAt: true,
            updatedAt: true
          }
        }
      }
    })

    if (!profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeAnalytics = searchParams.get('analytics') === 'true'

    // Base export data
    const exportData = {
      exportDate: new Date().toISOString(),
      user: profile.user,
      profile: {
        id: profile.id,
        slug: profile.slug,
        theme: profile.theme,
        backgroundType: profile.backgroundType,
        backgroundValue: profile.backgroundValue,
        isPublic: profile.isPublic,
        viewCount: profile.viewCount,
        createdAt: profile.createdAt,
        updatedAt: profile.updatedAt
      },
      links: profile.links
    }

    // Add analytics data if requested (privacy-compliant)
    if (includeAnalytics) {
      // Get aggregated analytics data (no personal information)
      const profileViews = await db.profileView.findMany({
        where: { profileId: profile.id },
        select: {
          timestamp: true,
          // Note: We don't export ipHash, userAgent, or other potentially identifying info
          referrer: true,
          country: true
        },
        orderBy: { timestamp: 'desc' }
      })

      const linkClicks = await db.linkClick.findMany({
        where: { profileId: profile.id },
        select: {
          linkId: true,
          timestamp: true,
          // Note: We don't export ipHash, userAgent, or other potentially identifying info
          referrer: true,
          country: true
        },
        orderBy: { timestamp: 'desc' }
      })

      // Aggregate analytics data by day for privacy
      const viewsByDay = aggregateByDay(profileViews)
      const clicksByDay = aggregateByDay(linkClicks)

      // Aggregate referrers (remove specific URLs for privacy)
      const referrerCounts = profileViews.reduce((acc, view) => {
        if (view.referrer) {
          try {
            const domain = new URL(view.referrer).hostname
            acc[domain] = (acc[domain] || 0) + 1
          } catch {
            acc['unknown'] = (acc['unknown'] || 0) + 1
          }
        } else {
          acc['direct'] = (acc['direct'] || 0) + 1
        }
        return acc
      }, {} as Record<string, number>)

      exportData.analytics = {
        summary: {
          totalViews: profile.viewCount,
          totalClicks: profile.links.reduce((sum, link) => sum + link.clickCount, 0),
          dataRange: {
            firstView: profileViews[profileViews.length - 1]?.timestamp || null,
            lastView: profileViews[0]?.timestamp || null
          }
        },
        viewsByDay,
        clicksByDay,
        referrerDomains: referrerCounts,
        note: "This export contains aggregated, privacy-compliant analytics data. Individual visitor information is not included."
      }
    }

    // Set headers for file download
    const headers = new Headers()
    headers.set('Content-Type', 'application/json')
    headers.set('Content-Disposition', `attachment; filename="linkinbio-export-${new Date().toISOString().split('T')[0]}.json"`)

    return new NextResponse(JSON.stringify(exportData, null, 2), {
      headers
    })
  } catch (error) {
    console.error('Failed to export data:', error)
    return NextResponse.json(
      { error: 'Failed to export data' },
      { status: 500 }
    )
  }
}

function aggregateByDay(data: Array<{ timestamp: Date }>): Record<string, number> {
  return data.reduce((acc, item) => {
    const date = new Date(item.timestamp).toISOString().split('T')[0]
    acc[date] = (acc[date] || 0) + 1
    return acc
  }, {} as Record<string, number>)
}