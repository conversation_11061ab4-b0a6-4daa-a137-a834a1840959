import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'
import { GET } from '../dashboard/route'
import { db } from '@/lib/db'
import { auth } from '@/auth'

// Mock the auth function
jest.mock('@/auth', () => ({
  auth: jest.fn()
}))

// Mock the database
jest.mock('@/lib/db', () => ({
  db: {
    profile: {
      findUnique: jest.fn()
    },
    profileView: {
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
      create: jest.fn(),
      deleteMany: jest.fn()
    },
    linkClick: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      deleteMany: jest.fn()
    },
    link: {
      findMany: jest.fn(),
      create: jest.fn(),
      deleteMany: jest.fn()
    },
    user: {
      create: jest.fn(),
      deleteMany: jest.fn()
    }
  },
  handleDatabaseError: jest.fn()
}))

// Mock the logger
jest.mock('@/lib/utils/analytics-logger', () => ({
  analyticsLogger: {
    apiRequest: jest.fn(),
    apiResponse: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    operationStart: jest.fn(),
    operationComplete: jest.fn(),
    operationFailed: jest.fn()
  },
  createTimer: jest.fn(() => ({
    end: jest.fn(() => 100)
  }))
}))

describe('Analytics Dashboard API Integration Tests', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    username: 'testuser'
  }

  const mockProfile = {
    id: 'test-profile-id',
    userId: mockUser.id,
    slug: 'testuser',
    viewCount: 150,
    theme: {
      primaryColor: '#3b82f6',
      secondaryColor: '#1e40af',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      fontFamily: 'Inter'
    },
    backgroundType: 'color',
    backgroundValue: '#ffffff',
    isPublic: true
  }

  const mockLinks = [
    {
      id: 'link-1',
      profileId: mockProfile.id,
      title: 'My Website',
      url: 'https://example.com',
      clickCount: 25,
      isVisible: true,
      _count: { linkClicks: 10 }
    },
    {
      id: 'link-2',
      profileId: mockProfile.id,
      title: 'GitHub',
      url: 'https://github.com/user',
      clickCount: 15,
      isVisible: true,
      _count: { linkClicks: 8 }
    }
  ]

  const mockProfileViews = [
    { timestamp: new Date('2024-01-01T10:00:00Z'), referrer: 'https://google.com' },
    { timestamp: new Date('2024-01-01T15:00:00Z'), referrer: 'https://twitter.com' },
    { timestamp: new Date('2024-01-02T12:00:00Z'), referrer: null },
    { timestamp: new Date('2024-01-03T08:00:00Z'), referrer: 'https://google.com' }
  ]

  const mockLinkClicks = [
    { timestamp: new Date('2024-01-01T11:00:00Z'), linkId: 'link-1' },
    { timestamp: new Date('2024-01-02T14:00:00Z'), linkId: 'link-1' },
    { timestamp: new Date('2024-01-03T09:00:00Z'), linkId: 'link-2' }
  ]

  const mockTopReferrers = [
    { referrer: 'https://google.com', _count: { referrer: 2 } },
    { referrer: 'https://twitter.com', _count: { referrer: 1 } }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default successful auth
    jest.mocked(auth).mockResolvedValue({
      user: mockUser
    } as any)

    // Setup default database responses
    jest.mocked(db.profile.findUnique).mockResolvedValue(mockProfile as any)
    jest.mocked(db.profileView.findMany).mockResolvedValue(mockProfileViews as any)
    jest.mocked(db.linkClick.findMany).mockResolvedValue(mockLinkClicks as any)
    jest.mocked(db.link.findMany).mockResolvedValue(mockLinks as any)
    jest.mocked(db.profileView.groupBy).mockResolvedValue(mockTopReferrers as any)
  })

  describe('GET /api/analytics/dashboard', () => {
    it('should return analytics data for authenticated user', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard?period=30')
      
      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toHaveProperty('overview')
      expect(data).toHaveProperty('charts')
      expect(data).toHaveProperty('linkPerformance')
      expect(data).toHaveProperty('topReferrers')

      // Verify overview data structure
      expect(data.overview).toMatchObject({
        totalViews: expect.any(Number),
        totalClicks: expect.any(Number),
        clickRate: expect.any(Number),
        periodViews: expect.any(Number),
        periodClicks: expect.any(Number)
      })

      // Verify charts data structure
      expect(data.charts).toMatchObject({
        viewsByDay: expect.any(Array),
        clicksByDay: expect.any(Array)
      })

      // Verify link performance data structure
      expect(data.linkPerformance).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            title: expect.any(String),
            url: expect.any(String),
            totalClicks: expect.any(Number),
            periodClicks: expect.any(Number),
            isVisible: expect.any(Boolean)
          })
        ])
      )

      // Verify top referrers data structure
      expect(data.topReferrers).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            referrer: expect.any(String),
            count: expect.any(Number)
          })
        ])
      )
    })

    it('should handle different period parameters', async () => {
      const periods = [7, 30, 90]
      
      for (const period of periods) {
        const request = new NextRequest(`http://localhost:3000/api/analytics/dashboard?period=${period}`)
        const response = await GET(request)
        
        expect(response.status).toBe(200)
        
        const data = await response.json()
        expect(data).toHaveProperty('overview')
        expect(data).toHaveProperty('charts')
      }
    })

    it('should use default period when not specified', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toHaveProperty('overview')
    })

    it('should return 401 for unauthenticated requests', async () => {
      jest.mocked(auth).mockResolvedValue(null)
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(401)
      
      const data = await response.json()
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('code')
    })

    it('should return 404 when user has no profile', async () => {
      jest.mocked(db.profile.findUnique).mockResolvedValue(null)
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(404)
      
      const data = await response.json()
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('code')
    })

    it('should handle invalid period parameters', async () => {
      const invalidPeriods = ['invalid', '0', '400', '-5']
      
      for (const period of invalidPeriods) {
        const request = new NextRequest(`http://localhost:3000/api/analytics/dashboard?period=${period}`)
        const response = await GET(request)
        
        expect(response.status).toBe(400)
        
        const data = await response.json()
        expect(data).toHaveProperty('error')
      }
    })

    it('should handle database errors gracefully', async () => {
      jest.mocked(db.profile.findUnique).mockRejectedValue(new Error('Database connection failed'))
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('code')
    })

    it('should handle empty analytics data', async () => {
      // Mock empty data responses
      jest.mocked(db.profileView.findMany).mockResolvedValue([])
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.overview.periodViews).toBe(0)
      expect(data.overview.periodClicks).toBe(0)
      expect(data.charts.viewsByDay).toEqual(expect.any(Array))
      expect(data.charts.clicksByDay).toEqual(expect.any(Array))
      expect(data.linkPerformance).toEqual([])
      expect(data.topReferrers).toEqual([])
    })

    it('should validate response data structure', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      const data = await response.json()

      // Validate overview structure
      expect(data.overview).toMatchObject({
        totalViews: expect.any(Number),
        totalClicks: expect.any(Number),
        clickRate: expect.any(Number),
        periodViews: expect.any(Number),
        periodClicks: expect.any(Number)
      })

      // Validate charts structure
      expect(Array.isArray(data.charts.viewsByDay)).toBe(true)
      expect(Array.isArray(data.charts.clicksByDay)).toBe(true)
      
      // Validate chart data items
      if (data.charts.viewsByDay.length > 0) {
        expect(data.charts.viewsByDay[0]).toMatchObject({
          date: expect.any(String),
          count: expect.any(Number)
        })
      }

      // Validate link performance structure
      expect(Array.isArray(data.linkPerformance)).toBe(true)
      if (data.linkPerformance.length > 0) {
        expect(data.linkPerformance[0]).toMatchObject({
          id: expect.any(String),
          title: expect.any(String),
          url: expect.any(String),
          totalClicks: expect.any(Number),
          periodClicks: expect.any(Number),
          isVisible: expect.any(Boolean)
        })
      }

      // Validate top referrers structure
      expect(Array.isArray(data.topReferrers)).toBe(true)
      if (data.topReferrers.length > 0) {
        expect(data.topReferrers[0]).toMatchObject({
          referrer: expect.any(String),
          count: expect.any(Number)
        })
      }
    })

    it('should handle concurrent requests properly', async () => {
      const requests = Array.from({ length: 5 }, () => 
        new NextRequest('http://localhost:3000/api/analytics/dashboard')
      )
      
      const responses = await Promise.all(requests.map(req => GET(req)))
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200)
      })
      
      // All responses should have the same structure
      const dataPromises = responses.map(response => response.json())
      const dataResults = await Promise.all(dataPromises)
      
      dataResults.forEach(data => {
        expect(data).toHaveProperty('overview')
        expect(data).toHaveProperty('charts')
        expect(data).toHaveProperty('linkPerformance')
        expect(data).toHaveProperty('topReferrers')
      })
    })

    it('should handle malformed query parameters', async () => {
      const malformedRequests = [
        'http://localhost:3000/api/analytics/dashboard?period=',
        'http://localhost:3000/api/analytics/dashboard?period=abc',
        'http://localhost:3000/api/analytics/dashboard?period=30.5',
        'http://localhost:3000/api/analytics/dashboard?startDate=invalid',
        'http://localhost:3000/api/analytics/dashboard?endDate=2024-13-45'
      ]
      
      for (const url of malformedRequests) {
        const request = new NextRequest(url)
        const response = await GET(request)
        
        // Should either use defaults (200) or return validation error (400)
        expect([200, 400]).toContain(response.status)
      }
    })

    it('should calculate metrics correctly', async () => {
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      const data = await response.json()

      // Verify click rate calculation
      const expectedClickRate = data.overview.totalViews > 0 
        ? Math.round((data.overview.totalClicks / data.overview.totalViews) * 100 * 10) / 10
        : 0
      
      expect(data.overview.clickRate).toBe(expectedClickRate)

      // Verify period counts match data length
      expect(data.overview.periodViews).toBe(mockProfileViews.length)
      expect(data.overview.periodClicks).toBe(mockLinkClicks.length)
    })

    it('should handle timezone edge cases', async () => {
      // Mock data with different timezones
      const timezoneViews = [
        { timestamp: new Date('2024-01-01T00:00:00Z'), referrer: null },
        { timestamp: new Date('2024-01-01T23:59:59Z'), referrer: null },
        { timestamp: new Date('2024-01-02T00:00:00+05:00'), referrer: null }
      ]
      
      jest.mocked(db.profileView.findMany).mockResolvedValue(timezoneViews as any)
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data.charts.viewsByDay).toEqual(expect.any(Array))
      
      // Should handle timezone normalization properly
      expect(data.charts.viewsByDay.length).toBeGreaterThan(0)
    })
  })

  describe('Error Scenarios', () => {
    it('should handle authentication service failures', async () => {
      jest.mocked(auth).mockRejectedValue(new Error('Auth service unavailable'))
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data).toHaveProperty('error')
    })

    it('should handle partial database failures', async () => {
      // Mock one query failing while others succeed
      jest.mocked(db.profileView.findMany).mockRejectedValue(new Error('Query timeout'))
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('code')
    })

    it('should handle data validation failures', async () => {
      // Mock invalid data structure from database
      jest.mocked(db.link.findMany).mockResolvedValue([
        { invalidStructure: true }
      ] as any)
      
      const request = new NextRequest('http://localhost:3000/api/analytics/dashboard')
      const response = await GET(request)
      
      // Should either handle gracefully (200) or return validation error (500)
      expect([200, 500]).toContain(response.status)
    })
  })
})