import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { AnalyticsRepository } from '@/lib/repositories/analytics'

/**
 * Track a profile view with enhanced visitor context detection
 */
export async function POST(request: NextRequest) {
  try {
    const { profileId } = await request.json()

    if (!profileId) {
      return NextResponse.json(
        { error: 'Profile ID is required' },
        { status: 400 }
      )
    }

    // Get request headers for enhanced analytics
    const headersList = await headers()
    const userAgent = headersList.get('user-agent') || ''
    const referrer = headersList.get('referer') || undefined
    
    // Get current domain for referrer analysis
    const host = headersList.get('host') || 'localhost'
    const currentDomain = host.split(':')[0] // Remove port if present

    // Convert headers to Headers object for visitor context detection
    const requestHeaders = new Headers()
    headersList.forEach((value, key) => {
      requestHeaders.set(key, value)
    })

    // Track the view using enhanced visitor context
    await AnalyticsRepository.trackProfileView(
      profileId,
      userAgent,
      requestHeaders,
      referrer,
      currentDomain
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to track profile view:', error)
    return NextResponse.json(
      { error: 'Failed to track view' },
      { status: 500 }
    )
  }
}