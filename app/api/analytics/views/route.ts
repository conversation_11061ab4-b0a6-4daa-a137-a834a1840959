import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { AnalyticsRepository } from '@/lib/repositories/analytics'

/**
 * Track a profile view
 */
export async function POST(request: NextRequest) {
  try {
    const { profileId } = await request.json()

    if (!profileId) {
      return NextResponse.json(
        { error: 'Profile ID is required' },
        { status: 400 }
      )
    }

    // Get request headers for analytics
    const headersList = await headers()
    const userAgent = headersList.get('user-agent') || undefined
    const forwardedFor = headersList.get('x-forwarded-for')
    const realIp = headersList.get('x-real-ip')
    const referrer = headersList.get('referer') || undefined

    // Get IP address (prioritize x-forwarded-for for proxied requests)
    const ip = forwardedFor?.split(',')[0] || realIp || request.ip || 'unknown'

    // Track the view using the repository
    await AnalyticsRepository.trackProfileView(profileId, {
      userAgent,
      ip: ip !== 'unknown' ? ip : undefined,
      referrer,
      // Note: Country detection would require a GeoIP service
      country: undefined
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to track profile view:', error)
    return NextResponse.json(
      { error: 'Failed to track view' },
      { status: 500 }
    )
  }
}