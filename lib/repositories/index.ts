// Repository exports
export { UserRepository } from './user'
export { ProfileRepository } from './profile'
export { LinkRepository } from './link'

// Database utilities
export { 
  db, 
  prisma,
  handleDatabaseError,
  checkDatabaseConnection,
  withTransaction,
  disconnectDatabase,
  seedDatabase,
  DatabaseError,
  NotFoundError,
  ConflictError,
  ValidationError
} from '../db'

// Type exports for convenience
export type {
  User,
  Profile,
  Link,
  UserWithProfile,
  ProfileWithLinks,
  ProfileWithUser
} from '../types'

export type {
  CreateUserData,
  UpdateUserData,
  CreateLinkData,
  UpdateLinkData,
  CreateProfileData,
  UpdateProfileData
} from '../validations'