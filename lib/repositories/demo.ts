/**
 * Demo script to test repository functionality
 * Run with: npx tsx lib/repositories/demo.ts
 */

import { UserRepository, ProfileRepository, LinkRepository, checkDatabaseConnection } from './index'

async function main() {
  console.log('🚀 Testing Repository Layer...\n')

  // Check database connection
  const isConnected = await checkDatabaseConnection()
  if (!isConnected) {
    console.error('❌ Database connection failed')
    process.exit(1)
  }
  console.log('✅ Database connection successful')

  try {
    // Test User Repository
    console.log('\n📝 Testing UserRepository...')
    
    const testEmail = `test-${Date.now()}@example.com`
    const testUsername = `testuser${Date.now()}`
    
    const userData = {
      email: testEmail,
      username: testUsername,
      displayName: 'Test User',
      bio: 'This is a test user',
      password: 'hashedpassword123'
    }

    // Check availability
    const isUsernameAvailable = await UserRepository.isUsernameAvailable(testUsername)
    const isEmailAvailable = await UserRepository.isEmailAvailable(testEmail)
    console.log(`  Username "${testUsername}" available: ${isUsernameAvailable}`)
    console.log(`  Email "${testEmail}" available: ${isEmailAvailable}`)

    // Create user
    const user = await UserRepository.create(userData)
    console.log(`  ✅ Created user: ${user.displayName} (${user.id})`)

    // Test Profile Repository
    console.log('\n🎨 Testing ProfileRepository...')
    
    const profileData = {
      userId: user.id,
      slug: testUsername,
      theme: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#ffffff',
        textColor: '#1e293b',
        fontFamily: 'Inter'
      },
      backgroundType: 'color' as const,
      backgroundValue: '#f8fafc',
      isPublic: true
    }

    // Check slug availability
    const isSlugAvailable = await ProfileRepository.isSlugAvailable(testUsername)
    console.log(`  Slug "${testUsername}" available: ${isSlugAvailable}`)

    // Create profile
    const profile = await ProfileRepository.create(profileData)
    console.log(`  ✅ Created profile: ${profile.slug} (${profile.id})`)

    // Test Link Repository
    console.log('\n🔗 Testing LinkRepository...')

    const linkData1 = {
      title: 'Personal Website',
      url: 'https://example.com',
      icon: 'globe',
      isVisible: true
    }

    const linkData2 = {
      title: 'GitHub Profile',
      url: 'https://github.com/testuser',
      icon: 'github',
      isVisible: true
    }

    // Create links
    const link1 = await LinkRepository.create(profile.id, linkData1)
    const link2 = await LinkRepository.create(profile.id, linkData2)
    console.log(`  ✅ Created link: ${link1.title} (order: ${link1.order})`)
    console.log(`  ✅ Created link: ${link2.title} (order: ${link2.order})`)

    // Test link operations
    await LinkRepository.incrementClickCount(link1.id)
    console.log(`  📊 Incremented click count for "${link1.title}"`)

    // Test reordering
    const reorderedLinks = await LinkRepository.reorder(profile.id, [link2.id, link1.id])
    console.log(`  🔄 Reordered links: ${reorderedLinks.map(l => `${l.title} (order: ${l.order})`).join(', ')}`)

    // Test analytics
    const analytics = await ProfileRepository.getAnalytics(profile.id)
    console.log(`  📈 Profile analytics:`, analytics)

    // Test finding profile with links
    const profileWithLinks = await ProfileRepository.findBySlugWithLinks(testUsername)
    console.log(`  🔍 Found profile "${profileWithLinks?.slug}" with ${profileWithLinks?.links.length} visible links`)

    // Cleanup
    console.log('\n🧹 Cleaning up...')
    await LinkRepository.delete(link1.id)
    await LinkRepository.delete(link2.id)
    await ProfileRepository.delete(profile.id)
    await UserRepository.delete(user.id)
    console.log('  ✅ Cleanup completed')

    console.log('\n🎉 All repository tests passed!')

  } catch (error) {
    console.error('\n❌ Repository test failed:', error)
    process.exit(1)
  }
}

// Run the demo if this file is executed directly
if (require.main === module) {
  main().catch(console.error)
}

export { main as runRepositoryDemo }