# Repository Layer

This directory contains the data access layer for the LinksInBio application, providing a clean interface between the application logic and the database.

## Overview

The repository layer implements the Repository pattern to:
- Encapsulate database operations
- Provide type-safe data access
- Handle errors consistently
- Validate data before database operations
- Support complex queries and transactions

## Architecture

```
lib/repositories/
├── index.ts          # Main exports
├── user.ts           # User repository
├── profile.ts        # Profile repository  
├── link.ts           # Link repository
├── demo.ts           # Demo/testing script
├── __tests__/        # Test examples
└── README.md         # This file
```

## Core Components

### Database Utilities (`lib/db.ts`)

Provides database connection management and error handling:

```typescript
import { db, handleDatabaseError, withTransaction } from './db'

// Basic database operations
const user = await db.user.findUnique({ where: { id } })

// With error handling
try {
  const result = await someOperation()
} catch (error) {
  return handleDatabaseError(error)
}

// With transactions
const result = await withTransaction(async (tx) => {
  // Multiple operations in a transaction
  const user = await tx.user.create(userData)
  const profile = await tx.profile.create({ ...profileData, userId: user.id })
  return { user, profile }
})
```

### Error Classes

Custom error classes for better error handling:

- `DatabaseError` - Base database error
- `NotFoundError` - Resource not found (404)
- `ConflictError` - Unique constraint violation (409)
- `ValidationError` - Data validation error (400)

### Validation Schemas (`lib/validations.ts`)

Zod schemas for data validation:

```typescript
import { createUserSchema, updateUserSchema } from '../validations'

// Validate data before database operations
const validatedData = createUserSchema.parse(userData)
```

## Repository Classes

### UserRepository

Manages user data operations:

```typescript
import { UserRepository } from './repositories'

// Create user
const user = await UserRepository.create({
  email: '<EMAIL>',
  username: 'username',
  displayName: 'Display Name',
  bio: 'User bio',
  password: 'hashedpassword'
})

// Find operations
const user = await UserRepository.findById(id)
const user = await UserRepository.findByEmail(email)
const user = await UserRepository.findByUsername(username)
const userWithProfile = await UserRepository.findWithProfile(id)

// Update operations
const updatedUser = await UserRepository.update(id, updateData)
const updatedUser = await UserRepository.updatePassword(id, hashedPassword)

// Utility operations
const isAvailable = await UserRepository.isUsernameAvailable(username)
const isAvailable = await UserRepository.isEmailAvailable(email)

// Password reset
await UserRepository.setResetToken(email, token, expiry)
const user = await UserRepository.findByResetToken(token)
await UserRepository.clearResetToken(userId)
```

### ProfileRepository

Manages profile data operations:

```typescript
import { ProfileRepository } from './repositories'

// Create profile
const profile = await ProfileRepository.create({
  userId: 'user-id',
  slug: 'username',
  theme: {
    primaryColor: '#000000',
    secondaryColor: '#666666',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    fontFamily: 'Inter'
  },
  backgroundType: 'color',
  backgroundValue: '#ffffff',
  isPublic: true
})

// Find operations
const profile = await ProfileRepository.findById(id)
const profile = await ProfileRepository.findBySlug(slug)
const profile = await ProfileRepository.findByUserId(userId)
const profileWithLinks = await ProfileRepository.findWithLinks(id)
const publicProfile = await ProfileRepository.findBySlugWithLinks(slug)

// Update operations
const updatedProfile = await ProfileRepository.update(id, updateData)
const updatedProfile = await ProfileRepository.updateByUserId(userId, updateData)

// Analytics
await ProfileRepository.incrementViewCount(id)
const analytics = await ProfileRepository.getAnalytics(id)

// Utility operations
const isAvailable = await ProfileRepository.isSlugAvailable(slug)
await ProfileRepository.toggleVisibility(id)
```

### LinkRepository

Manages link data operations:

```typescript
import { LinkRepository } from './repositories'

// Create link
const link = await LinkRepository.create(profileId, {
  title: 'Link Title',
  url: 'https://example.com',
  icon: 'globe',
  isVisible: true
})

// Find operations
const link = await LinkRepository.findById(id)
const links = await LinkRepository.findByProfileId(profileId)
const visibleLinks = await LinkRepository.findByProfileId(profileId, false)

// Update operations
const updatedLink = await LinkRepository.update(id, updateData)
const reorderedLinks = await LinkRepository.reorder(profileId, [id1, id2, id3])
const movedLink = await LinkRepository.moveToPosition(id, newPosition)

// Bulk operations
const updatedLinks = await LinkRepository.bulkUpdate([
  { id: 'link1', data: { title: 'New Title' } },
  { id: 'link2', data: { isVisible: false } }
])

// Analytics
await LinkRepository.incrementClickCount(id)
const analytics = await LinkRepository.getAnalytics(profileId)
const totalClicks = await LinkRepository.getTotalClickCount(profileId)

// Utility operations
await LinkRepository.toggleVisibility(id)
const duplicatedLink = await LinkRepository.duplicate(id)
```

## Usage Examples

### Basic CRUD Operations

```typescript
import { UserRepository, ProfileRepository, LinkRepository } from './repositories'

// Create a complete user profile with links
async function createUserProfile(userData, profileData, linksData) {
  try {
    // Create user
    const user = await UserRepository.create(userData)
    
    // Create profile
    const profile = await ProfileRepository.create({
      ...profileData,
      userId: user.id
    })
    
    // Create links
    const links = []
    for (const linkData of linksData) {
      const link = await LinkRepository.create(profile.id, linkData)
      links.push(link)
    }
    
    return { user, profile, links }
  } catch (error) {
    console.error('Failed to create user profile:', error)
    throw error
  }
}
```

### Error Handling

```typescript
import { NotFoundError, ConflictError, ValidationError } from './repositories'

try {
  const user = await UserRepository.create(userData)
} catch (error) {
  if (error instanceof ConflictError) {
    // Handle duplicate email/username
    console.log('User already exists:', error.message)
  } else if (error instanceof ValidationError) {
    // Handle validation errors
    console.log('Invalid data:', error.message)
  } else {
    // Handle other errors
    console.log('Unexpected error:', error.message)
  }
}
```

### Transactions

```typescript
import { withTransaction } from './repositories'

// Create user and profile in a single transaction
const result = await withTransaction(async (tx) => {
  const user = await tx.user.create({ data: userData })
  const profile = await tx.profile.create({ 
    data: { ...profileData, userId: user.id } 
  })
  return { user, profile }
})
```

## Testing

### Running the Demo

```bash
# Install tsx if not already installed
npm install -g tsx

# Run the repository demo
npx tsx lib/repositories/demo.ts
```

### Test Examples

See `__tests__/repositories.test.ts` for comprehensive test examples. To run actual tests:

1. Install Jest and related dependencies
2. Configure Jest for TypeScript
3. Run tests with `npm test`

## Best Practices

1. **Always validate data** before database operations using Zod schemas
2. **Handle errors appropriately** using the custom error classes
3. **Use transactions** for operations that modify multiple tables
4. **Check availability** before creating resources with unique constraints
5. **Use type-safe operations** with TypeScript interfaces
6. **Clean up resources** in reverse order of creation (due to foreign keys)

## Database Schema

The repositories work with the following Prisma models:

- `User` - User accounts and authentication
- `Profile` - User profiles with customization settings
- `Link` - Links displayed on user profiles
- `Account` - NextAuth.js account linking
- `Session` - NextAuth.js session management

See `prisma/schema.prisma` for the complete database schema.

## Environment Setup

Ensure your `.env` file contains:

```env
DATABASE_URL="file:./dev.db"  # For SQLite
# DATABASE_URL="postgresql://..." # For PostgreSQL in production
```

## Migration

When making schema changes:

```bash
# Generate migration
npx prisma migrate dev --name your-migration-name

# Apply migrations in production
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate
```