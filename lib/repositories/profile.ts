import { Profile, Prisma } from '@prisma/client'
import { db, handleDatabaseError, NotFoundError, ConflictError } from '../db'
import { createProfileSchema, updateProfileSchema, type CreateProfileData, type UpdateProfileData } from '../validations'
import type { ProfileWithLinks, ProfileWithUser } from '../types'

export class ProfileRepository {
  /**
   * Create a new profile
   */
  static async create(data: CreateProfileData): Promise<Profile> {
    try {
      // Validate input data
      const validatedData = createProfileSchema.parse(data)
      
      // Check if profile already exists for user
      const existingProfile = await db.profile.findUnique({
        where: { userId: validatedData.userId }
      })

      if (existingProfile) {
        throw new ConflictError('User already has a profile', 'userId')
      }

      // Check if slug is available
      const existingSlug = await db.profile.findUnique({
        where: { slug: validatedData.slug }
      })

      if (existingSlug) {
        throw new ConflictError('This slug is already taken', 'slug')
      }

      return await db.profile.create({
        data: {
          ...validatedData,
          theme: validatedData.theme as Prisma.JsonObject
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by ID
   */
  static async findById(id: string): Promise<Profile | null> {
    try {
      return await db.profile.findUnique({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by ID or throw error
   */
  static async findByIdOrThrow(id: string): Promise<Profile> {
    try {
      const profile = await db.profile.findUnique({
        where: { id }
      })

      if (!profile) {
        throw new NotFoundError('Profile', id)
      }

      return profile
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by user ID
   */
  static async findByUserId(userId: string): Promise<Profile | null> {
    try {
      return await db.profile.findUnique({
        where: { userId }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by slug
   */
  static async findBySlug(slug: string): Promise<Profile | null> {
    try {
      return await db.profile.findUnique({
        where: { slug }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by slug with user data
   */
  static async findBySlugWithUser(slug: string): Promise<ProfileWithUser | null> {
    try {
      return await db.profile.findUnique({
        where: { slug },
        include: {
          user: {
            select: {
              id: true,
              displayName: true,
              bio: true,
              profileImage: true
            }
          }
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile with links
   */
  static async findWithLinks(id: string): Promise<ProfileWithLinks | null> {
    try {
      return await db.profile.findUnique({
        where: { id },
        include: {
          links: {
            orderBy: { order: 'asc' }
          },
          user: true
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find profile by slug with links (for public view)
   */
  static async findBySlugWithLinks(slug: string): Promise<ProfileWithLinks | null> {
    try {
      return await db.profile.findUnique({
        where: { 
          slug,
          isPublic: true // Only return public profiles
        },
        include: {
          links: {
            where: { isVisible: true }, // Only return visible links
            orderBy: { order: 'asc' }
          },
          user: true
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update profile
   */
  static async update(id: string, data: UpdateProfileData): Promise<Profile> {
    try {
      // Validate input data
      const validatedData = updateProfileSchema.parse(data)

      // Check if profile exists
      const existingProfile = await this.findById(id)
      if (!existingProfile) {
        throw new NotFoundError('Profile', id)
      }

      // Check slug availability if updating slug
      if (validatedData.slug && validatedData.slug !== existingProfile.slug) {
        const existingSlug = await db.profile.findUnique({
          where: { slug: validatedData.slug }
        })

        if (existingSlug) {
          throw new ConflictError('This slug is already taken', 'slug')
        }
      }

      const updateData: any = { ...validatedData }
      if (updateData.theme) {
        updateData.theme = updateData.theme as Prisma.JsonObject
      }

      return await db.profile.update({
        where: { id },
        data: updateData
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update profile by user ID
   */
  static async updateByUserId(userId: string, data: UpdateProfileData): Promise<Profile> {
    try {
      const profile = await this.findByUserId(userId)
      if (!profile) {
        throw new NotFoundError('Profile for user', userId)
      }

      return await this.update(profile.id, data)
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Delete profile
   */
  static async delete(id: string): Promise<Profile> {
    try {
      // Check if profile exists
      const existingProfile = await this.findById(id)
      if (!existingProfile) {
        throw new NotFoundError('Profile', id)
      }

      return await db.profile.delete({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Check if slug is available
   */
  static async isSlugAvailable(slug: string, excludeProfileId?: string): Promise<boolean> {
    try {
      const existingProfile = await db.profile.findUnique({
        where: { slug }
      })

      if (!existingProfile) {
        return true
      }

      // If excluding a profile ID, check if the existing profile is the excluded one
      return excludeProfileId ? existingProfile.id === excludeProfileId : false
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Increment view count
   */
  static async incrementViewCount(id: string): Promise<Profile> {
    try {
      return await db.profile.update({
        where: { id },
        data: {
          viewCount: {
            increment: 1
          }
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Increment view count by slug
   */
  static async incrementViewCountBySlug(slug: string): Promise<Profile> {
    try {
      const profile = await this.findBySlug(slug)
      if (!profile) {
        throw new NotFoundError('Profile', slug)
      }

      return await this.incrementViewCount(profile.id)
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get profile analytics
   */
  static async getAnalytics(id: string): Promise<{
    viewCount: number
    totalLinks: number
    visibleLinks: number
    totalClicks: number
  }> {
    try {
      const profile = await db.profile.findUnique({
        where: { id },
        include: {
          links: true
        }
      })

      if (!profile) {
        throw new NotFoundError('Profile', id)
      }

      const totalClicks = profile.links.reduce((sum, link) => sum + link.clickCount, 0)

      return {
        viewCount: profile.viewCount,
        totalLinks: profile.links.length,
        visibleLinks: profile.links.filter(link => link.isVisible).length,
        totalClicks
      }
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Toggle profile visibility
   */
  static async toggleVisibility(id: string): Promise<Profile> {
    try {
      const profile = await this.findByIdOrThrow(id)
      
      return await db.profile.update({
        where: { id },
        data: {
          isPublic: !profile.isPublic
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get most viewed profiles for static generation
   */
  static async getMostViewed(limit: number = 50): Promise<Profile[]> {
    try {
      return await db.profile.findMany({
        where: {
          isPublic: true,
          viewCount: {
            gt: 0
          }
        },
        orderBy: {
          viewCount: 'desc'
        },
        take: limit
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }
}