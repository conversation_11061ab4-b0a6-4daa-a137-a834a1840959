import { db, handleDatabaseError } from '../db'
import { createHash } from 'crypto'
import { AnalyticsErrorFactory, isAnalyticsError } from '../errors/analytics-errors'
import { analyticsLogger, createTimer } from '../utils/analytics-logger'
import { 
  isValidProfileId, 
  isValidPeriod,
  sanitizeChartData,
  sanitizeLinkPerformanceData,
  sanitizeReferrerData,
  validateResponseData
} from '../validations/analytics'
import { createVisitorContext, VisitorContext } from '../utils/visitor-context'

export interface AnalyticsOverview {
  totalViews: number
  totalClicks: number
  clickRate: number
  periodViews: number
  periodClicks: number
}

export interface ChartData {
  date: string
  count: number
}

export interface LinkPerformanceData {
  id: string
  title: string
  url: string
  totalClicks: number
  periodClicks: number
  isVisible: boolean
}

export interface ReferrerData {
  referrer: string
  count: number
}

export interface AnalyticsDashboardData {
  overview: AnalyticsOverview
  charts: {
    viewsByDay: ChartData[]
    clicksByDay: ChartData[]
  }
  linkPerformance: LinkPerformanceData[]
  topReferrers: ReferrerData[]
}

export class AnalyticsRepository {
  /**
   * Track a profile view with enhanced visitor context detection
   */
  static async trackProfileView(
    profileId: string,
    userAgent: string,
    headers: Headers,
    referrer?: string,
    currentDomain: string = 'localhost'
  ): Promise<void> {
    const timer = createTimer('trackProfileView', { profileId })
    
    try {
      analyticsLogger.operationStart('trackProfileView', { profileId })

      // Input validation
      if (!profileId || typeof profileId !== 'string') {
        throw AnalyticsErrorFactory.invalidProfileId(profileId)
      }

      // Create comprehensive visitor context
      const context = createVisitorContext(userAgent, headers, referrer, currentDomain)

      await db.profileView.create({
        data: {
          profileId,
          userAgent: context.device.browser.name + ' ' + context.device.browser.version,
          ipHash: context.ipHash,
          referrer: context.referrer?.domain || null,
          country: context.location?.country || null,
          // Enhanced context data
          city: context.location?.city || null,
          region: context.location?.region || null,
          timezone: context.timezone,
          deviceType: context.device.device.type,
          osName: context.device.os.name,
          browserName: context.device.browser.name,
          isMobile: context.device.isMobile,
          referrerSource: context.referrer?.source || null,
          referrerMedium: context.referrer?.medium || null,
          isInternalReferrer: context.referrer?.isInternal || false
        }
      })

      // Also increment the simple counter
      await db.profile.update({
        where: { id: profileId },
        data: {
          viewCount: {
            increment: 1
          }
        }
      })

      const duration = timer.end()
      analyticsLogger.operationComplete('trackProfileView', duration, { profileId })
    } catch (error) {
      const duration = timer.end()
      
      if (isAnalyticsError(error)) {
        analyticsLogger.operationFailed('trackProfileView', error, { profileId, duration })
        throw error
      }

      const analyticsError = AnalyticsErrorFactory.databaseConnectionError(error as Error)
      analyticsLogger.operationFailed('trackProfileView', analyticsError, { profileId, duration })
      throw analyticsError
    }
  }

  /**
   * Track a link click with enhanced visitor context detection
   */
  static async trackLinkClick(
    linkId: string,
    profileId: string,
    userAgent: string,
    headers: Headers,
    referrer?: string,
    currentDomain: string = 'localhost'
  ): Promise<void> {
    const timer = createTimer('trackLinkClick', { linkId, profileId })
    
    try {
      analyticsLogger.operationStart('trackLinkClick', { linkId, profileId })

      // Input validation
      if (!linkId || typeof linkId !== 'string') {
        throw AnalyticsErrorFactory.dataValidationError('linkId', linkId, 'string')
      }

      if (!profileId || typeof profileId !== 'string') {
        throw AnalyticsErrorFactory.invalidProfileId(profileId)
      }

      // Create comprehensive visitor context
      const context = createVisitorContext(userAgent, headers, referrer, currentDomain)

      await db.linkClick.create({
        data: {
          linkId,
          profileId,
          userAgent: context.device.browser.name + ' ' + context.device.browser.version,
          ipHash: context.ipHash,
          referrer: context.referrer?.domain || null,
          country: context.location?.country || null,
          // Enhanced context data
          city: context.location?.city || null,
          region: context.location?.region || null,
          timezone: context.timezone,
          deviceType: context.device.device.type,
          osName: context.device.os.name,
          browserName: context.device.browser.name,
          isMobile: context.device.isMobile,
          referrerSource: context.referrer?.source || null,
          referrerMedium: context.referrer?.medium || null,
          isInternalReferrer: context.referrer?.isInternal || false
        }
      })

      // Also increment the simple counter
      await db.link.update({
        where: { id: linkId },
        data: {
          clickCount: {
            increment: 1
          }
        }
      })

      const duration = timer.end()
      analyticsLogger.operationComplete('trackLinkClick', duration, { linkId, profileId })
    } catch (error) {
      const duration = timer.end()
      
      if (isAnalyticsError(error)) {
        analyticsLogger.operationFailed('trackLinkClick', error, { linkId, profileId, duration })
        throw error
      }

      const analyticsError = AnalyticsErrorFactory.databaseConnectionError(error as Error)
      analyticsLogger.operationFailed('trackLinkClick', analyticsError, { linkId, profileId, duration })
      throw analyticsError
    }
  }

  /**
   * Get comprehensive analytics dashboard data with optimized parallel queries
   */
  static async getDashboardData(
    profileId: string,
    periodDays: number = 30
  ): Promise<AnalyticsDashboardData> {
    const timer = createTimer('getDashboardData', { profileId, periodDays })
    
    try {
      analyticsLogger.operationStart('getDashboardData', { profileId, periodDays })

      // Input validation using enhanced type guards
      if (!isValidProfileId(profileId)) {
        throw AnalyticsErrorFactory.invalidProfileId(profileId)
      }
      
      if (!isValidPeriod(periodDays)) {
        throw AnalyticsErrorFactory.invalidPeriod(periodDays)
      }

      // Enhanced date range calculation with proper timezone handling
      const { startDate, endDate } = this.calculateDateRange(periodDays)
      
      // Validate date range
      if (startDate >= endDate) {
        throw AnalyticsErrorFactory.invalidDateRange(startDate, endDate)
      }

      analyticsLogger.debug('Date range calculated', { 
        profileId, 
        startDate: startDate.toISOString(), 
        endDate: endDate.toISOString(),
        periodDays 
      })

      // Execute ALL database queries in parallel for maximum performance
      const queriesTimer = createTimer('parallelQueries', { profileId })
      const [profile, profileViews, linkClicks, linkPerformance, topReferrers] = await this.executeParallelQueries(
        profileId,
        startDate,
        endDate
      )
      queriesTimer.end()

      if (!profile) {
        throw AnalyticsErrorFactory.profileNotFound(profileId)
      }

      analyticsLogger.debug('All data retrieved in parallel', { 
        profileId, 
        viewCount: profile.viewCount,
        profileViewsCount: profileViews.length,
        linkClicksCount: linkClicks.length,
        linkPerformanceCount: linkPerformance.length,
        topReferrersCount: topReferrers.length 
      })

      // Enhanced data validation and sanitization
      const validProfileViews = this.sanitizeTimestampData(profileViews, 'profile views')
      const validLinkClicks = this.sanitizeTimestampData(linkClicks, 'link clicks')

      if (validProfileViews.length !== profileViews.length) {
        analyticsLogger.warn('Sanitized profile views data', { 
          profileId, 
          originalCount: profileViews.length,
          validCount: validProfileViews.length,
          operation: 'dataSanitization'
        })
      }

      if (validLinkClicks.length !== linkClicks.length) {
        analyticsLogger.warn('Sanitized link clicks data', { 
          profileId, 
          originalCount: linkClicks.length,
          validCount: validLinkClicks.length,
          operation: 'dataSanitization'
        })
      }

      // Get expected number of days for validation
      const { totalDays } = this.getDateRangeBoundaries(periodDays)

      // Optimized parallel data aggregation for better performance
      const aggregationTimer = createTimer('dataAggregation')
      const [rawViewsByDay, rawClicksByDay] = await Promise.all([
        Promise.resolve(this.aggregateByDay(validProfileViews, startDate, endDate)),
        Promise.resolve(this.aggregateByDay(validLinkClicks, startDate, endDate))
      ])
      aggregationTimer.end()

      // Sanitize aggregated chart data
      const viewsByDay = sanitizeChartData(rawViewsByDay, 'profile views')
      const clicksByDay = sanitizeChartData(rawClicksByDay, 'link clicks')

      // Validate aggregated data structure and completeness
      const viewsValid = this.validateAggregatedData(viewsByDay, totalDays, 'Views')
      const clicksValid = this.validateAggregatedData(clicksByDay, totalDays, 'Clicks')

      if (!viewsValid) {
        analyticsLogger.warn('Views aggregation validation failed', { 
          profileId, 
          expectedDays: totalDays,
          actualDays: viewsByDay.length,
          operation: 'aggregationValidation'
        })
      }

      if (!clicksValid) {
        analyticsLogger.warn('Clicks aggregation validation failed', { 
          profileId, 
          expectedDays: totalDays,
          actualDays: clicksByDay.length,
          operation: 'aggregationValidation'
        })
      }

      // Additional validation for data consistency
      if (viewsByDay.length === 0 && validProfileViews.length > 0) {
        analyticsLogger.warn('Views aggregation resulted in empty data despite having valid input', { 
          profileId, 
          inputCount: validProfileViews.length,
          operation: 'aggregationConsistency'
        })
      }
      
      if (clicksByDay.length === 0 && validLinkClicks.length > 0) {
        analyticsLogger.warn('Clicks aggregation resulted in empty data despite having valid input', { 
          profileId, 
          inputCount: validLinkClicks.length,
          operation: 'aggregationConsistency'
        })
      }

      // Calculate totals with enhanced validation
      const totalViews = Math.max(0, profile.viewCount || 0)
      const totalClicks = linkPerformance.reduce((sum: number, link: { clickCount: number }) => {
        const clicks = typeof link.clickCount === 'number' ? link.clickCount : 0
        return sum + Math.max(0, clicks)
      }, 0)
      const periodViews = validProfileViews.length
      const periodClicks = validLinkClicks.length
      const clickRate = totalViews > 0 ? Math.round((totalClicks / totalViews) * 100 * 10) / 10 : 0

      // Prepare raw result data
      const rawResult = {
        overview: {
          totalViews,
          totalClicks,
          clickRate: parseFloat(clickRate.toFixed(1)),
          periodViews,
          periodClicks
        },
        charts: {
          viewsByDay,
          clicksByDay
        },
        linkPerformance: linkPerformance.map((link: {
          id: string;
          title: string;
          url: string;
          clickCount: number;
          isVisible: boolean;
          _count: { linkClicks: number };
        }) => ({
          id: link.id,
          title: link.title,
          url: link.url,
          totalClicks: link.clickCount,
          periodClicks: link._count.linkClicks,
          isVisible: link.isVisible
        })),
        topReferrers: topReferrers.map((ref: {
          referrer: string | null;
          _count: { referrer: number };
        }) => ({
          referrer: ref.referrer || 'Direct',
          count: ref._count.referrer
        }))
      }

      // Sanitize link performance and referrer data
      const sanitizedLinkPerformance = sanitizeLinkPerformanceData(rawResult.linkPerformance)
      const sanitizedTopReferrers = sanitizeReferrerData(rawResult.topReferrers)

      // Create final result with sanitized data
      const result = {
        ...rawResult,
        linkPerformance: sanitizedLinkPerformance,
        topReferrers: sanitizedTopReferrers
      }

      // Final validation of complete response structure
      try {
        validateResponseData(result)
      } catch (validationError) {
        analyticsLogger.error('Final response validation failed', {
          profileId,
          periodDays,
          operation: 'finalValidation'
        }, validationError as Error)
        
        // Return a safe fallback structure if validation fails
        return {
          overview: {
            totalViews: Math.max(0, totalViews),
            totalClicks: Math.max(0, totalClicks),
            clickRate: Math.max(0, Math.min(100, parseFloat(clickRate.toFixed(1)))),
            periodViews: Math.max(0, periodViews),
            periodClicks: Math.max(0, periodClicks)
          },
          charts: {
            viewsByDay: [],
            clicksByDay: []
          },
          linkPerformance: [],
          topReferrers: []
        }
      }

      const duration = timer.end()
      analyticsLogger.operationComplete('getDashboardData', duration, { 
        profileId, 
        periodDays,
        totalViews,
        totalClicks,
        periodViews,
        periodClicks
      })

      return result
    } catch (error) {
      const duration = timer.end()
      
      if (isAnalyticsError(error)) {
        analyticsLogger.operationFailed('getDashboardData', error, { profileId, periodDays, duration })
        throw error
      }

      const analyticsError = AnalyticsErrorFactory.dataProcessingError('getDashboardData', error as Error)
      analyticsLogger.operationFailed('getDashboardData', analyticsError, { profileId, periodDays, duration })
      throw analyticsError
    }
  }

  /**
   * Get analytics data for a specific time range with enhanced validation
   */
  static async getAnalyticsForDateRange(
    profileId: string,
    startDate: Date,
    endDate: Date
  ): Promise<{
    views: number
    clicks: number
    uniqueReferrers: number
  }> {
    try {
      // Input validation
      if (!isValidProfileId(profileId)) {
        throw new Error('Invalid profile ID provided')
      }

      if (!startDate || !endDate || !(startDate instanceof Date) || !(endDate instanceof Date)) {
        throw new Error('Invalid date range provided')
      }

      if (startDate >= endDate) {
        throw new Error('Start date must be before end date')
      }

      // Normalize dates to ensure consistent timezone handling
      const normalizedStartDate = new Date(startDate)
      const normalizedEndDate = new Date(endDate)
      normalizedStartDate.setHours(0, 0, 0, 0)
      normalizedEndDate.setHours(23, 59, 59, 999)

      const [viewCount, clickCount, referrerCount] = await Promise.all([
        db.profileView.count({
          where: {
            profileId,
            timestamp: {
              gte: normalizedStartDate,
              lte: normalizedEndDate
            }
          }
        }),
        db.linkClick.count({
          where: {
            profileId,
            timestamp: {
              gte: normalizedStartDate,
              lte: normalizedEndDate
            }
          }
        }),
        db.profileView.groupBy({
          by: ['referrer'],
          where: {
            profileId,
            timestamp: {
              gte: normalizedStartDate,
              lte: normalizedEndDate
            }
          }
        }).then((results: Array<{ referrer: string | null }>) => Array.isArray(results) ? results.length : 0)
      ])

      return {
        views: Math.max(0, viewCount || 0),
        clicks: Math.max(0, clickCount || 0),
        uniqueReferrers: Math.max(0, referrerCount || 0)
      }
    } catch (error) {
      console.error('Error in getAnalyticsForDateRange:', error)
      return handleDatabaseError(error)
    }
  }

  /**
   * Get most popular links for a profile
   */
  static async getTopLinks(
    profileId: string,
    limit: number = 10
  ): Promise<Array<{
    id: string
    title: string
    url: string
    clickCount: number
    recentClicks: number
  }>> {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      return await db.link.findMany({
        where: {
          profileId,
          isVisible: true
        },
        select: {
          id: true,
          title: true,
          url: true,
          clickCount: true,
          _count: {
            select: {
              linkClicks: {
                where: {
                  timestamp: {
                    gte: thirtyDaysAgo
                  }
                }
              }
            }
          }
        },
        orderBy: {
          clickCount: 'desc'
        },
        take: limit
      }).then((links: Array<{
        id: string;
        title: string;
        url: string;
        clickCount: number;
        _count: { linkClicks: number };
      }>) => 
        links.map((link) => ({
          id: link.id,
          title: link.title,
          url: link.url,
          clickCount: link.clickCount,
          recentClicks: link._count.linkClicks
        }))
      )
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Clean up old analytics data (for privacy compliance)
   */
  static async cleanupOldData(retentionDays: number = 365): Promise<void> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

      await Promise.all([
        db.profileView.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate
            }
          }
        }),
        db.linkClick.deleteMany({
          where: {
            timestamp: {
              lt: cutoffDate
            }
          }
        })
      ])
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Execute all database queries in parallel with retry logic and error handling
   */
  private static async executeParallelQueries(
    profileId: string,
    startDate: Date,
    endDate: Date
  ): Promise<[
    { viewCount: number } | null,
    Array<{ timestamp: Date; referrer: string | null }>,
    Array<{ timestamp: Date; linkId: string }>,
    Array<{
      id: string;
      title: string;
      url: string;
      clickCount: number;
      isVisible: boolean;
      _count: { linkClicks: number };
    }>,
    Array<{
      referrer: string | null;
      _count: { referrer: number };
    }>
  ]> {
    const maxRetries = 3
    const retryDelay = 1000 // 1 second

    const executeWithRetry = async <T>(
      operation: () => Promise<T>,
      operationName: string,
      retryCount = 0
    ): Promise<T> => {
      try {
        return await operation()
      } catch (error) {
        if (retryCount < maxRetries && this.isRetryableError(error)) {
          analyticsLogger.warn(`${operationName} failed, retrying (${retryCount + 1}/${maxRetries})`, {
            profileId,
            error: error instanceof Error ? error.message : String(error),
            operation: 'databaseRetry'
          })
          
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retryCount)))
          return executeWithRetry(operation, operationName, retryCount + 1)
        }
        
        analyticsLogger.error(`${operationName} failed after ${retryCount + 1} attempts`, {
          profileId,
          operation: 'databaseQueryFailed'
        }, error as Error)
        
        throw error
      }
    }

    // Execute all queries in parallel with individual retry logic
    return Promise.all([
      // Get profile data with retry
      executeWithRetry(
        () => db.profile.findUnique({
          where: { id: profileId },
          select: { viewCount: true }
        }),
        'getProfile'
      ),

      // Get profile views with optimized query and retry
      executeWithRetry(
        () => db.profileView.findMany({
          where: {
            profileId,
            timestamp: {
              gte: startDate,
              lte: endDate
            }
          },
          select: {
            timestamp: true,
            referrer: true
          },
          orderBy: {
            timestamp: 'asc'
          }
        }),
        'getProfileViews'
      ),

      // Get link clicks with optimized query and retry
      executeWithRetry(
        () => db.linkClick.findMany({
          where: {
            profileId,
            timestamp: {
              gte: startDate,
              lte: endDate
            }
          },
          select: {
            timestamp: true,
            linkId: true
          },
          orderBy: {
            timestamp: 'asc'
          }
        }),
        'getLinkClicks'
      ),

      // Get link performance data with retry
      executeWithRetry(
        () => db.link.findMany({
          where: {
            profileId
          },
          select: {
            id: true,
            title: true,
            url: true,
            clickCount: true,
            isVisible: true,
            _count: {
              select: {
                linkClicks: {
                  where: {
                    timestamp: {
                      gte: startDate,
                      lte: endDate
                    }
                  }
                }
              }
            }
          },
          orderBy: {
            clickCount: 'desc'
          }
        }),
        'getLinkPerformance'
      ),

      // Get top referrers with retry
      executeWithRetry(
        () => db.profileView.groupBy({
          by: ['referrer'],
          where: {
            profileId,
            timestamp: {
              gte: startDate,
              lte: endDate
            },
            referrer: {
              not: null
            }
          },
          _count: {
            referrer: true
          },
          orderBy: {
            _count: {
              referrer: 'desc'
            }
          },
          take: 5
        }),
        'getTopReferrers'
      )
    ])
  }

  /**
   * Check if an error is retryable (connection issues, timeouts, etc.)
   */
  private static isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      const message = error.message.toLowerCase()
      
      // Common retryable database errors
      return (
        message.includes('connection') ||
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('econnreset') ||
        message.includes('enotfound') ||
        message.includes('etimedout') ||
        message.includes('server closed the connection') ||
        message.includes('connection terminated unexpectedly')
      )
    }
    
    return false
  }

  /**
   * Calculate date range with enhanced timezone handling and validation
   */
  private static calculateDateRange(periodDays: number): { startDate: Date; endDate: Date } {
    // Use UTC to avoid timezone issues in date calculations
    const now = new Date()
    const endDate = new Date(Date.UTC(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      23, 59, 59, 999
    ))
    
    const startDate = new Date(endDate)
    startDate.setUTCDate(startDate.getUTCDate() - periodDays + 1)
    startDate.setUTCHours(0, 0, 0, 0)
    
    return { startDate, endDate }
  }

  /**
   * Normalize date to UTC start of day for consistent aggregation
   */
  private static normalizeToUTCDate(date: Date): string {
    const utcDate = new Date(Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    ))
    return utcDate.toISOString().split('T')[0]
  }

  /**
   * Optimized data aggregation by day with improved performance for large datasets
   */
  private static aggregateByDay(
    data: Array<{ timestamp: Date }>,
    startDate: Date,
    endDate: Date
  ): ChartData[] {
    // Input validation
    if (!Array.isArray(data)) {
      analyticsLogger.warn('Invalid data array provided to aggregateByDay', {
        operation: 'aggregateByDay',
        dataType: typeof data
      })
      return []
    }

    if (startDate >= endDate) {
      analyticsLogger.warn('Invalid date range provided to aggregateByDay', {
        operation: 'aggregateByDay',
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      })
      return []
    }

    const aggregationTimer = createTimer('aggregateByDay', { 
      dataLength: data.length,
      dateRange: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`
    })

    try {
      // Pre-calculate the expected date range for better performance
      const dateRange = this.generateDateRange(startDate, endDate)
      const dataMap = new Map<string, number>()
      
      // Initialize all dates with zero counts for complete coverage
      dateRange.forEach(dateStr => {
        dataMap.set(dateStr, 0)
      })

      // Optimized data processing with batch operations for large datasets
      if (data.length > 1000) {
        // For large datasets, process in chunks to avoid memory issues
        const chunkSize = 500
        for (let i = 0; i < data.length; i += chunkSize) {
          const chunk = data.slice(i, i + chunkSize)
          this.processDataChunk(chunk, dataMap)
        }
      } else {
        // For smaller datasets, process all at once
        this.processDataChunk(data, dataMap)
      }

      // Convert map to sorted array efficiently
      const result = dateRange.map(dateStr => ({
        date: dateStr,
        count: dataMap.get(dateStr) || 0
      }))

      const duration = aggregationTimer.end()
      analyticsLogger.debug('Data aggregation completed', {
        operation: 'aggregateByDay',
        inputSize: data.length,
        outputSize: result.length,
        duration,
        dateRange: dateRange.length
      })

      return result
    } catch (error) {
      const duration = aggregationTimer.end()
      analyticsLogger.error('Data aggregation failed', {
        operation: 'aggregateByDay',
        inputSize: data.length,
        duration
      }, error as Error)
      
      // Return empty result with proper date range on error
      return this.generateDateRange(startDate, endDate).map(dateStr => ({
        date: dateStr,
        count: 0
      }))
    }
  }

  /**
   * Process a chunk of data for aggregation (optimized for performance)
   */
  private static processDataChunk(
    chunk: Array<{ timestamp: Date }>,
    dataMap: Map<string, number>
  ): void {
    for (const item of chunk) {
      if (!item?.timestamp || !(item.timestamp instanceof Date)) {
        continue
      }

      try {
        const dateStr = this.normalizeToUTCDate(item.timestamp)
        const currentCount = dataMap.get(dateStr) || 0
        dataMap.set(dateStr, currentCount + 1)
      } catch (error) {
        // Skip invalid timestamps silently for performance
        continue
      }
    }
  }

  /**
   * Generate complete date range array for efficient processing
   */
  private static generateDateRange(startDate: Date, endDate: Date): string[] {
    const dateRange: string[] = []
    const current = new Date(startDate)
    const end = new Date(endDate)
    
    while (current <= end) {
      dateRange.push(this.normalizeToUTCDate(current))
      current.setUTCDate(current.getUTCDate() + 1)
    }
    
    return dateRange
  }

  /**
   * Sanitize timestamp data with comprehensive validation
   */
  private static sanitizeTimestampData(
    data: Array<{ timestamp: Date }>, 
    dataType: string
  ): Array<{ timestamp: Date }> {
    if (!Array.isArray(data)) {
      analyticsLogger.warn(`Invalid ${dataType} data structure`, {
        operation: 'sanitizeTimestampData',
        dataType,
        receivedType: typeof data
      })
      return []
    }

    const sanitized = data.filter((item): item is { timestamp: Date } => {
      if (!item || typeof item !== 'object') {
        analyticsLogger.warn(`Invalid ${dataType} item structure`, {
          operation: 'sanitizeTimestampData',
          dataType,
          item
        })
        return false
      }

      if (!item.timestamp || !(item.timestamp instanceof Date)) {
        analyticsLogger.warn(`Invalid ${dataType} timestamp`, {
          operation: 'sanitizeTimestampData',
          dataType,
          timestamp: item.timestamp,
          timestampType: typeof item.timestamp
        })
        return false
      }

      if (isNaN(item.timestamp.getTime())) {
        analyticsLogger.warn(`Invalid ${dataType} timestamp value`, {
          operation: 'sanitizeTimestampData',
          dataType,
          timestamp: item.timestamp
        })
        return false
      }

      // Check if timestamp is within reasonable bounds (not too far in future or past)
      const now = new Date()
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      const oneMonthFromNow = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate())

      if (item.timestamp < oneYearAgo || item.timestamp > oneMonthFromNow) {
        analyticsLogger.warn(`${dataType} timestamp outside reasonable bounds`, {
          operation: 'sanitizeTimestampData',
          dataType,
          timestamp: item.timestamp,
          bounds: { oneYearAgo, oneMonthFromNow }
        })
        return false
      }

      return true
    })

    if (sanitized.length !== data.length) {
      analyticsLogger.info(`${dataType} data sanitization completed`, {
        operation: 'sanitizeTimestampData',
        dataType,
        originalCount: data.length,
        sanitizedCount: sanitized.length,
        filteredCount: data.length - sanitized.length
      })
    }

    return sanitized
  }



  /**
   * Get date range boundaries for a given period with timezone safety
   */
  static getDateRangeBoundaries(periodDays: number): { 
    startDate: Date; 
    endDate: Date; 
    totalDays: number 
  } {
    const { startDate, endDate } = this.calculateDateRange(periodDays)
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
    
    return { startDate, endDate, totalDays }
  }

  /**
   * Format date for consistent display across components
   */
  static formatDateForDisplay(date: string | Date): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    
    if (!dateObj || isNaN(dateObj.getTime())) {
      return 'Invalid Date'
    }

    return dateObj.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: dateObj.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    })
  }

  /**
   * Check if aggregated data has expected number of data points
   */
  private static validateAggregatedData(
    data: ChartData[], 
    expectedDays: number, 
    dataType: string
  ): boolean {
    if (data.length !== expectedDays) {
      analyticsLogger.warn(`${dataType} aggregation validation failed`, {
        operation: 'validateAggregatedData',
        expectedDays,
        actualDays: data.length,
        dataType
      })
      return false
    }

    // Check for any invalid dates in the aggregated data
    const hasInvalidDates = data.some(item => 
      !item.date || 
      isNaN(new Date(item.date).getTime()) ||
      typeof item.count !== 'number' ||
      item.count < 0
    )

    if (hasInvalidDates) {
      analyticsLogger.warn(`${dataType} aggregation contains invalid data points`, {
        operation: 'validateAggregatedData',
        dataType
      })
      return false
    }

    return true
  }

  /**
   * Optimize memory usage for very large datasets by using streaming aggregation
   */
  private static async streamingAggregateByDay(
    profileId: string,
    startDate: Date,
    endDate: Date,
    dataType: 'views' | 'clicks'
  ): Promise<ChartData[]> {
    const dateRange = this.generateDateRange(startDate, endDate)
    const dataMap = new Map<string, number>()
    
    // Initialize all dates with zero counts
    dateRange.forEach(dateStr => {
      dataMap.set(dateStr, 0)
    })

    const batchSize = 1000
    let offset = 0
    let hasMore = true

    const table = dataType === 'views' ? 'profileView' : 'linkClick'
    
    while (hasMore) {
      try {
        // Fetch data in batches to manage memory usage
        const batch = await (dataType === 'views' 
          ? db.profileView.findMany({
              where: {
                profileId,
                timestamp: {
                  gte: startDate,
                  lte: endDate
                }
              },
              select: { timestamp: true },
              skip: offset,
              take: batchSize,
              orderBy: { timestamp: 'asc' }
            })
          : db.linkClick.findMany({
              where: {
                profileId,
                timestamp: {
                  gte: startDate,
                  lte: endDate
                }
              },
              select: { timestamp: true },
              skip: offset,
              take: batchSize,
              orderBy: { timestamp: 'asc' }
            })
        )

        if (batch.length === 0) {
          hasMore = false
          break
        }

        // Process batch
        this.processDataChunk(batch, dataMap)
        
        offset += batchSize
        hasMore = batch.length === batchSize

        // Add small delay to prevent overwhelming the database
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 10))
        }
      } catch (error) {
        analyticsLogger.error(`Streaming aggregation failed for ${dataType}`, {
          operation: 'streamingAggregateByDay',
          profileId,
          dataType,
          offset,
          batchSize
        }, error as Error)
        
        // Fall back to regular aggregation on error
        const fallbackData = dataType === 'views'
          ? await db.profileView.findMany({
              where: { profileId, timestamp: { gte: startDate, lte: endDate } },
              select: { timestamp: true }
            })
          : await db.linkClick.findMany({
              where: { profileId, timestamp: { gte: startDate, lte: endDate } },
              select: { timestamp: true }
            })
        
        return this.aggregateByDay(fallbackData, startDate, endDate)
      }
    }

    // Convert map to sorted array
    return dateRange.map(dateStr => ({
      date: dateStr,
      count: dataMap.get(dateStr) || 0
    }))
  }

  /**
   * Determine if streaming aggregation should be used based on data size estimation
   */
  private static async shouldUseStreamingAggregation(
    profileId: string,
    startDate: Date,
    endDate: Date
  ): Promise<boolean> {
    try {
      // Quick count to estimate data size
      const [viewCount, clickCount] = await Promise.all([
        db.profileView.count({
          where: {
            profileId,
            timestamp: { gte: startDate, lte: endDate }
          }
        }),
        db.linkClick.count({
          where: {
            profileId,
            timestamp: { gte: startDate, lte: endDate }
          }
        })
      ])

      // Use streaming for datasets larger than 5000 records
      return (viewCount + clickCount) > 5000
    } catch (error) {
      // Default to regular aggregation if count fails
      return false
    }
  }
}