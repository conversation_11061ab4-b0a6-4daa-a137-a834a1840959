import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { AnalyticsRepository } from '../analytics'
import { db } from '../../db'
import { AnalyticsErrorFactory } from '../../errors/analytics-errors'

// Mock the database
jest.mock('../../db', () => ({
  db: {
    profileView: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
      deleteMany: jest.fn()
    },
    linkClick: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn()
    },
    profile: {
      update: jest.fn(),
      findUnique: jest.fn()
    },
    link: {
      update: jest.fn(),
      findMany: jest.fn()
    }
  },
  handleDatabaseError: jest.fn()
}))

// Mock the logger
jest.mock('../../utils/analytics-logger', () => ({
  analyticsLogger: {
    operationStart: jest.fn(),
    operationComplete: jest.fn(),
    operationFailed: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  },
  createTimer: jest.fn(() => ({
    end: jest.fn(() => 100)
  }))
}))

// Mock validation functions
jest.mock('../../validations/analytics', () => ({
  isValidProfileId: jest.fn((id) => typeof id === 'string' && id.length > 0),
  isValidPeriod: jest.fn((period) => typeof period === 'number' && period > 0 && period <= 365),
  sanitizeChartData: jest.fn((data) => data),
  sanitizeLinkPerformanceData: jest.fn((data) => data),
  sanitizeReferrerData: jest.fn((data) => data),
  validateResponseData: jest.fn((data) => data)
}))

describe('AnalyticsRepository - Unit Tests', () => {
  const mockProfileId = 'test-profile-id'
  const mockLinkId = 'test-link-id'
  const mockMetadata = {
    userAgent: 'Mozilla/5.0 Test Browser',
    ip: '***********',
    referrer: 'https://google.com',
    country: 'US'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Data Aggregation Functions', () => {
    describe('aggregateByDay', () => {
      it('should aggregate data by day correctly', () => {
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-03T23:59:59Z')
        const mockData = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: new Date('2024-01-01T15:00:00Z') },
          { timestamp: new Date('2024-01-02T12:00:00Z') },
          { timestamp: new Date('2024-01-03T08:00:00Z') }
        ]

        // Access the private method through reflection for testing
        const aggregateByDay = (AnalyticsRepository as any).aggregateByDay
        const result = aggregateByDay(mockData, startDate, endDate)

        expect(result).toHaveLength(3) // 3 days
        expect(result[0]).toEqual({ date: '2024-01-01', count: 2 })
        expect(result[1]).toEqual({ date: '2024-01-02', count: 1 })
        expect(result[2]).toEqual({ date: '2024-01-03', count: 1 })
      })

      it('should handle empty data arrays', () => {
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-03T23:59:59Z')
        const mockData: Array<{ timestamp: Date }> = []

        const aggregateByDay = (AnalyticsRepository as any).aggregateByDay
        const result = aggregateByDay(mockData, startDate, endDate)

        expect(result).toHaveLength(3) // 3 days with zero counts
        expect(result.every(item => item.count === 0)).toBe(true)
      })

      it('should handle invalid timestamps gracefully', () => {
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-02T23:59:59Z')
        const mockData = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: new Date('invalid-date') },
          { timestamp: new Date('2024-01-02T12:00:00Z') }
        ]

        const aggregateByDay = (AnalyticsRepository as any).aggregateByDay
        const result = aggregateByDay(mockData, startDate, endDate)

        expect(result).toHaveLength(2)
        expect(result[0]).toEqual({ date: '2024-01-01', count: 1 })
        expect(result[1]).toEqual({ date: '2024-01-02', count: 1 })
      })

      it('should handle date range edge cases', () => {
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-01T23:59:59Z') // Same day
        const mockData = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: new Date('2024-01-01T15:00:00Z') }
        ]

        const aggregateByDay = (AnalyticsRepository as any).aggregateByDay
        const result = aggregateByDay(mockData, startDate, endDate)

        expect(result).toHaveLength(1)
        expect(result[0]).toEqual({ date: '2024-01-01', count: 2 })
      })

      it('should handle large datasets efficiently', () => {
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-02T23:59:59Z')
        
        // Create a large dataset (1500 items to test chunking)
        const mockData = Array.from({ length: 1500 }, (_, i) => ({
          timestamp: new Date(`2024-01-0${(i % 2) + 1}T${String(i % 24).padStart(2, '0')}:00:00Z`)
        }))

        const aggregateByDay = (AnalyticsRepository as any).aggregateByDay
        const result = aggregateByDay(mockData, startDate, endDate)

        expect(result).toHaveLength(2)
        expect(result[0].count + result[1].count).toBe(1500)
      })
    })

    describe('calculateDateRange', () => {
      it('should calculate date range correctly', () => {
        const calculateDateRange = (AnalyticsRepository as any).calculateDateRange
        const result = calculateDateRange(7)

        expect(result.startDate).toBeInstanceOf(Date)
        expect(result.endDate).toBeInstanceOf(Date)
        expect(result.startDate.getTime()).toBeLessThan(result.endDate.getTime())
        
        // Should be approximately 7 days apart
        const daysDiff = Math.ceil((result.endDate.getTime() - result.startDate.getTime()) / (1000 * 60 * 60 * 24))
        expect(daysDiff).toBe(7)
      })

      it('should handle different period lengths', () => {
        const calculateDateRange = (AnalyticsRepository as any).calculateDateRange
        
        const result30 = calculateDateRange(30)
        const result90 = calculateDateRange(90)
        
        const daysDiff30 = Math.ceil((result30.endDate.getTime() - result30.startDate.getTime()) / (1000 * 60 * 60 * 24))
        const daysDiff90 = Math.ceil((result90.endDate.getTime() - result90.startDate.getTime()) / (1000 * 60 * 60 * 24))
        
        expect(daysDiff30).toBe(30)
        expect(daysDiff90).toBe(90)
      })
    })

    describe('sanitizeTimestampData', () => {
      it('should filter out invalid timestamp data', () => {
        const sanitizeTimestampData = (AnalyticsRepository as any).sanitizeTimestampData
        const mockData = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: null },
          { timestamp: new Date('invalid-date') },
          { timestamp: new Date('2024-01-02T12:00:00Z') },
          { notTimestamp: 'invalid' }
        ]

        const result = sanitizeTimestampData(mockData, 'test data')

        expect(result).toHaveLength(2)
        expect(result[0].timestamp).toEqual(new Date('2024-01-01T10:00:00Z'))
        expect(result[1].timestamp).toEqual(new Date('2024-01-02T12:00:00Z'))
      })

      it('should filter out timestamps outside reasonable bounds', () => {
        const sanitizeTimestampData = (AnalyticsRepository as any).sanitizeTimestampData
        const now = new Date()
        const twoYearsAgo = new Date(now.getFullYear() - 2, now.getMonth(), now.getDate())
        const futureDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate())
        const validDate = new Date()

        const mockData = [
          { timestamp: twoYearsAgo },
          { timestamp: futureDate },
          { timestamp: validDate }
        ]

        const result = sanitizeTimestampData(mockData, 'test data')

        expect(result).toHaveLength(1)
        expect(result[0].timestamp).toEqual(validDate)
      })

      it('should handle non-array input gracefully', () => {
        const sanitizeTimestampData = (AnalyticsRepository as any).sanitizeTimestampData
        
        const result1 = sanitizeTimestampData(null, 'test data')
        const result2 = sanitizeTimestampData('invalid', 'test data')
        const result3 = sanitizeTimestampData(123, 'test data')

        expect(result1).toEqual([])
        expect(result2).toEqual([])
        expect(result3).toEqual([])
      })
    })

    describe('processDataChunk', () => {
      it('should process data chunks correctly', () => {
        const processDataChunk = (AnalyticsRepository as any).processDataChunk
        const dataMap = new Map<string, number>()
        const chunk = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: new Date('2024-01-01T15:00:00Z') },
          { timestamp: new Date('2024-01-02T12:00:00Z') }
        ]

        processDataChunk(chunk, dataMap)

        expect(dataMap.get('2024-01-01')).toBe(2)
        expect(dataMap.get('2024-01-02')).toBe(1)
      })

      it('should skip invalid items silently', () => {
        const processDataChunk = (AnalyticsRepository as any).processDataChunk
        const dataMap = new Map<string, number>()
        const chunk = [
          { timestamp: new Date('2024-01-01T10:00:00Z') },
          { timestamp: null },
          { invalidItem: 'test' },
          { timestamp: new Date('2024-01-02T12:00:00Z') }
        ]

        processDataChunk(chunk, dataMap)

        expect(dataMap.get('2024-01-01')).toBe(1)
        expect(dataMap.get('2024-01-02')).toBe(1)
        expect(dataMap.size).toBe(2)
      })
    })

    describe('generateDateRange', () => {
      it('should generate complete date range', () => {
        const generateDateRange = (AnalyticsRepository as any).generateDateRange
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-03T23:59:59Z')

        const result = generateDateRange(startDate, endDate)

        expect(result).toEqual(['2024-01-01', '2024-01-02', '2024-01-03'])
      })

      it('should handle single day range', () => {
        const generateDateRange = (AnalyticsRepository as any).generateDateRange
        const startDate = new Date('2024-01-01T00:00:00Z')
        const endDate = new Date('2024-01-01T23:59:59Z')

        const result = generateDateRange(startDate, endDate)

        expect(result).toEqual(['2024-01-01'])
      })
    })
  })

  describe('Input Validation', () => {
    describe('trackProfileView', () => {
      it('should validate profile ID', async () => {
        await expect(AnalyticsRepository.trackProfileView('', mockMetadata))
          .rejects.toThrow()
        
        await expect(AnalyticsRepository.trackProfileView(null as any, mockMetadata))
          .rejects.toThrow()
      })

      it('should handle missing metadata gracefully', async () => {
        const mockCreate = jest.mocked(db.profileView.create)
        const mockUpdate = jest.mocked(db.profile.update)
        
        mockCreate.mockResolvedValue({} as any)
        mockUpdate.mockResolvedValue({} as any)

        await AnalyticsRepository.trackProfileView(mockProfileId, {})

        expect(mockCreate).toHaveBeenCalledWith({
          data: {
            profileId: mockProfileId,
            userAgent: undefined,
            ipHash: undefined,
            referrer: undefined,
            country: undefined
          }
        })
      })
    })

    describe('trackLinkClick', () => {
      it('should validate link ID and profile ID', async () => {
        await expect(AnalyticsRepository.trackLinkClick('', mockProfileId, mockMetadata))
          .rejects.toThrow()
        
        await expect(AnalyticsRepository.trackLinkClick(mockLinkId, '', mockMetadata))
          .rejects.toThrow()
      })
    })

    describe('getDashboardData', () => {
      it('should validate profile ID', async () => {
        await expect(AnalyticsRepository.getDashboardData('', 30))
          .rejects.toThrow()
        
        await expect(AnalyticsRepository.getDashboardData(null as any, 30))
          .rejects.toThrow()
      })

      it('should validate period days', async () => {
        await expect(AnalyticsRepository.getDashboardData(mockProfileId, 0))
          .rejects.toThrow()
        
        await expect(AnalyticsRepository.getDashboardData(mockProfileId, 400))
          .rejects.toThrow()
        
        await expect(AnalyticsRepository.getDashboardData(mockProfileId, -5))
          .rejects.toThrow()
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockRejectedValue(new Error('Connection failed'))

      await expect(AnalyticsRepository.trackProfileView(mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should handle database timeout errors', async () => {
      const mockCreate = jest.mocked(db.linkClick.create)
      mockCreate.mockRejectedValue(new Error('Connection timeout'))

      await expect(AnalyticsRepository.trackLinkClick(mockLinkId, mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should identify retryable errors correctly', () => {
      const isRetryableError = (AnalyticsRepository as any).isRetryableError
      
      expect(isRetryableError(new Error('Connection failed'))).toBe(true)
      expect(isRetryableError(new Error('Network timeout'))).toBe(true)
      expect(isRetryableError(new Error('ECONNRESET'))).toBe(true)
      expect(isRetryableError(new Error('Invalid data'))).toBe(false)
      expect(isRetryableError(new Error('Validation failed'))).toBe(false)
    })
  })

  describe('Privacy and Security', () => {
    it('should hash IP addresses for privacy', async () => {
      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockResolvedValue({} as any)
      
      const mockUpdate = jest.mocked(db.profile.update)
      mockUpdate.mockResolvedValue({} as any)

      await AnalyticsRepository.trackProfileView(mockProfileId, {
        ip: '***********',
        userAgent: 'Test Browser'
      })

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ipHash: expect.any(String),
          userAgent: 'Test Browser'
        })
      })

      // Verify IP is not stored directly
      const callArgs = mockCreate.mock.calls[0][0]
      expect(callArgs.data).not.toHaveProperty('ip')
      expect(callArgs.data.ipHash).not.toBe('***********')
    })

    it('should handle missing IP gracefully', async () => {
      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockResolvedValue({} as any)
      
      const mockUpdate = jest.mocked(db.profile.update)
      mockUpdate.mockResolvedValue({} as any)

      await AnalyticsRepository.trackProfileView(mockProfileId, {
        userAgent: 'Test Browser'
      })

      expect(mockCreate).toHaveBeenCalledWith({
        data: expect.objectContaining({
          ipHash: undefined,
          userAgent: 'Test Browser'
        })
      })
    })
  })

  describe('Performance Optimizations', () => {
    it('should handle large datasets with chunking', () => {
      const processDataChunk = (AnalyticsRepository as any).processDataChunk
      const dataMap = new Map<string, number>()
      
      // Create a large chunk (600 items)
      const largeChunk = Array.from({ length: 600 }, (_, i) => ({
        timestamp: new Date(`2024-01-01T${String(i % 24).padStart(2, '0')}:00:00Z`)
      }))

      // Should not throw and should process all items
      expect(() => processDataChunk(largeChunk, dataMap)).not.toThrow()
      expect(dataMap.get('2024-01-01')).toBe(600)
    })

    it('should validate aggregated data structure', () => {
      const validateAggregatedData = (AnalyticsRepository as any).validateAggregatedData
      
      const validData = [
        { date: '2024-01-01', count: 5 },
        { date: '2024-01-02', count: 3 },
        { date: '2024-01-03', count: 0 }
      ]
      
      const invalidData = [
        { date: '2024-01-01', count: 5 },
        { invalidStructure: true }
      ]

      expect(validateAggregatedData(validData, 3, 'Test')).toBe(true)
      expect(validateAggregatedData(invalidData, 2, 'Test')).toBe(false)
      expect(validateAggregatedData(validData, 5, 'Test')).toBe(false) // Wrong length
    })
  })
})