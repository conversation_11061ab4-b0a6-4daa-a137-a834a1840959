import { UserRepository } from '../user'

// Mock the database
jest.mock('../../db', () => ({
  db: {
    user: {
      findFirst: jest.fn(),
    }
  },
  handleDatabaseError: jest.fn((error) => { throw error })
}))

// Import after mocking
const { db } = require('../../db')
const mockDb = db as any

describe('UserRepository.isUsernameAvailable', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear console spies
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Case-insensitive behavior', () => {
    it('should check username availability with case-insensitive comparison', async () => {
      // Arrange
      const username = 'TestUser'
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(true)
      expect(mockDb.user.findFirst).toHaveBeenCalledWith({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })
    })

    it('should return false when username exists with different case', async () => {
      // Arrange
      const username = 'TestUser'
      const existingUser = { id: 'user-1', username: 'testuser' }
      mockDb.user.findFirst.mockResolvedValue(existingUser)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(false)
      expect(mockDb.user.findFirst).toHaveBeenCalledWith({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })
    })

    it('should return true when excluding the current user with same username', async () => {
      // Arrange
      const username = 'TestUser'
      const currentUserId = 'user-1'
      const existingUser = { id: currentUserId, username: 'testuser' }
      mockDb.user.findFirst.mockResolvedValue(existingUser)

      // Act
      const result = await UserRepository.isUsernameAvailable(username, currentUserId)

      // Assert
      expect(result).toBe(true)
    })

    it('should return false when excluding different user but username still exists', async () => {
      // Arrange
      const username = 'TestUser'
      const currentUserId = 'user-1'
      const existingUser = { id: 'user-2', username: 'testuser' }
      mockDb.user.findFirst.mockResolvedValue(existingUser)

      // Act
      const result = await UserRepository.isUsernameAvailable(username, currentUserId)

      // Assert
      expect(result).toBe(false)
    })
  })

  describe('Performance logging', () => {
    it('should log query performance for normal queries', async () => {
      // Arrange
      const username = 'TestUser'
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {})
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\[UserRepository\] Username availability check for "TestUser" completed in \d+ms/)
      )
    })

    it('should log performance warning for slow queries', async () => {
      // Arrange
      const username = 'TestUser'
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {})
      
      // Mock a slow query (delay the promise resolution)
      mockDb.user.findFirst.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(null), 1100))
      )

      // Act
      await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\[UserRepository\] Slow username availability query detected: \d+ms for username "TestUser"/)
      )
    })

    it('should log error with performance data when query fails', async () => {
      // Arrange
      const username = 'TestUser'
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      const testError = new Error('Database connection failed')
      mockDb.user.findFirst.mockRejectedValue(testError)

      // Act & Assert
      await expect(UserRepository.isUsernameAvailable(username)).rejects.toThrow()
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringMatching(/\[UserRepository\] Username availability check failed after \d+ms for username "TestUser":/),
        testError
      )
    })
  })

  describe('Timeout handling', () => {
    it('should timeout after 5 seconds', async () => {
      // Arrange
      const username = 'TestUser'
      
      // Mock a query that never resolves
      mockDb.user.findFirst.mockImplementation(() => 
        new Promise(() => {}) // Never resolves
      )

      // Act & Assert
      await expect(UserRepository.isUsernameAvailable(username)).rejects.toThrow(
        'Username availability check timed out after 5 seconds'
      )
    }, 6000) // Set test timeout to 6 seconds

    it('should complete successfully before timeout', async () => {
      // Arrange
      const username = 'TestUser'
      
      // Mock a query that resolves quickly
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const startTime = Date.now()
      const result = await UserRepository.isUsernameAvailable(username)
      const duration = Date.now() - startTime

      // Assert
      expect(result).toBe(true)
      expect(duration).toBeLessThan(5000)
    })

    it('should handle database errors before timeout', async () => {
      // Arrange
      const username = 'TestUser'
      const testError = new Error('Database connection failed')
      
      // Mock a query that fails quickly
      mockDb.user.findFirst.mockRejectedValue(testError)

      // Act & Assert
      await expect(UserRepository.isUsernameAvailable(username)).rejects.toThrow()
    })
  })

  describe('Edge cases', () => {
    it('should handle empty username', async () => {
      // Arrange
      const username = ''
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(true)
      expect(mockDb.user.findFirst).toHaveBeenCalledWith({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })
    })

    it('should handle special characters in username', async () => {
      // Arrange
      const username = 'test-user_123'
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(true)
      expect(mockDb.user.findFirst).toHaveBeenCalledWith({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })
    })

    it('should handle very long usernames', async () => {
      // Arrange
      const username = 'a'.repeat(100)
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(true)
    })

    it('should handle unicode characters in username', async () => {
      // Arrange
      const username = 'tëst-üser'
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const result = await UserRepository.isUsernameAvailable(username)

      // Assert
      expect(result).toBe(true)
      expect(mockDb.user.findFirst).toHaveBeenCalledWith({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })
    })
  })

  describe('Performance benchmarks', () => {
    it('should complete availability check within reasonable time for available username', async () => {
      // Arrange
      const username = 'TestUser'
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const startTime = Date.now()
      const result = await UserRepository.isUsernameAvailable(username)
      const duration = Date.now() - startTime

      // Assert
      expect(result).toBe(true)
      expect(duration).toBeLessThan(100) // Should complete within 100ms in test environment
    })

    it('should complete availability check within reasonable time for unavailable username', async () => {
      // Arrange
      const username = 'TestUser'
      const existingUser = { id: 'user-1', username: 'testuser' }
      mockDb.user.findFirst.mockResolvedValue(existingUser)

      // Act
      const startTime = Date.now()
      const result = await UserRepository.isUsernameAvailable(username)
      const duration = Date.now() - startTime

      // Assert
      expect(result).toBe(false)
      expect(duration).toBeLessThan(100) // Should complete within 100ms in test environment
    })
  })

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent username availability checks', async () => {
      // Arrange
      const usernames = ['user1', 'user2', 'user3', 'user4', 'user5']
      mockDb.user.findFirst.mockResolvedValue(null)

      // Act
      const promises = usernames.map(username => 
        UserRepository.isUsernameAvailable(username)
      )
      const results = await Promise.all(promises)

      // Assert
      expect(results).toEqual([true, true, true, true, true])
      expect(mockDb.user.findFirst).toHaveBeenCalledTimes(5)
    })

    it('should handle mixed results for concurrent checks', async () => {
      // Arrange
      const usernames = ['available1', 'taken1', 'available2', 'taken2']
      
      mockDb.user.findFirst.mockImplementation(({ where }) => {
        const username = where.username.equals
        if (username.includes('taken')) {
          return Promise.resolve({ id: 'user-1', username })
        }
        return Promise.resolve(null)
      })

      // Act
      const promises = usernames.map(username => 
        UserRepository.isUsernameAvailable(username)
      )
      const results = await Promise.all(promises)

      // Assert
      expect(results).toEqual([true, false, true, false])
    })
  })
})