import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import { AnalyticsRepository } from '../analytics'
import { db } from '../../db'

// Mock data for testing
const mockProfile = {
  id: 'test-profile-id',
  userId: 'test-user-id',
  slug: 'test-user',
  theme: {
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    fontFamily: 'Inter'
  },
  backgroundType: 'color',
  backgroundValue: '#ffffff',
  isPublic: true,
  viewCount: 0
}

const mockLink = {
  id: 'test-link-id',
  profileId: 'test-profile-id',
  title: 'Test Link',
  url: 'https://example.com',
  icon: null,
  isVisible: true,
  order: 0,
  clickCount: 0
}

describe('AnalyticsRepository', () => {
  beforeEach(async () => {
    // Clean up any existing test data
    await db.linkClick.deleteMany({ where: { profileId: mockProfile.id } })
    await db.profileView.deleteMany({ where: { profileId: mockProfile.id } })
    await db.link.deleteMany({ where: { profileId: mockProfile.id } })
    await db.profile.deleteMany({ where: { id: mockProfile.id } })
    await db.user.deleteMany({ where: { id: mockProfile.userId } })

    // Create test user and profile
    await db.user.create({
      data: {
        id: mockProfile.userId,
        email: '<EMAIL>',
        username: 'testuser',
        displayName: 'Test User'
      }
    })

    await db.profile.create({
      data: mockProfile
    })

    await db.link.create({
      data: mockLink
    })
  })

  afterEach(async () => {
    // Clean up test data
    await db.linkClick.deleteMany({ where: { profileId: mockProfile.id } })
    await db.profileView.deleteMany({ where: { profileId: mockProfile.id } })
    await db.link.deleteMany({ where: { profileId: mockProfile.id } })
    await db.profile.deleteMany({ where: { id: mockProfile.id } })
    await db.user.deleteMany({ where: { id: mockProfile.userId } })
  })

  describe('trackProfileView', () => {
    it('should track a profile view with metadata', async () => {
      const metadata = {
        userAgent: 'Mozilla/5.0 Test Browser',
        ip: '***********',
        referrer: 'https://google.com',
        country: 'US'
      }

      await AnalyticsRepository.trackProfileView(mockProfile.id, metadata)

      // Check that the view was recorded
      const views = await db.profileView.findMany({
        where: { profileId: mockProfile.id }
      })

      expect(views).toHaveLength(1)
      expect(views[0].userAgent).toBe(metadata.userAgent)
      expect(views[0].referrer).toBe(metadata.referrer)
      expect(views[0].country).toBe(metadata.country)
      expect(views[0].ipHash).toBeDefined() // Should be hashed

      // Check that the profile view count was incremented
      const profile = await db.profile.findUnique({
        where: { id: mockProfile.id }
      })
      expect(profile?.viewCount).toBe(1)
    })

    it('should handle missing metadata gracefully', async () => {
      await AnalyticsRepository.trackProfileView(mockProfile.id, {})

      const views = await db.profileView.findMany({
        where: { profileId: mockProfile.id }
      })

      expect(views).toHaveLength(1)
      expect(views[0].userAgent).toBeNull()
      expect(views[0].ipHash).toBeNull()
      expect(views[0].referrer).toBeNull()
      expect(views[0].country).toBeNull()
    })
  })

  describe('trackLinkClick', () => {
    it('should track a link click with metadata', async () => {
      const metadata = {
        userAgent: 'Mozilla/5.0 Test Browser',
        ip: '***********',
        referrer: 'https://example.com/profile',
        country: 'US'
      }

      await AnalyticsRepository.trackLinkClick(mockLink.id, mockProfile.id, metadata)

      // Check that the click was recorded
      const clicks = await db.linkClick.findMany({
        where: { linkId: mockLink.id }
      })

      expect(clicks).toHaveLength(1)
      expect(clicks[0].profileId).toBe(mockProfile.id)
      expect(clicks[0].userAgent).toBe(metadata.userAgent)
      expect(clicks[0].referrer).toBe(metadata.referrer)
      expect(clicks[0].country).toBe(metadata.country)
      expect(clicks[0].ipHash).toBeDefined() // Should be hashed

      // Check that the link click count was incremented
      const link = await db.link.findUnique({
        where: { id: mockLink.id }
      })
      expect(link?.clickCount).toBe(1)
    })
  })

  describe('getDashboardData', () => {
    it('should return comprehensive analytics data', async () => {
      // Create some test data
      await AnalyticsRepository.trackProfileView(mockProfile.id, {
        userAgent: 'Test Browser',
        ip: '***********',
        referrer: 'https://google.com'
      })

      await AnalyticsRepository.trackLinkClick(mockLink.id, mockProfile.id, {
        userAgent: 'Test Browser',
        ip: '***********'
      })

      const data = await AnalyticsRepository.getDashboardData(mockProfile.id, 30)

      expect(data.overview.totalViews).toBe(1)
      expect(data.overview.totalClicks).toBe(1)
      expect(data.overview.periodViews).toBe(1)
      expect(data.overview.periodClicks).toBe(1)
      expect(data.overview.clickRate).toBe(100) // 1 click / 1 view * 100

      expect(data.charts.viewsByDay).toBeDefined()
      expect(data.charts.clicksByDay).toBeDefined()
      expect(data.linkPerformance).toHaveLength(1)
      expect(data.linkPerformance[0].title).toBe(mockLink.title)
      expect(data.linkPerformance[0].totalClicks).toBe(1)

      expect(data.topReferrers).toHaveLength(1)
      expect(data.topReferrers[0].referrer).toBe('https://google.com')
      expect(data.topReferrers[0].count).toBe(1)
    })

    it('should return empty data for profile with no analytics', async () => {
      const data = await AnalyticsRepository.getDashboardData(mockProfile.id, 30)

      expect(data.overview.totalViews).toBe(0)
      expect(data.overview.totalClicks).toBe(0)
      expect(data.overview.periodViews).toBe(0)
      expect(data.overview.periodClicks).toBe(0)
      expect(data.overview.clickRate).toBe(0)

      expect(data.linkPerformance).toHaveLength(1) // Link exists but no clicks
      expect(data.linkPerformance[0].totalClicks).toBe(0)
      expect(data.topReferrers).toHaveLength(0)
    })
  })

  describe('getTopLinks', () => {
    it('should return top performing links', async () => {
      // Create multiple clicks
      for (let i = 0; i < 5; i++) {
        await AnalyticsRepository.trackLinkClick(mockLink.id, mockProfile.id, {})
      }

      const topLinks = await AnalyticsRepository.getTopLinks(mockProfile.id, 10)

      expect(topLinks).toHaveLength(1)
      expect(topLinks[0].title).toBe(mockLink.title)
      expect(topLinks[0].clickCount).toBe(5)
      expect(topLinks[0].recentClicks).toBe(5)
    })
  })
})