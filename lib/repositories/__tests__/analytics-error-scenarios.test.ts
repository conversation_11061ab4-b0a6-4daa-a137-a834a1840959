import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { AnalyticsRepository } from '../analytics'
import { db } from '../../db'
import { AnalyticsErrorFactory } from '../../errors/analytics-errors'

// Mock the database with error scenarios
jest.mock('../../db', () => ({
  db: {
    profileView: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn(),
      deleteMany: jest.fn()
    },
    linkClick: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      deleteMany: jest.fn()
    },
    profile: {
      update: jest.fn(),
      findUnique: jest.fn()
    },
    link: {
      update: jest.fn(),
      findMany: jest.fn()
    }
  },
  handleDatabaseError: jest.fn()
}))

// Mock the logger
jest.mock('../../utils/analytics-logger', () => ({
  analyticsLogger: {
    operationStart: jest.fn(),
    operationComplete: jest.fn(),
    operationFailed: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  },
  createTimer: jest.fn(() => ({
    end: jest.fn(() => 100)
  }))
}))

// Mock validation functions to test error scenarios
jest.mock('../../validations/analytics', () => ({
  isValidProfileId: jest.fn(),
  isValidPeriod: jest.fn(),
  sanitizeChartData: jest.fn(),
  sanitizeLinkPerformanceData: jest.fn(),
  sanitizeReferrerData: jest.fn(),
  validateResponseData: jest.fn()
}))

describe('AnalyticsRepository - Error Scenarios', () => {
  const mockProfileId = 'test-profile-id'
  const mockLinkId = 'test-link-id'
  const mockMetadata = {
    userAgent: 'Mozilla/5.0 Test Browser',
    ip: '***********',
    referrer: 'https://google.com',
    country: 'US'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Database Connection Errors', () => {
    it('should handle connection timeout in trackProfileView', async () => {
      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockRejectedValue(new Error('Connection timeout'))

      await expect(AnalyticsRepository.trackProfileView(mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should handle connection reset in trackLinkClick', async () => {
      const mockCreate = jest.mocked(db.linkClick.create)
      mockCreate.mockRejectedValue(new Error('ECONNRESET'))

      await expect(AnalyticsRepository.trackLinkClick(mockLinkId, mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should handle database unavailable in getDashboardData', async () => {
      const mockFindUnique = jest.mocked(db.profile.findUnique)
      mockFindUnique.mockRejectedValue(new Error('Database unavailable'))

      await expect(AnalyticsRepository.getDashboardData(mockProfileId, 30))
        .rejects.toThrow()
    })

    it('should handle network errors with retry logic', async () => {
      const mockFindUnique = jest.mocked(db.profile.findUnique)
      
      // First two calls fail, third succeeds
      mockFindUnique
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Connection failed'))
        .mockResolvedValueOnce({ viewCount: 100 } as any)

      // Mock other required database calls
      jest.mocked(db.profileView.findMany).mockResolvedValue([])
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Mock validation functions
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Should eventually succeed after retries
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })

    it('should fail after maximum retry attempts', async () => {
      const mockFindUnique = jest.mocked(db.profile.findUnique)
      
      // All calls fail
      mockFindUnique.mockRejectedValue(new Error('Persistent connection error'))

      // Mock validation functions
      const { isValidProfileId, isValidPeriod } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)

      await expect(AnalyticsRepository.getDashboardData(mockProfileId, 30))
        .rejects.toThrow()
    })
  })

  describe('Data Validation Errors', () => {
    it('should handle invalid profile ID', async () => {
      const { isValidProfileId } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(false)

      await expect(AnalyticsRepository.getDashboardData('', 30))
        .rejects.toThrow()
    })

    it('should handle invalid period', async () => {
      const { isValidProfileId, isValidPeriod } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(false)

      await expect(AnalyticsRepository.getDashboardData(mockProfileId, -5))
        .rejects.toThrow()
    })

    it('should handle profile not found', async () => {
      const { isValidProfileId, isValidPeriod } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)

      const mockFindUnique = jest.mocked(db.profile.findUnique)
      mockFindUnique.mockResolvedValue(null)

      await expect(AnalyticsRepository.getDashboardData(mockProfileId, 30))
        .rejects.toThrow()
    })

    it('should handle invalid date range', async () => {
      const { isValidProfileId, isValidPeriod } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)

      // Mock calculateDateRange to return invalid range
      const originalCalculateDateRange = (AnalyticsRepository as any).calculateDateRange
      ;(AnalyticsRepository as any).calculateDateRange = jest.fn(() => ({
        startDate: new Date('2024-01-02'),
        endDate: new Date('2024-01-01') // End before start
      }))

      await expect(AnalyticsRepository.getDashboardData(mockProfileId, 30))
        .rejects.toThrow()

      // Restore original method
      ;(AnalyticsRepository as any).calculateDateRange = originalCalculateDateRange
    })
  })

  describe('Data Processing Errors', () => {
    it('should handle corrupted timestamp data', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock database responses with corrupted data
      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue([
        { timestamp: null },
        { timestamp: 'invalid-date' },
        { timestamp: new Date('2024-01-01') }
      ] as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle corrupted data gracefully
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
      expect(result.overview.periodViews).toBe(1) // Only valid timestamp counted
    })

    it('should handle malformed database responses', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock database responses with unexpected structure
      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 'invalid' } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue('not-an-array' as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue(null as any)
      jest.mocked(db.link.findMany).mockResolvedValue([
        { id: 'link-1', title: null, clickCount: 'invalid' }
      ] as any)
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle malformed data gracefully
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })

    it('should handle aggregation failures', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock valid database responses
      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue([
        { timestamp: new Date('2024-01-01') }
      ] as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Mock aggregateByDay to throw an error
      const originalAggregateByDay = (AnalyticsRepository as any).aggregateByDay
      ;(AnalyticsRepository as any).aggregateByDay = jest.fn(() => {
        throw new Error('Aggregation failed')
      })

      // Should handle aggregation failure gracefully
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()

      // Restore original method
      ;(AnalyticsRepository as any).aggregateByDay = originalAggregateByDay
    })
  })

  describe('Memory and Performance Errors', () => {
    it('should handle out of memory errors with large datasets', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock extremely large dataset
      const largeDataset = Array.from({ length: 1000000 }, (_, i) => ({
        timestamp: new Date(`2024-01-01T${String(i % 24).padStart(2, '0')}:00:00Z`)
      }))

      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue(largeDataset as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle large datasets without crashing
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })

    it('should handle stack overflow in recursive operations', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock circular reference data that could cause stack overflow
      const circularData: any = { timestamp: new Date('2024-01-01') }
      circularData.self = circularData

      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue([circularData] as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle circular references gracefully
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })
  })

  describe('Concurrent Access Errors', () => {
    it('should handle database lock errors', async () => {
      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockRejectedValue(new Error('Database is locked'))

      await expect(AnalyticsRepository.trackProfileView(mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should handle concurrent modification errors', async () => {
      const mockUpdate = jest.mocked(db.profile.update)
      mockUpdate.mockRejectedValue(new Error('Record was modified by another process'))

      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockResolvedValue({} as any)

      await expect(AnalyticsRepository.trackProfileView(mockProfileId, mockMetadata))
        .rejects.toThrow()
    })

    it('should handle deadlock scenarios', async () => {
      const mockCreate = jest.mocked(db.linkClick.create)
      const mockUpdate = jest.mocked(db.link.update)
      
      mockCreate.mockResolvedValue({} as any)
      mockUpdate.mockRejectedValue(new Error('Deadlock detected'))

      await expect(AnalyticsRepository.trackLinkClick(mockLinkId, mockProfileId, mockMetadata))
        .rejects.toThrow()
    })
  })

  describe('Security and Privacy Errors', () => {
    it('should handle IP hashing failures', async () => {
      // Mock crypto.createHash to fail
      const originalCreateHash = require('crypto').createHash
      require('crypto').createHash = jest.fn(() => {
        throw new Error('Crypto operation failed')
      })

      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockResolvedValue({} as any)
      
      const mockUpdate = jest.mocked(db.profile.update)
      mockUpdate.mockResolvedValue({} as any)

      await expect(AnalyticsRepository.trackProfileView(mockProfileId, {
        ...mockMetadata,
        ip: '***********'
      })).rejects.toThrow()

      // Restore original
      require('crypto').createHash = originalCreateHash
    })

    it('should handle malicious input data', async () => {
      const maliciousMetadata = {
        userAgent: '<script>alert("xss")</script>',
        ip: '../../etc/passwd',
        referrer: 'javascript:alert("xss")',
        country: '\x00\x01\x02'
      }

      const mockCreate = jest.mocked(db.profileView.create)
      mockCreate.mockResolvedValue({} as any)
      
      const mockUpdate = jest.mocked(db.profile.update)
      mockUpdate.mockResolvedValue({} as any)

      // Should handle malicious input without crashing
      await expect(AnalyticsRepository.trackProfileView(mockProfileId, maliciousMetadata))
        .resolves.not.toThrow()
    })
  })

  describe('Response Validation Errors', () => {
    it('should handle response validation failures', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation(() => {
        throw new Error('Response validation failed')
      })

      // Mock successful database responses
      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue([])
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should return fallback data when validation fails
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
      expect(result.overview.totalViews).toBeGreaterThanOrEqual(0)
    })

    it('should handle sanitization failures', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData, sanitizeChartData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)
      sanitizeChartData.mockImplementation(() => {
        throw new Error('Sanitization failed')
      })

      // Mock successful database responses
      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue([
        { timestamp: new Date('2024-01-01') }
      ] as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle sanitization failure gracefully
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })
  })

  describe('Edge Case Scenarios', () => {
    it('should handle timezone edge cases', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock data with various timezone issues
      const timezoneData = [
        { timestamp: new Date('2024-01-01T00:00:00+14:00') }, // UTC+14
        { timestamp: new Date('2024-01-01T00:00:00-12:00') }, // UTC-12
        { timestamp: new Date('2024-02-29T12:00:00Z') }, // Leap year
        { timestamp: new Date('2024-12-31T23:59:59Z') } // Year boundary
      ]

      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue(timezoneData as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle timezone edge cases
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
      expect(result.charts.viewsByDay).toEqual(expect.any(Array))
    })

    it('should handle leap year and DST transitions', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock data around DST transition
      const dstData = [
        { timestamp: new Date('2024-03-10T01:59:59Z') }, // Before DST
        { timestamp: new Date('2024-03-10T03:00:00Z') }, // After DST
        { timestamp: new Date('2024-11-03T01:59:59Z') }, // Before DST end
        { timestamp: new Date('2024-11-03T01:00:00Z') } // After DST end
      ]

      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue(dstData as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle DST transitions
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })

    it('should handle extreme date ranges', async () => {
      const { isValidProfileId, isValidPeriod, validateResponseData } = require('../../validations/analytics')
      isValidProfileId.mockReturnValue(true)
      isValidPeriod.mockReturnValue(true)
      validateResponseData.mockImplementation((data) => data)

      // Mock data with extreme dates
      const extremeData = [
        { timestamp: new Date('1970-01-01T00:00:00Z') }, // Unix epoch
        { timestamp: new Date('2038-01-19T03:14:07Z') }, // 32-bit timestamp limit
        { timestamp: new Date('9999-12-31T23:59:59Z') } // Far future
      ]

      jest.mocked(db.profile.findUnique).mockResolvedValue({ viewCount: 100 } as any)
      jest.mocked(db.profileView.findMany).mockResolvedValue(extremeData as any)
      jest.mocked(db.linkClick.findMany).mockResolvedValue([])
      jest.mocked(db.link.findMany).mockResolvedValue([])
      jest.mocked(db.profileView.groupBy).mockResolvedValue([])

      // Should handle extreme dates (may filter them out)
      const result = await AnalyticsRepository.getDashboardData(mockProfileId, 30)
      expect(result).toBeDefined()
    })
  })
})