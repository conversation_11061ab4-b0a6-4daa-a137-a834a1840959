/**
 * Repository Usage Examples
 * 
 * This file demonstrates how to use the repository classes.
 * To run actual tests, set up Jest and create proper test files.
 */

import { UserRepository, ProfileRepository, LinkRepository } from '../index'
import type { CreateUserData, CreateProfileData, CreateLinkData } from '../index'

// Example usage of repositories
export async function repositoryExamples() {
  try {
    // User Repository Examples
    console.log('=== User Repository Examples ===')
    
    // Create a user
    const userData: CreateUserData = {
      email: '<EMAIL>',
      username: 'johndo<PERSON>',
      displayName: '<PERSON>',
      bio: 'Software developer',
      password: 'hashedpassword123'
    }
    
    // Check if username is available
    const isUsernameAvailable = await UserRepository.isUsernameAvailable(userData.username)
    console.log('Username available:', isUsernameAvailable)
    
    if (isUsernameAvailable) {
      const user = await UserRepository.create(userData)
      console.log('Created user:', user.id)
      
      // Find user by email
      const foundUser = await UserRepository.findByEmail(userData.email)
      console.log('Found user by email:', foundUser?.displayName)
      
      // Profile Repository Examples
      console.log('\n=== Profile Repository Examples ===')
      
      const profileData: CreateProfileData = {
        userId: user.id,
        slug: userData.username,
        theme: {
          primaryColor: '#000000',
          secondaryColor: '#666666',
          backgroundColor: '#ffffff',
          textColor: '#000000',
          fontFamily: 'Inter'
        },
        backgroundType: 'color',
        backgroundValue: '#ffffff',
        isPublic: true
      }
      
      // Check if slug is available
      const isSlugAvailable = await ProfileRepository.isSlugAvailable(profileData.slug)
      console.log('Slug available:', isSlugAvailable)
      
      if (isSlugAvailable) {
        const profile = await ProfileRepository.create(profileData)
        console.log('Created profile:', profile.id)
        
        // Link Repository Examples
        console.log('\n=== Link Repository Examples ===')
        
        const linkData: CreateLinkData = {
          title: 'My Website',
          url: 'https://johndoe.com',
          icon: 'globe',
          isVisible: true
        }
        
        const link = await LinkRepository.create(profile.id, linkData)
        console.log('Created link:', link.id)
        
        // Create another link
        const link2 = await LinkRepository.create(profile.id, {
          title: 'GitHub',
          url: 'https://github.com/johndoe',
          icon: 'github',
          isVisible: true
        })
        
        // Get all links for profile
        const links = await LinkRepository.findByProfileId(profile.id)
        console.log('Profile links:', links.length)
        
        // Reorder links
        const reorderedLinks = await LinkRepository.reorder(profile.id, [link2.id, link.id])
        console.log('Reordered links:', reorderedLinks.map(l => l.title))
        
        // Increment click count
        await LinkRepository.incrementClickCount(link.id)
        console.log('Incremented click count for link')
        
        // Get profile with links
        const profileWithLinks = await ProfileRepository.findWithLinks(profile.id)
        console.log('Profile with links:', profileWithLinks?.links.length)
        
        // Clean up (in reverse order due to foreign key constraints)
        await LinkRepository.delete(link.id)
        await LinkRepository.delete(link2.id)
        await ProfileRepository.delete(profile.id)
      }
      
      await UserRepository.delete(user.id)
      console.log('\nCleanup completed')
    }
    
  } catch (error) {
    console.error('Repository example error:', error)
  }
}

// Test data validation
export function testValidationExamples() {
  console.log('=== Validation Examples ===')
  
  // These would throw validation errors:
  try {
    const invalidUserData = {
      email: 'invalid-email', // Invalid email format
      username: 'ab', // Too short
      displayName: '', // Required field empty
    }
    console.log('This would fail validation:', invalidUserData)
  } catch (error) {
    console.log('Validation error caught:', error)
  }
}

// Helper function to run tests
export async function runRepositoryTests() {
  console.log('Repository tests would run here in a proper test environment')
  console.log('To run these tests, set up Jest and run: npm test')
}