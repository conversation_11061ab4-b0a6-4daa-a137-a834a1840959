import { Link, Prisma } from '@prisma/client'
import { db, handleDatabaseError, NotFoundError, withTransaction } from '../db'
import { createLinkSchema, updateLinkSchema, type CreateLinkData, type UpdateLinkData } from '../validations'

export class LinkRepository {
  /**
   * Create a new link
   */
  static async create(profileId: string, data: CreateLinkData): Promise<Link> {
    try {
      // Validate input data
      const validatedData = createLinkSchema.parse(data)

      // Get the next order number if not provided
      let order = validatedData.order
      if (order === undefined) {
        const lastLink = await db.link.findFirst({
          where: { profileId },
          orderBy: { order: 'desc' }
        })
        order = (lastLink?.order ?? -1) + 1
      }

      return await db.link.create({
        data: {
          ...validatedData,
          profileId,
          order
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find link by ID
   */
  static async findById(id: string): Promise<Link | null> {
    try {
      return await db.link.findUnique({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find link by ID or throw error
   */
  static async findByIdOrThrow(id: string): Promise<Link> {
    try {
      const link = await db.link.findUnique({
        where: { id }
      })

      if (!link) {
        throw new NotFoundError('Link', id)
      }

      return link
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find all links for a profile
   */
  static async findByProfileId(profileId: string, includeHidden = false): Promise<Link[]> {
    try {
      const whereClause: Prisma.LinkWhereInput = { profileId }
      if (!includeHidden) {
        whereClause.isVisible = true
      }

      return await db.link.findMany({
        where: whereClause,
        orderBy: { order: 'asc' }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update link
   */
  static async update(id: string, data: UpdateLinkData): Promise<Link> {
    try {
      // Validate input data
      const validatedData = updateLinkSchema.parse(data)

      // Check if link exists
      const existingLink = await this.findById(id)
      if (!existingLink) {
        throw new NotFoundError('Link', id)
      }

      return await db.link.update({
        where: { id },
        data: validatedData
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Delete link
   */
  static async delete(id: string): Promise<Link> {
    try {
      // Check if link exists
      const existingLink = await this.findById(id)
      if (!existingLink) {
        throw new NotFoundError('Link', id)
      }

      return await withTransaction(async (tx) => {
        // Delete the link
        const deletedLink = await tx.link.delete({
          where: { id }
        })

        // Reorder remaining links
        await tx.link.updateMany({
          where: {
            profileId: existingLink.profileId,
            order: { gt: existingLink.order }
          },
          data: {
            order: { decrement: 1 }
          }
        })

        return deletedLink
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Reorder links
   */
  static async reorder(profileId: string, linkIds: string[]): Promise<Link[]> {
    try {
      // Verify all links belong to the profile
      const existingLinks = await db.link.findMany({
        where: {
          profileId,
          id: { in: linkIds }
        }
      })

      if (existingLinks.length !== linkIds.length) {
        throw new NotFoundError('One or more links not found or do not belong to this profile')
      }

      return await withTransaction(async (tx) => {
        const updatedLinks: Link[] = []

        for (let i = 0; i < linkIds.length; i++) {
          const updatedLink = await tx.link.update({
            where: { id: linkIds[i] },
            data: { order: i }
          })
          updatedLinks.push(updatedLink)
        }

        return updatedLinks
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Toggle link visibility
   */
  static async toggleVisibility(id: string): Promise<Link> {
    try {
      const link = await this.findByIdOrThrow(id)
      
      return await db.link.update({
        where: { id },
        data: {
          isVisible: !link.isVisible
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Increment click count
   */
  static async incrementClickCount(id: string): Promise<Link> {
    try {
      return await db.link.update({
        where: { id },
        data: {
          clickCount: {
            increment: 1
          }
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get link analytics
   */
  static async getAnalytics(profileId: string): Promise<Array<{
    id: string
    title: string
    url: string
    clickCount: number
    isVisible: boolean
  }>> {
    try {
      return await db.link.findMany({
        where: { profileId },
        select: {
          id: true,
          title: true,
          url: true,
          clickCount: true,
          isVisible: true
        },
        orderBy: { order: 'asc' }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Bulk update links
   */
  static async bulkUpdate(updates: Array<{ id: string; data: UpdateLinkData }>): Promise<Link[]> {
    try {
      return await withTransaction(async (tx) => {
        const updatedLinks: Link[] = []

        for (const update of updates) {
          // Validate each update
          const validatedData = updateLinkSchema.parse(update.data)
          
          const updatedLink = await tx.link.update({
            where: { id: update.id },
            data: validatedData
          })
          
          updatedLinks.push(updatedLink)
        }

        return updatedLinks
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Move link to position
   */
  static async moveToPosition(id: string, newPosition: number): Promise<Link[]> {
    try {
      const link = await this.findByIdOrThrow(id)
      const allLinks = await this.findByProfileId(link.profileId, true)
      
      // Remove the link from its current position
      const filteredLinks = allLinks.filter(l => l.id !== id)
      
      // Insert the link at the new position
      filteredLinks.splice(newPosition, 0, link)
      
      // Get the new order of link IDs
      const newOrder = filteredLinks.map(l => l.id)
      
      return await this.reorder(link.profileId, newOrder)
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Duplicate link
   */
  static async duplicate(id: string): Promise<Link> {
    try {
      const originalLink = await this.findByIdOrThrow(id)
      
      // Create a copy with modified title
      const duplicateData: CreateLinkData = {
        title: `${originalLink.title} (Copy)`,
        url: originalLink.url,
        icon: originalLink.icon || undefined,
        isVisible: originalLink.isVisible
      }

      return await this.create(originalLink.profileId, duplicateData)
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Get total click count for profile
   */
  static async getTotalClickCount(profileId: string): Promise<number> {
    try {
      const result = await db.link.aggregate({
        where: { profileId },
        _sum: {
          clickCount: true
        }
      })

      return result._sum.clickCount || 0
    } catch (error) {
      return handleDatabaseError(error)
    }
  }
}