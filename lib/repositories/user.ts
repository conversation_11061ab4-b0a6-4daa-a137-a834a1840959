import { User, Prisma } from '@prisma/client'
import { db, handleDatabaseError, NotFoundError, ConflictError } from '../db'
import { createUserSchema, updateUserSchema, type CreateUserData, type UpdateUserData } from '../validations'
import type { UserWithProfile } from '../types'

export class UserRepository {
  /**
   * Create a new user
   */
  static async create(data: CreateUserData): Promise<User> {
    try {
      // Validate input data
      const validatedData = createUserSchema.parse(data)
      
      // Check if user already exists
      const existingUser = await db.user.findFirst({
        where: {
          OR: [
            { email: validatedData.email },
            { username: validatedData.username }
          ]
        }
      })

      if (existingUser) {
        if (existingUser.email === validatedData.email) {
          throw new ConflictError('A user with this email already exists', 'email')
        }
        if (existingUser.username === validatedData.username) {
          throw new ConflictError('A user with this username already exists', 'username')
        }
      }

      return await db.user.create({
        data: validatedData
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user by ID
   */
  static async findById(id: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user by ID or throw error
   */
  static async findByIdOrThrow(id: string): Promise<User> {
    try {
      const user = await db.user.findUnique({
        where: { id }
      })

      if (!user) {
        throw new NotFoundError('User', id)
      }

      return user
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user by email
   */
  static async findByEmail(email: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { email }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user by username
   */
  static async findByUsername(username: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { username }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user with profile
   */
  static async findWithProfile(id: string): Promise<UserWithProfile | null> {
    try {
      return await db.user.findUnique({
        where: { id },
        include: {
          profile: true
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update user
   */
  static async update(id: string, data: UpdateUserData): Promise<User> {
    try {
      // Validate input data
      const validatedData = updateUserSchema.parse(data)

      // Check if user exists
      const existingUser = await this.findById(id)
      if (!existingUser) {
        throw new NotFoundError('User', id)
      }

      // Check for conflicts if updating email or username
      if (validatedData.email || validatedData.username) {
        const conflictUser = await db.user.findFirst({
          where: {
            AND: [
              { id: { not: id } }, // Exclude current user
              {
                OR: [
                  validatedData.email ? { email: validatedData.email } : {},
                  validatedData.username ? { username: validatedData.username } : {}
                ].filter(condition => Object.keys(condition).length > 0)
              }
            ]
          }
        })

        if (conflictUser) {
          if (conflictUser.email === validatedData.email) {
            throw new ConflictError('A user with this email already exists', 'email')
          }
          if (conflictUser.username === validatedData.username) {
            throw new ConflictError('A user with this username already exists', 'username')
          }
        }
      }

      return await db.user.update({
        where: { id },
        data: validatedData
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Delete user
   */
  static async delete(id: string): Promise<User> {
    try {
      // Check if user exists
      const existingUser = await this.findById(id)
      if (!existingUser) {
        throw new NotFoundError('User', id)
      }

      return await db.user.delete({
        where: { id }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Check if username is available
   */
  static async isUsernameAvailable(username: string, excludeUserId?: string): Promise<boolean> {
    const startTime = Date.now()
    
    try {
      // Create a timeout promise that rejects after 5 seconds
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Username availability check timed out after 5 seconds'))
        }, 5000)
      })

      // Race the database query against the timeout
      const queryPromise = db.user.findFirst({
        where: {
          username: {
            equals: username,
            mode: 'insensitive'
          }
        }
      })

      const existingUser = await Promise.race([queryPromise, timeoutPromise])
      
      // Log query performance
      const duration = Date.now() - startTime
      console.log(`[UserRepository] Username availability check for "${username}" completed in ${duration}ms`)
      
      // Log performance warning if query is slow
      if (duration > 1000) {
        console.warn(`[UserRepository] Slow username availability query detected: ${duration}ms for username "${username}"`)
      }

      if (!existingUser) {
        return true
      }

      // If excluding a user ID, check if the existing user is the excluded one
      return excludeUserId ? existingUser.id === excludeUserId : false
    } catch (error) {
      // Log error with performance data
      const duration = Date.now() - startTime
      console.error(`[UserRepository] Username availability check failed after ${duration}ms for username "${username}":`, error)
      return handleDatabaseError(error)
    }
  }

  /**
   * Check if email is available
   */
  static async isEmailAvailable(email: string, excludeUserId?: string): Promise<boolean> {
    try {
      const existingUser = await db.user.findUnique({
        where: { email }
      })

      if (!existingUser) {
        return true
      }

      // If excluding a user ID, check if the existing user is the excluded one
      return excludeUserId ? existingUser.id === excludeUserId : false
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Update user password
   */
  static async updatePassword(id: string, hashedPassword: string): Promise<User> {
    try {
      // Check if user exists
      const existingUser = await this.findById(id)
      if (!existingUser) {
        throw new NotFoundError('User', id)
      }

      return await db.user.update({
        where: { id },
        data: { password: hashedPassword }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Set password reset token
   */
  static async setResetToken(email: string, token: string, expiry: Date): Promise<User> {
    try {
      const user = await this.findByEmail(email)
      if (!user) {
        throw new NotFoundError('User', email)
      }

      return await db.user.update({
        where: { email },
        data: {
          resetToken: token,
          resetTokenExpiry: expiry
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Find user by reset token
   */
  static async findByResetToken(token: string): Promise<User | null> {
    try {
      return await db.user.findFirst({
        where: {
          resetToken: token,
          resetTokenExpiry: {
            gt: new Date()
          }
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }

  /**
   * Clear reset token
   */
  static async clearResetToken(id: string): Promise<User> {
    try {
      return await db.user.update({
        where: { id },
        data: {
          resetToken: null,
          resetTokenExpiry: null
        }
      })
    } catch (error) {
      return handleDatabaseError(error)
    }
  }
}