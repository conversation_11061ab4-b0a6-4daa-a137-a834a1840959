"use server"

import { ProfileRepository } from '@/lib/repositories/profile'
import { LinkRepository } from '@/lib/repositories/link'

/**
 * Track a profile view (server-side fallback)
 */
export async function trackProfileView(profileId: string): Promise<void> {
  try {
    await ProfileRepository.incrementViewCount(profileId)
  } catch (error) {
    console.error('Failed to track profile view:', error)
    // Don't throw error to avoid breaking the user experience
  }
}

/**
 * Track a link click (server-side fallback)
 */
export async function trackLinkClick(linkId: string): Promise<void> {
  try {
    await LinkRepository.incrementClickCount(linkId)
  } catch (error) {
    console.error('Failed to track link click:', error)
    // Don't throw error to avoid breaking the user experience
  }
}

/**
 * Client-side tracking function for profile views
 */
export async function trackProfileViewClient(profileId: string): Promise<void> {
  try {
    await fetch('/api/analytics/views', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ profileId }),
    })
  } catch (error) {
    console.error('Failed to track profile view:', error)
    // Fallback to server action
    await trackProfileView(profileId)
  }
}

/**
 * Client-side tracking function for link clicks
 */
export async function trackLinkClickClient(linkId: string): Promise<void> {
  try {
    await fetch('/api/analytics/clicks', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ linkId }),
    })
  } catch (error) {
    console.error('Failed to track link click:', error)
    // Fallback to server action
    await trackLinkClick(linkId)
  }
}