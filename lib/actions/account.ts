"use server"

import { auth } from "@/auth"
import { UserRepository } from "@/lib/repositories/user"
import { ProfileRepository } from "@/lib/repositories/profile"
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { usernameSchema } from "@/lib/validations"

export interface AccountActionResult {
  success: boolean
  error?: string
  data?: unknown
}

/**
 * Check if username is available
 */
export async function checkUsernameAvailability(username: string): Promise<AccountActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Validate username format
    const validatedUsername = usernameSchema.parse(username)
    
    // Check availability
    const isAvailable = await UserRepository.isUsernameAvailable(validatedUsername, session.user.id)
    
    return { 
      success: true, 
      data: { 
        username: validatedUsername,
        available: isAvailable 
      }
    }
  } catch (error) {
    console.error('Username availability check error:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.issues.map((issue) => issue.message).join(', ')
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to check username availability'
    }
  }
}

/**
 * Update username and regenerate profile slug
 */
export async function updateUsername(username: string): Promise<AccountActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Validate username
    const validatedUsername = usernameSchema.parse(username)
    
    // Check if username is available
    const isAvailable = await UserRepository.isUsernameAvailable(validatedUsername, session.user.id)
    if (!isAvailable) {
      return { success: false, error: "Username is already taken" }
    }
    
    // Get current user with profile
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    if (!userWithProfile) {
      return { success: false, error: "User not found" }
    }

    const oldSlug = userWithProfile.profile?.slug
    
    // Update user
    const updatedUser = await UserRepository.update(session.user.id, {
      username: validatedUsername
    })
    
    // Update profile slug if profile exists
    if (userWithProfile.profile) {
      await ProfileRepository.update(userWithProfile.profile.id, {
        slug: validatedUsername
      })
    }

    // Revalidate pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/settings')
    revalidatePath('/dashboard/profile')
    
    // Revalidate old and new public profile URLs
    if (oldSlug) {
      revalidatePath(`/${oldSlug}`)
    }
    revalidatePath(`/${validatedUsername}`)

    return { 
      success: true, 
      data: { 
        username: updatedUser.username,
        oldSlug,
        newSlug: validatedUsername
      }
    }
  } catch (error) {
    console.error('Username update error:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.issues.map((issue) => issue.message).join(', ')
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update username'
    }
  }
}

/**
 * Export user data in JSON format
 */
export async function exportUserData(): Promise<AccountActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Get user with profile
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    if (!userWithProfile) {
      return { success: false, error: "User not found" }
    }

    // Get profile with links if profile exists
    let profileWithLinks = null
    if (userWithProfile.profile) {
      profileWithLinks = await ProfileRepository.findWithLinks(userWithProfile.profile.id)
    }

    // Prepare export data
    const exportData = {
      user: {
        id: userWithProfile.id,
        email: userWithProfile.email,
        username: userWithProfile.username,
        displayName: userWithProfile.displayName,
        bio: userWithProfile.bio,
        profileImage: userWithProfile.profileImage,
        createdAt: userWithProfile.createdAt,
        updatedAt: userWithProfile.updatedAt,
      },
      profile: profileWithLinks ? {
        id: profileWithLinks.id,
        slug: profileWithLinks.slug,
        theme: profileWithLinks.theme,
        backgroundType: profileWithLinks.backgroundType,
        backgroundValue: profileWithLinks.backgroundValue,
        isPublic: profileWithLinks.isPublic,
        viewCount: profileWithLinks.viewCount,
        createdAt: profileWithLinks.createdAt,
        updatedAt: profileWithLinks.updatedAt,
      } : null,
      links: profileWithLinks?.links.map((link) => ({
        id: link.id,
        title: link.title,
        url: link.url,
        icon: link.icon,
        isVisible: link.isVisible,
        order: link.order,
        clickCount: link.clickCount,
        createdAt: link.createdAt,
        updatedAt: link.updatedAt,
      })) || [],
      exportedAt: new Date().toISOString(),
    }

    return { 
      success: true, 
      data: exportData
    }
  } catch (error) {
    console.error('Data export error:', error)
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to export data'
    }
  }
}

/**
 * Delete user account and all associated data
 */
export async function deleteAccount(): Promise<AccountActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Get user with profile to get slug for revalidation
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    const slug = userWithProfile?.profile?.slug

    // Delete user (cascade will handle profile and links)
    await UserRepository.delete(session.user.id)

    // Revalidate pages
    revalidatePath('/dashboard')
    if (slug) {
      revalidatePath(`/${slug}`)
    }

    return { success: true }
  } catch (error) {
    console.error('Account deletion error:', error)
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete account'
    }
  }
}