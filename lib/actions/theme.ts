"use server"

import { auth } from "@/auth"
import { ProfileRepository } from "@/lib/repositories/profile"
import { UserRepository } from "@/lib/repositories/user"
import { revalidatePath } from "next/cache"
import { z } from "zod"
import type { ProfileTheme } from "@/lib/types"

// Theme update schema
const themeUpdateSchema = z.object({
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid primary color format'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid secondary color format'),
    backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid background color format'),
    textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid text color format'),
    fontFamily: z.string().min(1, 'Font family is required'),
    preset: z.string().optional(),
  }),
  backgroundType: z.enum(['color', 'gradient', 'image']).optional(),
  backgroundValue: z.string().optional(),
})

export type ThemeUpdateData = z.infer<typeof themeUpdateSchema>

export interface ThemeActionResult {
  success: boolean
  error?: string
  data?: any
}

/**
 * Update user's theme settings
 */
export async function updateTheme(data: ThemeUpdateData): Promise<ThemeActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Validate the input data
    const validatedData = themeUpdateSchema.parse(data)
    
    // Get user's profile or create one if it doesn't exist
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    
    if (!userWithProfile) {
      return { success: false, error: "User not found" }
    }

    let profileId = userWithProfile.profile?.id

    // Create profile if it doesn't exist
    if (!userWithProfile.profile) {
      const newProfile = await ProfileRepository.create({
        userId: session.user.id,
        slug: userWithProfile.username,
        theme: validatedData.theme,
        backgroundType: validatedData.backgroundType || 'color',
        backgroundValue: validatedData.backgroundValue || validatedData.theme.backgroundColor,
        isPublic: true,
      })
      profileId = newProfile.id
    } else {
      // Update existing profile
      await ProfileRepository.update(userWithProfile.profile.id, {
        theme: validatedData.theme,
        backgroundType: validatedData.backgroundType,
        backgroundValue: validatedData.backgroundValue,
      })
    }

    // Revalidate relevant pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/customize')
    
    if (userWithProfile.profile?.slug) {
      revalidatePath(`/${userWithProfile.profile.slug}`)
    }

    return { 
      success: true, 
      data: {
        theme: validatedData.theme,
        backgroundType: validatedData.backgroundType,
        backgroundValue: validatedData.backgroundValue,
      }
    }
  } catch (error) {
    console.error('Theme update error:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.issues.map((issue: any) => issue.message).join(', ')
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update theme'
    }
  }
}

/**
 * Apply a theme preset
 */
export async function applyThemePreset(presetName: string): Promise<ThemeActionResult> {
  const presets: Record<string, ProfileTheme> = {
    default: {
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      fontFamily: 'Inter',
      preset: 'default'
    },
    dark: {
      primaryColor: '#60a5fa',
      secondaryColor: '#94a3b8',
      backgroundColor: '#1f2937',
      textColor: '#f9fafb',
      fontFamily: 'Inter',
      preset: 'dark'
    },
    colorful: {
      primaryColor: '#ec4899',
      secondaryColor: '#8b5cf6',
      backgroundColor: '#fef3c7',
      textColor: '#1f2937',
      fontFamily: 'Poppins',
      preset: 'colorful'
    },
    minimal: {
      primaryColor: '#000000',
      secondaryColor: '#6b7280',
      backgroundColor: '#f9fafb',
      textColor: '#111827',
      fontFamily: 'Inter',
      preset: 'minimal'
    },
    ocean: {
      primaryColor: '#0891b2',
      secondaryColor: '#0284c7',
      backgroundColor: '#f0f9ff',
      textColor: '#0c4a6e',
      fontFamily: 'Inter',
      preset: 'ocean'
    },
    forest: {
      primaryColor: '#059669',
      secondaryColor: '#047857',
      backgroundColor: '#f0fdf4',
      textColor: '#064e3b',
      fontFamily: 'Inter',
      preset: 'forest'
    }
  }

  const theme = presets[presetName]
  if (!theme) {
    return { success: false, error: "Invalid preset name" }
  }

  return updateTheme({
    theme,
    backgroundType: 'color',
    backgroundValue: theme.backgroundColor,
  })
}

/**
 * Get current user's theme settings
 */
export async function getCurrentTheme(): Promise<ThemeActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    
    if (!userWithProfile?.profile) {
      // Return default theme if no profile exists
      return {
        success: true,
        data: {
          theme: {
            primaryColor: '#3b82f6',
            secondaryColor: '#64748b',
            backgroundColor: '#ffffff',
            textColor: '#1f2937',
            fontFamily: 'Inter',
          },
          backgroundType: 'color',
          backgroundValue: '#ffffff',
        }
      }
    }

    return {
      success: true,
      data: {
        theme: userWithProfile.profile.theme,
        backgroundType: userWithProfile.profile.backgroundType,
        backgroundValue: userWithProfile.profile.backgroundValue,
      }
    }
  } catch (error) {
    console.error('Get theme error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to get theme'
    }
  }
}