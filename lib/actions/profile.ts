"use server"

import { auth } from "@/auth"
import { UserRepository } from "@/lib/repositories/user"
import { ProfileRepository } from "@/lib/repositories/profile"
import { revalidatePath } from "next/cache"
import { z } from "zod"

// Profile update schema
const profileUpdateSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional().nullable(),
  profileImage: z.string().min(1).optional().nullable(),
})

export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>

export interface ProfileActionResult {
  success: boolean
  error?: string
  data?: any
}

/**
 * Update user profile information
 */
export async function updateProfile(data: ProfileUpdateData): Promise<ProfileActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Validate the input data
    const validatedData = profileUpdateSchema.parse(data)
    
    // Update user information
    const updatedUser = await UserRepository.update(session.user.id, {
      displayName: validatedData.displayName,
      bio: validatedData.bio === '' ? null : validatedData.bio,
      profileImage: validatedData.profileImage,
    })

    // Revalidate relevant pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/profile')
    
    // If user has a profile, revalidate their public profile too
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    if (userWithProfile?.profile?.slug) {
      revalidatePath(`/${userWithProfile.profile.slug}`)
    }

    return { 
      success: true, 
      data: {
        displayName: updatedUser.displayName,
        bio: updatedUser.bio,
        profileImage: updatedUser.profileImage,
      }
    }
  } catch (error) {
    console.error('Profile update error:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.issues.map((issue: any) => issue.message).join(', ')
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update profile'
    }
  }
}

/**
 * Update username (affects profile slug)
 */
export async function updateUsername(username: string): Promise<ProfileActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Validate username
    const usernameSchema = z.string()
      .min(3, 'Username must be at least 3 characters')
      .max(20, 'Username must be less than 20 characters')
      .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores')
    
    const validatedUsername = usernameSchema.parse(username)
    
    // Check if username is available
    const isAvailable = await UserRepository.isUsernameAvailable(validatedUsername, session.user.id)
    if (!isAvailable) {
      return { success: false, error: "Username is already taken" }
    }
    
    // Update user
    const updatedUser = await UserRepository.update(session.user.id, {
      username: validatedUsername
    })
    
    // Update profile slug if profile exists
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    if (userWithProfile?.profile) {
      await ProfileRepository.update(userWithProfile.profile.id, {
        slug: validatedUsername
      })
    }

    // Revalidate pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/profile')
    revalidatePath(`/${validatedUsername}`)

    return { 
      success: true, 
      data: { username: updatedUser.username }
    }
  } catch (error) {
    console.error('Username update error:', error)
    
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.issues.map((issue: any) => issue.message).join(', ')
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update username'
    }
  }
}

/**
 * Delete profile image
 */
export async function deleteProfileImage(): Promise<ProfileActionResult> {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return { success: false, error: "Unauthorized" }
    }

    // Update user to remove profile image
    await UserRepository.update(session.user.id, {
      profileImage: null
    })

    // Revalidate pages
    revalidatePath('/dashboard')
    revalidatePath('/dashboard/profile')
    
    const userWithProfile = await UserRepository.findWithProfile(session.user.id)
    if (userWithProfile?.profile?.slug) {
      revalidatePath(`/${userWithProfile.profile.slug}`)
    }

    return { success: true }
  } catch (error) {
    console.error('Profile image deletion error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete profile image'
    }
  }
}