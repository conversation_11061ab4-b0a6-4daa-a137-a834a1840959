import { z } from 'zod'
import type { User, <PERSON>, <PERSON> } from '@prisma/client'

// Authentication schemas
export const signInSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

export const signUpSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores'),
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// Core data types from Prisma
export type { User, Profile, Link } from '@prisma/client'

// Extended types with relations
export type UserWithProfile = User & {
  profile: Profile | null
}

export type ProfileWithLinks = Profile & {
  links: Link[]
  user: User
}

export type ProfileWithUser = Profile & {
  user: Pick<User, 'id' | 'displayName' | 'bio' | 'profileImage'>
}

// Profile and Link types
export interface ProfileTheme {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  fontFamily: string
  preset?: string
}

export type SignInData = z.infer<typeof signInSchema>
export type SignUpData = z.infer<typeof signUpSchema>

// UI Component Types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

export interface FormState<T = unknown> extends LoadingState {
  data?: T
  success?: boolean
}

// Common UI variants
export type ComponentSize = "sm" | "md" | "lg"
export type ComponentVariant = "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"

// Form field types for better type safety
export interface FormFieldConfig {
  name: string
  label: string
  placeholder?: string
  type?: "text" | "email" | "password" | "textarea" | "select"
  required?: boolean
  options?: Array<{ value: string; label: string }>
}