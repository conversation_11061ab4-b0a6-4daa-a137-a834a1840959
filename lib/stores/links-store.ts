"use client"

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { Link } from '@/lib/types'
import { 
  createLink, 
  updateLink, 
  deleteLink, 
  toggleLinkVisibility, 
  reorderLinks,
  duplicateLink,
  getUserLinks
} from '@/lib/actions/links'
import type { CreateLinkData, UpdateLinkData } from '@/lib/validations'

interface LinksStore {
  links: Link[]
  isLoading: boolean
  error: string | null
  optimisticUpdates: Record<string, Partial<Link>>
  
  // Actions
  setLinks: (links: Link[]) => void
  addLinkOptimistic: (tempId: string, link: Omit<Link, 'id' | 'createdAt' | 'updatedAt'>) => void
  createLinkServer: (data: CreateLinkData) => Promise<void>
  updateLinkOptimistic: (id: string, updates: Partial<Link>) => void
  updateLinkServer: (id: string, data: UpdateLinkData) => Promise<void>
  deleteLinkOptimistic: (id: string) => void
  deleteLinkServer: (id: string) => Promise<void>
  toggleLinkVisibilityOptimistic: (id: string) => void
  toggleLinkVisibilityServer: (id: string) => Promise<void>
  reorderLinksOptimistic: (newOrder: Link[]) => void
  reorderLinksServer: (linkIds: string[]) => Promise<void>
  duplicateLinkServer: (id: string) => Promise<void>
  refreshLinks: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useLinksStore = create<LinksStore>()(
  subscribeWithSelector((set, get) => ({
    links: [],
    isLoading: false,
    error: null,
    optimisticUpdates: {},

    setLinks: (links) => set({ 
      links: links.sort((a, b) => a.order - b.order), 
      optimisticUpdates: {},
      error: null 
    }),

    addLinkOptimistic: (tempId, linkData) => {
      const { links } = get()
      const newOrder = Math.max(...links.map(l => l.order), 0) + 1
      const tempLink: Link = {
        id: tempId,
        ...linkData,
        order: newOrder,
        createdAt: new Date(),
        updatedAt: new Date(),
      }
      
      set((state) => ({
        links: [...state.links, tempLink].sort((a, b) => a.order - b.order),
        optimisticUpdates: { ...state.optimisticUpdates, [tempId]: tempLink }
      }))
    },

    createLinkServer: async (data) => {
      const tempId = `temp-${Date.now()}`
      
      // Optimistic update
      get().addLinkOptimistic(tempId, {
        profileId: '', // Will be set by server
        title: data.title,
        url: data.url,
        icon: data.icon,
        isVisible: true,
        clickCount: 0,
      })

      set({ isLoading: true, error: null })

      try {
        const result = await createLink(data)
        
        if (result.success && result.data) {
          // Replace temp link with real link
          set((state) => ({
            links: state.links.map(link => 
              link.id === tempId ? result.data : link
            ).sort((a, b) => a.order - b.order),
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([id]) => id !== tempId)
            ),
            isLoading: false,
            error: null
          }))
        } else {
          // Remove temp link on error
          set((state) => ({
            links: state.links.filter(link => link.id !== tempId),
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([id]) => id !== tempId)
            ),
            isLoading: false,
            error: result.error || 'Failed to create link'
          }))
        }
      } catch (error) {
        // Remove temp link on error
        set((state) => ({
          links: state.links.filter(link => link.id !== tempId),
          optimisticUpdates: Object.fromEntries(
            Object.entries(state.optimisticUpdates).filter(([id]) => id !== tempId)
          ),
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to create link'
        }))
      }
    },

    updateLinkOptimistic: (id, updates) => set((state) => ({
      optimisticUpdates: { 
        ...state.optimisticUpdates, 
        [id]: { ...state.optimisticUpdates[id], ...updates }
      }
    })),

    updateLinkServer: async (id, data) => {
      // Optimistic update
      get().updateLinkOptimistic(id, data)
      set({ isLoading: true, error: null })

      try {
        const result = await updateLink(id, data)
        
        if (result.success && result.data) {
          // Update the actual link data
          set((state) => ({
            links: state.links.map(link => 
              link.id === id ? result.data : link
            ),
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
            ),
            isLoading: false,
            error: null
          }))
        } else {
          // Revert optimistic update on error
          set((state) => ({
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
            ),
            isLoading: false,
            error: result.error || 'Failed to update link'
          }))
        }
      } catch (error) {
        // Revert optimistic update on error
        set((state) => ({
          optimisticUpdates: Object.fromEntries(
            Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
          ),
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to update link'
        }))
      }
    },

    deleteLinkOptimistic: (id) => set((state) => ({
      links: state.links.filter(link => link.id !== id),
      optimisticUpdates: Object.fromEntries(
        Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
      )
    })),

    deleteLinkServer: async (id) => {
      const { links } = get()
      const linkToDelete = links.find(link => link.id === id)
      
      // Optimistic update
      get().deleteLinkOptimistic(id)
      set({ isLoading: true, error: null })

      try {
        const result = await deleteLink(id)
        
        if (result.success) {
          set({ isLoading: false, error: null })
        } else {
          // Restore link on error
          if (linkToDelete) {
            set((state) => ({
              links: [...state.links, linkToDelete].sort((a, b) => a.order - b.order),
              isLoading: false,
              error: result.error || 'Failed to delete link'
            }))
          }
        }
      } catch (error) {
        // Restore link on error
        if (linkToDelete) {
          set((state) => ({
            links: [...state.links, linkToDelete].sort((a, b) => a.order - b.order),
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete link'
          }))
        }
      }
    },

    toggleLinkVisibilityOptimistic: (id) => set((state) => ({
      links: state.links.map(link => 
        link.id === id ? { ...link, isVisible: !link.isVisible } : link
      ),
      optimisticUpdates: {
        ...state.optimisticUpdates,
        [id]: { ...state.optimisticUpdates[id], isVisible: !state.links.find(l => l.id === id)?.isVisible }
      }
    })),

    toggleLinkVisibilityServer: async (id) => {
      // Optimistic update
      get().toggleLinkVisibilityOptimistic(id)
      set({ isLoading: true, error: null })

      try {
        const result = await toggleLinkVisibility(id)
        
        if (result.success && result.data) {
          // Update with server response
          set((state) => ({
            links: state.links.map(link => 
              link.id === id ? result.data : link
            ),
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
            ),
            isLoading: false,
            error: null
          }))
        } else {
          // Revert optimistic update on error
          get().toggleLinkVisibilityOptimistic(id) // Toggle back
          set((state) => ({
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([linkId]) => linkId !== id)
            ),
            isLoading: false,
            error: result.error || 'Failed to toggle link visibility'
          }))
        }
      } catch (error) {
        // Revert optimistic update on error
        get().toggleLinkVisibilityOptimistic(id) // Toggle back
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to toggle link visibility'
        })
      }
    },

    reorderLinksOptimistic: (newOrder) => set({ 
      links: newOrder.map((link, index) => ({ ...link, order: index }))
    }),

    reorderLinksServer: async (linkIds) => {
      const { links } = get()
      const originalOrder = [...links]
      
      // Optimistic update
      const newOrder = linkIds.map((id, index) => {
        const link = links.find(l => l.id === id)
        return link ? { ...link, order: index } : null
      }).filter(Boolean) as Link[]
      
      get().reorderLinksOptimistic(newOrder)
      set({ isLoading: true, error: null })

      try {
        const result = await reorderLinks(linkIds)
        
        if (result.success && result.data) {
          // Update with server response
          set({
            links: result.data.sort((a: Link, b: Link) => a.order - b.order),
            isLoading: false,
            error: null
          })
        } else {
          // Revert to original order on error
          set({
            links: originalOrder,
            isLoading: false,
            error: result.error || 'Failed to reorder links'
          })
        }
      } catch (error) {
        // Revert to original order on error
        set({
          links: originalOrder,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to reorder links'
        })
      }
    },

    duplicateLinkServer: async (id) => {
      set({ isLoading: true, error: null })

      try {
        const result = await duplicateLink(id)
        
        if (result.success && result.data) {
          // Add the duplicated link
          set((state) => ({
            links: [...state.links, result.data].sort((a, b) => a.order - b.order),
            isLoading: false,
            error: null
          }))
        } else {
          set({
            isLoading: false,
            error: result.error || 'Failed to duplicate link'
          })
        }
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to duplicate link'
        })
      }
    },

    refreshLinks: async () => {
      set({ isLoading: true, error: null })

      try {
        const result = await getUserLinks()
        
        if (result.success && result.data) {
          set({
            links: result.data.sort((a: Link, b: Link) => a.order - b.order),
            optimisticUpdates: {},
            isLoading: false,
            error: null
          })
        } else {
          set({
            isLoading: false,
            error: result.error || 'Failed to refresh links'
          })
        }
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to refresh links'
        })
      }
    },

    clearError: () => set({ error: null }),

    setLoading: (loading) => set({ isLoading: loading }),
  }))
)

// Computed values hook
export const useLinksData = () => {
  const { links, optimisticUpdates } = useLinksStore()
  
  // Merge actual links data with optimistic updates
  const currentLinks = links.map(link => ({
    ...link,
    ...optimisticUpdates[link.id]
  }))
  
  return currentLinks
}