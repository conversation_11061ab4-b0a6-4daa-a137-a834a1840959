"use client"

import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'
import type { ProfileTheme } from '@/lib/types'
import { updateTheme } from '@/lib/actions/theme'

interface ThemeStore {
  currentTheme: ProfileTheme
  presets: Array<{ name: string; theme: ProfileTheme }>
  backgroundType: 'color' | 'gradient' | 'image'
  backgroundValue: string
  isPreviewMode: boolean
  isLoading: boolean
  error: string | null
  optimisticUpdates: Partial<ProfileTheme>
  
  // Actions
  setTheme: (theme: ProfileTheme) => void
  updateThemeProperty: (key: keyof ProfileTheme, value: string) => void
  updateThemeOptimistic: (updates: Partial<ProfileTheme>) => void
  updateThemeServer: (updates: Partial<ProfileTheme>) => Promise<void>
  applyPreset: (preset: string) => void
  applyPresetServer: (preset: string) => Promise<void>
  setBackgroundType: (type: 'color' | 'gradient' | 'image') => void
  setBackgroundValue: (value: string) => void
  setPreviewMode: (enabled: boolean) => void
  resetTheme: () => void
  syncWithServer: (serverTheme: ProfileTheme, backgroundType: 'color' | 'gradient' | 'image', backgroundValue: string) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
}

const defaultTheme: ProfileTheme = {
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  fontFamily: 'Inter',
}

const themePresets = [
  {
    name: 'Default',
    theme: {
      primaryColor: '#3b82f6',
      secondaryColor: '#64748b',
      backgroundColor: '#ffffff',
      textColor: '#1f2937',
      fontFamily: 'Inter',
      preset: 'default'
    }
  },
  {
    name: 'Dark',
    theme: {
      primaryColor: '#60a5fa',
      secondaryColor: '#94a3b8',
      backgroundColor: '#1f2937',
      textColor: '#f9fafb',
      fontFamily: 'Inter',
      preset: 'dark'
    }
  },
  {
    name: 'Colorful',
    theme: {
      primaryColor: '#ec4899',
      secondaryColor: '#8b5cf6',
      backgroundColor: '#fef3c7',
      textColor: '#1f2937',
      fontFamily: 'Poppins',
      preset: 'colorful'
    }
  },
  {
    name: 'Minimal',
    theme: {
      primaryColor: '#000000',
      secondaryColor: '#6b7280',
      backgroundColor: '#f9fafb',
      textColor: '#111827',
      fontFamily: 'Inter',
      preset: 'minimal'
    }
  },
  {
    name: 'Ocean',
    theme: {
      primaryColor: '#0891b2',
      secondaryColor: '#0284c7',
      backgroundColor: '#f0f9ff',
      textColor: '#0c4a6e',
      fontFamily: 'Inter',
      preset: 'ocean'
    }
  },
  {
    name: 'Forest',
    theme: {
      primaryColor: '#059669',
      secondaryColor: '#047857',
      backgroundColor: '#f0fdf4',
      textColor: '#064e3b',
      fontFamily: 'Inter',
      preset: 'forest'
    }
  }
]

export const useThemeStore = create<ThemeStore>()(
  persist(
    subscribeWithSelector((set, get) => ({
      currentTheme: defaultTheme,
      presets: themePresets,
      backgroundType: 'color',
      backgroundValue: '#ffffff',
      isPreviewMode: false,
      isLoading: false,
      error: null,
      optimisticUpdates: {},

      setTheme: (theme) => set({ 
        currentTheme: theme, 
        optimisticUpdates: {},
        error: null 
      }),

      updateThemeProperty: (key, value) => set((state) => ({
        currentTheme: {
          ...state.currentTheme,
          [key]: value,
          preset: undefined // Clear preset when manually editing
        },
        optimisticUpdates: {
          ...state.optimisticUpdates,
          [key]: value,
          preset: undefined
        }
      })),

      updateThemeOptimistic: (updates) => set((state) => ({
        optimisticUpdates: { ...state.optimisticUpdates, ...updates }
      })),

      updateThemeServer: async (updates) => {
        const { currentTheme, backgroundType, backgroundValue } = get()
        
        // Optimistic update
        get().updateThemeOptimistic(updates)
        set({ isLoading: true, error: null })

        try {
          const result = await updateTheme({
            theme: { ...currentTheme, ...updates },
            backgroundType,
            backgroundValue
          })
          
          if (result.success && result.data) {
            // Update the actual theme data and clear optimistic updates
            set((state) => ({
              currentTheme: result.data.theme,
              backgroundType: result.data.backgroundType || state.backgroundType,
              backgroundValue: result.data.backgroundValue || state.backgroundValue,
              optimisticUpdates: {},
              isLoading: false,
              error: null
            }))
          } else {
            // Revert optimistic update on error
            set((state) => ({
              optimisticUpdates: Object.fromEntries(
                Object.entries(state.optimisticUpdates).filter(([key]) => !Object.keys(updates).includes(key))
              ),
              isLoading: false,
              error: result.error || 'Failed to update theme'
            }))
          }
        } catch (error) {
          // Revert optimistic update on error
          set((state) => ({
            optimisticUpdates: Object.fromEntries(
              Object.entries(state.optimisticUpdates).filter(([key]) => !Object.keys(updates).includes(key))
            ),
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update theme'
          }))
        }
      },

      applyPreset: (presetName) => {
        const preset = themePresets.find(p => p.name === presetName)
        if (preset) {
          set({ 
            currentTheme: preset.theme,
            backgroundType: 'color',
            backgroundValue: preset.theme.backgroundColor,
            optimisticUpdates: preset.theme
          })
        }
      },

      applyPresetServer: async (presetName) => {
        const preset = themePresets.find(p => p.name === presetName)
        if (!preset) return

        // Optimistic update
        get().applyPreset(presetName)
        set({ isLoading: true, error: null })

        try {
          const result = await updateTheme({
            theme: preset.theme,
            backgroundType: 'color',
            backgroundValue: preset.theme.backgroundColor
          })
          
          if (result.success && result.data) {
            // Update with server response
            set((state) => ({
              currentTheme: result.data.theme,
              backgroundType: result.data.backgroundType || 'color',
              backgroundValue: result.data.backgroundValue || preset.theme.backgroundColor,
              optimisticUpdates: {},
              isLoading: false,
              error: null
            }))
          } else {
            // Revert to previous theme on error
            set((state) => ({
              currentTheme: defaultTheme,
              backgroundType: 'color',
              backgroundValue: '#ffffff',
              optimisticUpdates: {},
              isLoading: false,
              error: result.error || 'Failed to apply preset'
            }))
          }
        } catch (error) {
          // Revert to previous theme on error
          set({
            currentTheme: defaultTheme,
            backgroundType: 'color',
            backgroundValue: '#ffffff',
            optimisticUpdates: {},
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to apply preset'
          })
        }
      },

      setBackgroundType: (type) => set({ backgroundType: type }),

      setBackgroundValue: (value) => set({ backgroundValue: value }),

      setPreviewMode: (enabled) => set({ isPreviewMode: enabled }),

      resetTheme: () => set({ 
        currentTheme: defaultTheme,
        backgroundType: 'color',
        backgroundValue: '#ffffff',
        optimisticUpdates: {},
        error: null
      }),

      syncWithServer: (serverTheme, backgroundType, backgroundValue) => set({
        currentTheme: serverTheme,
        backgroundType,
        backgroundValue,
        optimisticUpdates: {},
        error: null
      }),

      clearError: () => set({ error: null }),

      setLoading: (loading) => set({ isLoading: loading }),
    })),
    {
      name: 'theme-store',
      partialize: (state) => ({
        currentTheme: state.currentTheme,
        backgroundType: state.backgroundType,
        backgroundValue: state.backgroundValue,
      }),
    }
  )
)

// Computed values hook
export const useThemeData = () => {
  const { currentTheme, optimisticUpdates } = useThemeStore()
  
  // Merge actual theme data with optimistic updates
  const currentThemeWithUpdates = { ...currentTheme, ...optimisticUpdates }
  
  return currentThemeWithUpdates
}