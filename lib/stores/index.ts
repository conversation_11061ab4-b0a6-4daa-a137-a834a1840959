// Export all stores and their hooks
export { useProfileStore, useProfileData } from './profile-store'
export { useLinksStore, useLinksData } from './links-store'
export { useThemeStore, useThemeData } from './theme-store'

// Store synchronization utilities
export { 
  syncStoresWithServer, 
  clearAllStores, 
  useGlobalLoadingState, 
  useGlobalErrorState, 
  clearAllErrors 
} from './sync-utils'

// Optimistic updates hooks
export { 
  useProfileOptimistic, 
  useLinksOptimistic, 
  useThemeOptimistic, 
  useOptimisticUpdates 
} from '../hooks/use-optimistic-updates'

// State persistence hooks
export { 
  useThemePersistence, 
  useDraftPersistence, 
  useUserPreferences, 
  useOfflineSync 
} from '../hooks/use-state-persistence'