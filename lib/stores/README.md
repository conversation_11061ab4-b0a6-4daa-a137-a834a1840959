# Client-Side State Management

This directory contains the Zustand-based state management system for the LinksInBio application. The system provides optimistic updates, state synchronization between server and client, and persistence for theme preferences.

## Architecture

The state management system consists of three main stores:

1. **Profile Store** (`profile-store.ts`) - Manages user profile data
2. **Links Store** (`links-store.ts`) - Manages user links data
3. **Theme Store** (`theme-store.ts`) - Manages theme and customization data

## Key Features

### Optimistic Updates
All stores support optimistic updates for better user experience:
- Changes are immediately reflected in the UI
- Server actions are called in the background
- Changes are reverted if server actions fail

### State Synchronization
- Stores can be synchronized with server data
- Utilities provided for syncing all stores at once
- Handles initial data loading and updates

### Persistence
- Theme preferences are persisted to localStorage
- Draft data can be saved for forms
- User preferences are maintained across sessions

## Usage Examples

### Basic Store Usage

```typescript
import { useProfileData, useLinksData, useThemeData } from '@/lib/stores'

function MyComponent() {
  const currentUser = useProfileData() // Gets user with optimistic updates
  const currentLinks = useLinksData() // Gets links with optimistic updates
  const currentTheme = useThemeData() // Gets theme with optimistic updates

  return (
    <div>
      <h1>{currentUser?.displayName}</h1>
      <p>Links: {currentLinks.length}</p>
      <div style={{ color: currentTheme.primaryColor }}>
        Themed content
      </div>
    </div>
  )
}
```

### Optimistic Updates

```typescript
import { useOptimisticUpdates } from '@/lib/stores'

function ProfileEditor() {
  const { profile, links, theme } = useOptimisticUpdates()

  const handleUpdateProfile = async () => {
    // This will immediately update the UI and sync with server
    await profile.updateProfile({
      displayName: 'New Name',
      bio: 'New bio'
    })
  }

  const handleCreateLink = async () => {
    // This will immediately add the link to UI and sync with server
    await links.createLink({
      title: 'My Link',
      url: 'https://example.com'
    })
  }

  const handleUpdateTheme = async () => {
    // This will immediately update the theme and sync with server
    await theme.updateTheme({
      primaryColor: '#ff0000'
    })
  }

  return (
    <div>
      <button onClick={handleUpdateProfile}>Update Profile</button>
      <button onClick={handleCreateLink}>Create Link</button>
      <button onClick={handleUpdateTheme}>Update Theme</button>
    </div>
  )
}
```

### Store Synchronization

```typescript
import { syncStoresWithServer } from '@/lib/stores'

// Sync all stores with server data (e.g., on login or data refresh)
syncStoresWithServer({
  user: userData,
  profile: profileData,
  links: linksData,
  theme: {
    theme: themeData,
    backgroundType: 'color',
    backgroundValue: '#ffffff'
  }
})
```

### Global State Management

```typescript
import { 
  useGlobalLoadingState, 
  useGlobalErrorState, 
  clearAllErrors 
} from '@/lib/stores'

function GlobalStateIndicator() {
  const isLoading = useGlobalLoadingState()
  const errors = useGlobalErrorState()

  return (
    <div>
      {isLoading && <div>Loading...</div>}
      {errors && (
        <div>
          {errors.map((error, index) => (
            <div key={index} className="error">{error}</div>
          ))}
          <button onClick={clearAllErrors}>Clear Errors</button>
        </div>
      )}
    </div>
  )
}
```

### State Persistence

```typescript
import { useThemePersistence, useDraftPersistence } from '@/lib/stores'

function ThemeEditor() {
  const { saveThemePreferences, loadThemePreferences } = useThemePersistence()
  const { saveDraft, loadDraft, clearDraft } = useDraftPersistence('theme-editor')

  // Theme preferences are automatically saved
  // Draft data can be manually managed
  const handleSaveDraft = () => {
    saveDraft({ someFormData: 'value' })
  }

  const handleLoadDraft = () => {
    const draft = loadDraft()
    if (draft) {
      // Restore form state
    }
  }

  return (
    <div>
      <button onClick={handleSaveDraft}>Save Draft</button>
      <button onClick={handleLoadDraft}>Load Draft</button>
    </div>
  )
}
```

## Store Provider

Use the `StoreProvider` to initialize stores with server data:

```typescript
import { StoreProvider } from '@/components/providers/store-provider'

function App({ initialData }) {
  return (
    <StoreProvider initialData={initialData}>
      <YourApp />
    </StoreProvider>
  )
}
```

## Error Handling

All stores include comprehensive error handling:
- Optimistic updates are reverted on server errors
- Error states are tracked per store
- Global error state is available for UI feedback
- Errors can be cleared individually or globally

## Testing

The stores are fully testable with Jest and React Testing Library:

```typescript
import { renderHook, act } from '@testing-library/react'
import { useProfileStore } from '@/lib/stores'

test('should update profile optimistically', () => {
  const { result } = renderHook(() => useProfileStore())
  
  act(() => {
    result.current.updateProfileOptimistic({ displayName: 'Test Name' })
  })

  expect(result.current.optimisticUpdates.displayName).toBe('Test Name')
})
```

## Best Practices

1. **Use computed hooks** (`useProfileData`, `useLinksData`, `useThemeData`) instead of raw store data
2. **Use optimistic update hooks** for better UX
3. **Handle loading and error states** in your components
4. **Sync stores with server data** on app initialization
5. **Clear stores on logout** using `clearAllStores()`
6. **Use persistence hooks** for draft data and preferences
7. **Test store interactions** with proper mocking

## Files Structure

```
lib/stores/
├── index.ts                 # Main exports
├── profile-store.ts         # Profile state management
├── links-store.ts          # Links state management  
├── theme-store.ts          # Theme state management
├── sync-utils.ts           # Store synchronization utilities
├── __tests__/              # Store tests
└── README.md               # This documentation

lib/hooks/
├── use-optimistic-updates.ts  # Optimistic updates hooks
└── use-state-persistence.ts   # Persistence hooks

components/providers/
└── store-provider.tsx      # Store initialization provider
```

This state management system provides a robust foundation for managing client-side state with optimistic updates, server synchronization, and persistence capabilities.