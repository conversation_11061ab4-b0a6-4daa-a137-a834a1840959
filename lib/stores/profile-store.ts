"use client"

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import type { User, Profile } from '@/lib/types'
import { updateProfile, updateUsername, deleteProfileImage } from '@/lib/actions/profile'

interface ProfileData {
  user: User | null
  profile: Profile | null
}

interface ProfileStore extends ProfileData {
  isLoading: boolean
  error: string | null
  optimisticUpdates: Partial<User>
  
  // Actions
  setProfile: (user: User | null, profile: Profile | null) => void
  updateProfileOptimistic: (updates: Partial<User>) => void
  updateProfileServer: (updates: { displayName: string; bio?: string | null; profileImage?: string | null }) => Promise<void>
  updateUsernameServer: (username: string) => Promise<void>
  deleteProfileImageServer: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useProfileStore = create<ProfileStore>()(
  subscribeWithSelector((set, get) => ({
    user: null,
    profile: null,
    isLoading: false,
    error: null,
    optimisticUpdates: {},

    setProfile: (user, profile) => set({ 
      user, 
      profile, 
      optimisticUpdates: {},
      error: null 
    }),

    updateProfileOptimistic: (updates) => set((state) => ({
      optimisticUpdates: { ...state.optimisticUpdates, ...updates }
    })),

    updateProfileServer: async (updates) => {
      const { user } = get()
      if (!user) return

      // Optimistic update
      set((state) => ({
        optimisticUpdates: { ...state.optimisticUpdates, ...updates },
        isLoading: true,
        error: null
      }))

      try {
        const result = await updateProfile(updates)
        
        if (result.success && result.data) {
          // Update the actual user data and clear optimistic updates
          set((state) => ({
            user: state.user ? { ...state.user, ...result.data } : null,
            optimisticUpdates: {},
            isLoading: false,
            error: null
          }))
        } else {
          // Revert optimistic update on error
          set((state) => ({
            optimisticUpdates: {},
            isLoading: false,
            error: result.error || 'Failed to update profile'
          }))
        }
      } catch (error) {
        // Revert optimistic update on error
        set({
          optimisticUpdates: {},
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to update profile'
        })
      }
    },

    updateUsernameServer: async (username) => {
      const { user, profile } = get()
      if (!user) return

      // Optimistic update
      set((state) => ({
        optimisticUpdates: { ...state.optimisticUpdates, username },
        isLoading: true,
        error: null
      }))

      try {
        const result = await updateUsername(username)
        
        if (result.success && result.data) {
          // Update the actual user data and profile slug
          set((state) => ({
            user: state.user ? { ...state.user, username: result.data.username } : null,
            profile: state.profile ? { ...state.profile, slug: result.data.username } : null,
            optimisticUpdates: {},
            isLoading: false,
            error: null
          }))
        } else {
          // Revert optimistic update on error
          set({
            optimisticUpdates: {},
            isLoading: false,
            error: result.error || 'Failed to update username'
          })
        }
      } catch (error) {
        // Revert optimistic update on error
        set({
          optimisticUpdates: {},
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to update username'
        })
      }
    },

    deleteProfileImageServer: async () => {
      const { user } = get()
      if (!user) return

      // Optimistic update
      set((state) => ({
        optimisticUpdates: { ...state.optimisticUpdates, profileImage: null },
        isLoading: true,
        error: null
      }))

      try {
        const result = await deleteProfileImage()
        
        if (result.success) {
          // Update the actual user data
          set((state) => ({
            user: state.user ? { ...state.user, profileImage: null } : null,
            optimisticUpdates: {},
            isLoading: false,
            error: null
          }))
        } else {
          // Revert optimistic update on error
          set({
            optimisticUpdates: {},
            isLoading: false,
            error: result.error || 'Failed to delete profile image'
          })
        }
      } catch (error) {
        // Revert optimistic update on error
        set({
          optimisticUpdates: {},
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to delete profile image'
        })
      }
    },

    clearError: () => set({ error: null }),

    setLoading: (loading) => set({ isLoading: loading }),
  }))
)

// Computed values hook
export const useProfileData = () => {
  const { user, optimisticUpdates } = useProfileStore()
  
  // Merge actual user data with optimistic updates
  const currentUser = user ? { ...user, ...optimisticUpdates } : null
  
  return currentUser
}