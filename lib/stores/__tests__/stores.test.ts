import { renderHook, act } from '@testing-library/react'
import { useProfileStore, useLinksStore, useThemeStore } from '../index'

// Mock the actions
jest.mock('@/lib/actions/profile', () => ({
  updateProfile: jest.fn().mockResolvedValue({ success: true, data: { displayName: 'Updated Name' } }),
  updateUsername: jest.fn().mockResolvedValue({ success: true, data: { username: 'newusername' } }),
  deleteProfileImage: jest.fn().mockResolvedValue({ success: true })
}))

jest.mock('@/lib/actions/links', () => ({
  createLink: jest.fn().mockResolvedValue({ success: true, data: { id: '1', title: 'Test Link', url: 'https://test.com', order: 0, isVisible: true, clickCount: 0, profileId: 'profile1', createdAt: new Date(), updatedAt: new Date() } }),
  updateLink: jest.fn().mockResolvedValue({ success: true, data: { id: '1', title: 'Updated Link' } }),
  deleteLink: jest.fn().mockResolvedValue({ success: true }),
  toggleLinkVisibility: jest.fn().mockResolvedValue({ success: true, data: { id: '1', isVisible: false } }),
  reorderLinks: jest.fn().mockResolvedValue({ success: true, data: [] }),
  duplicateLink: jest.fn().mockResolvedValue({ success: true, data: { id: '2', title: 'Test Link Copy' } }),
  getUserLinks: jest.fn().mockResolvedValue({ success: true, data: [] })
}))

jest.mock('@/lib/actions/theme', () => ({
  updateTheme: jest.fn().mockResolvedValue({ success: true, data: { primaryColor: '#ff0000' } })
}))

describe('Profile Store', () => {
  beforeEach(() => {
    // Reset store state
    useProfileStore.getState().setProfile(null, null)
  })

  it('should set profile data', () => {
    const { result } = renderHook(() => useProfileStore())
    
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      username: 'testuser',
      displayName: 'Test User',
      bio: null,
      profileImage: null,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const mockProfile = {
      id: 'profile1',
      userId: '1',
      slug: 'testuser',
      theme: {},
      backgroundType: 'color' as const,
      backgroundValue: '#ffffff',
      isPublic: true,
      viewCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    act(() => {
      result.current.setProfile(mockUser, mockProfile)
    })

    expect(result.current.user).toEqual(mockUser)
    expect(result.current.profile).toEqual(mockProfile)
  })

  it('should handle optimistic updates', () => {
    const { result } = renderHook(() => useProfileStore())
    
    act(() => {
      result.current.updateProfileOptimistic({ displayName: 'Optimistic Name' })
    })

    expect(result.current.optimisticUpdates).toEqual({ displayName: 'Optimistic Name' })
  })
})

describe('Links Store', () => {
  beforeEach(() => {
    // Reset store state
    useLinksStore.getState().setLinks([])
  })

  it('should set links data', () => {
    const { result } = renderHook(() => useLinksStore())
    
    const mockLinks = [
      {
        id: '1',
        profileId: 'profile1',
        title: 'Test Link',
        url: 'https://test.com',
        icon: null,
        isVisible: true,
        order: 0,
        clickCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    act(() => {
      result.current.setLinks(mockLinks)
    })

    expect(result.current.links).toEqual(mockLinks)
  })

  it('should handle optimistic link updates', () => {
    const { result } = renderHook(() => useLinksStore())
    
    const mockLinks = [
      {
        id: '1',
        profileId: 'profile1',
        title: 'Test Link',
        url: 'https://test.com',
        icon: null,
        isVisible: true,
        order: 0,
        clickCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]

    act(() => {
      result.current.setLinks(mockLinks)
    })

    act(() => {
      result.current.updateLinkOptimistic('1', { title: 'Updated Title' })
    })

    expect(result.current.optimisticUpdates['1']).toEqual({ title: 'Updated Title' })
  })
})

describe('Theme Store', () => {
  beforeEach(() => {
    // Reset store state
    useThemeStore.getState().resetTheme()
  })

  it('should set theme data', () => {
    const { result } = renderHook(() => useThemeStore())
    
    const mockTheme = {
      primaryColor: '#ff0000',
      secondaryColor: '#00ff00',
      backgroundColor: '#ffffff',
      textColor: '#000000',
      fontFamily: 'Arial'
    }

    act(() => {
      result.current.setTheme(mockTheme)
    })

    expect(result.current.currentTheme).toEqual(mockTheme)
  })

  it('should handle theme property updates', () => {
    const { result } = renderHook(() => useThemeStore())
    
    act(() => {
      result.current.updateThemeProperty('primaryColor', '#ff0000')
    })

    expect(result.current.currentTheme.primaryColor).toBe('#ff0000')
    expect(result.current.optimisticUpdates.primaryColor).toBe('#ff0000')
  })

  it('should apply presets', () => {
    const { result } = renderHook(() => useThemeStore())
    
    act(() => {
      result.current.applyPreset('Dark')
    })

    expect(result.current.currentTheme.preset).toBe('dark')
    expect(result.current.backgroundType).toBe('color')
  })
})