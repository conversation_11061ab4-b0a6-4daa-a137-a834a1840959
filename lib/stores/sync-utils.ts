"use client"

import { useProfileStore } from './profile-store'
import { useLinksStore } from './links-store'
import { useThemeStore } from './theme-store'
import type { User, Profile, Link, ProfileTheme } from '@/lib/types'

/**
 * Synchronize all stores with server data
 * This should be called when the user logs in or when fresh data is fetched
 */
export function syncStoresWithServer(data: {
  user?: User | null
  profile?: Profile | null
  links?: Link[]
  theme?: {
    theme: ProfileTheme
    backgroundType: 'color' | 'gradient' | 'image'
    backgroundValue: string
  }
}) {
  const { setProfile } = useProfileStore.getState()
  const { setLinks } = useLinksStore.getState()
  const { syncWithServer } = useThemeStore.getState()

  // Sync profile data
  if (data.user !== undefined || data.profile !== undefined) {
    setProfile(data.user || null, data.profile || null)
  }

  // Sync links data
  if (data.links) {
    setLinks(data.links)
  }

  // Sync theme data
  if (data.theme) {
    syncWithServer(
      data.theme.theme,
      data.theme.backgroundType,
      data.theme.backgroundValue
    )
  }
}

/**
 * Clear all stores (useful for logout)
 */
export function clearAllStores() {
  const { setProfile } = useProfileStore.getState()
  const { setLinks } = useLinksStore.getState()
  const { resetTheme } = useThemeStore.getState()

  setProfile(null, null)
  setLinks([])
  resetTheme()
}

/**
 * Hook to get loading state from all stores
 */
export function useGlobalLoadingState() {
  const profileLoading = useProfileStore(state => state.isLoading)
  const linksLoading = useLinksStore(state => state.isLoading)
  const themeLoading = useThemeStore(state => state.isLoading)

  return profileLoading || linksLoading || themeLoading
}

/**
 * Hook to get error state from all stores
 */
export function useGlobalErrorState() {
  const profileError = useProfileStore(state => state.error)
  const linksError = useLinksStore(state => state.error)
  const themeError = useThemeStore(state => state.error)

  const errors = [profileError, linksError, themeError].filter(Boolean)
  return errors.length > 0 ? errors : null
}

/**
 * Clear all errors from all stores
 */
export function clearAllErrors() {
  const { clearError: clearProfileError } = useProfileStore.getState()
  const { clearError: clearLinksError } = useLinksStore.getState()
  const { clearError: clearThemeError } = useThemeStore.getState()

  clearProfileError()
  clearLinksError()
  clearThemeError()
}

/**
 * Subscribe to store changes for debugging
 */
export function subscribeToStoreChanges() {
  if (process.env.NODE_ENV === 'development') {
    useProfileStore.subscribe(
      (state) => state,
      (state) => console.log('Profile store updated:', state)
    )

    useLinksStore.subscribe(
      (state) => state,
      (state) => console.log('Links store updated:', state)
    )

    useThemeStore.subscribe(
      (state) => state,
      (state) => console.log('Theme store updated:', state)
    )
  }
}