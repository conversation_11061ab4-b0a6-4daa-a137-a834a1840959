"use client"

import { useCallback } from 'react'
import { useProfileStore, useLinksStore, useThemeStore } from '@/lib/stores'
import type { CreateLinkData, UpdateLinkData } from '@/lib/validations'
import type { ProfileTheme } from '@/lib/types'

/**
 * Hook for profile optimistic updates
 */
export function useProfileOptimistic() {
  const {
    updateProfileOptimistic,
    updateProfileServer,
    updateUsernameServer,
    deleteProfileImageServer,
    clearError,
    isLoading,
    error
  } = useProfileStore()

  const updateProfile = useCallback(async (updates: {
    displayName: string
    bio?: string | null
    profileImage?: string | null
  }) => {
    await updateProfileServer(updates)
  }, [updateProfileServer])

  const updateUsername = useCallback(async (username: string) => {
    await updateUsernameServer(username)
  }, [updateUsernameServer])

  const deleteProfileImage = useCallback(async () => {
    await deleteProfileImageServer()
  }, [deleteProfileImageServer])

  return {
    updateProfile,
    updateUsername,
    deleteProfileImage,
    clearError,
    isLoading,
    error
  }
}

/**
 * Hook for links optimistic updates
 */
export function useLinksOptimistic() {
  const {
    createLinkServer,
    updateLinkServer,
    deleteLinkServer,
    toggleLinkVisibilityServer,
    reorderLinksServer,
    duplicateLinkServer,
    refreshLinks,
    clearError,
    isLoading,
    error
  } = useLinksStore()

  const createLink = useCallback(async (data: CreateLinkData) => {
    await createLinkServer(data)
  }, [createLinkServer])

  const updateLink = useCallback(async (id: string, data: UpdateLinkData) => {
    await updateLinkServer(id, data)
  }, [updateLinkServer])

  const deleteLink = useCallback(async (id: string) => {
    await deleteLinkServer(id)
  }, [deleteLinkServer])

  const toggleLinkVisibility = useCallback(async (id: string) => {
    await toggleLinkVisibilityServer(id)
  }, [toggleLinkVisibilityServer])

  const reorderLinks = useCallback(async (linkIds: string[]) => {
    await reorderLinksServer(linkIds)
  }, [reorderLinksServer])

  const duplicateLink = useCallback(async (id: string) => {
    await duplicateLinkServer(id)
  }, [duplicateLinkServer])

  return {
    createLink,
    updateLink,
    deleteLink,
    toggleLinkVisibility,
    reorderLinks,
    duplicateLink,
    refreshLinks,
    clearError,
    isLoading,
    error
  }
}

/**
 * Hook for theme optimistic updates
 */
export function useThemeOptimistic() {
  const {
    updateThemeServer,
    applyPresetServer,
    updateThemeProperty,
    setBackgroundType,
    setBackgroundValue,
    setPreviewMode,
    clearError,
    isLoading,
    error
  } = useThemeStore()

  const updateTheme = useCallback(async (updates: Partial<ProfileTheme>) => {
    await updateThemeServer(updates)
  }, [updateThemeServer])

  const applyPreset = useCallback(async (preset: string) => {
    await applyPresetServer(preset)
  }, [applyPresetServer])

  const updateThemePropertyOptimistic = useCallback((key: keyof ProfileTheme, value: string) => {
    updateThemeProperty(key, value)
  }, [updateThemeProperty])

  return {
    updateTheme,
    applyPreset,
    updateThemeProperty: updateThemePropertyOptimistic,
    setBackgroundType,
    setBackgroundValue,
    setPreviewMode,
    clearError,
    isLoading,
    error
  }
}

/**
 * Combined hook for all optimistic updates
 */
export function useOptimisticUpdates() {
  const profile = useProfileOptimistic()
  const links = useLinksOptimistic()
  const theme = useThemeOptimistic()

  const clearAllErrors = useCallback(() => {
    profile.clearError()
    links.clearError()
    theme.clearError()
  }, [profile, links, theme])

  const isLoading = profile.isLoading || links.isLoading || theme.isLoading
  const errors = [profile.error, links.error, theme.error].filter(Boolean)

  return {
    profile,
    links,
    theme,
    clearAllErrors,
    isLoading,
    errors: errors.length > 0 ? errors : null
  }
}