"use client"

import { useEffect, useCallback } from 'react'
import { useThemeStore } from '@/lib/stores'

/**
 * Hook for managing theme persistence preferences
 */
export function useThemePersistence() {
  const { currentTheme, backgroundType, backgroundValue, setPreviewMode } = useThemeStore()

  // Save theme preferences to localStorage
  const saveThemePreferences = useCallback(() => {
    try {
      const preferences = {
        theme: currentTheme,
        backgroundType,
        backgroundValue,
        timestamp: Date.now()
      }
      localStorage.setItem('theme-preferences', JSON.stringify(preferences))
    } catch (error) {
      console.warn('Failed to save theme preferences:', error)
    }
  }, [currentTheme, backgroundType, backgroundValue])

  // Load theme preferences from localStorage
  const loadThemePreferences = useCallback(() => {
    try {
      const stored = localStorage.getItem('theme-preferences')
      if (stored) {
        const preferences = JSON.parse(stored)
        // Check if preferences are not too old (7 days)
        const isRecent = Date.now() - preferences.timestamp < 7 * 24 * 60 * 60 * 1000
        if (isRecent) {
          return preferences
        }
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error)
    }
    return null
  }, [])

  // Clear theme preferences
  const clearThemePreferences = useCallback(() => {
    try {
      localStorage.removeItem('theme-preferences')
    } catch (error) {
      console.warn('Failed to clear theme preferences:', error)
    }
  }, [])

  // Auto-save theme changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveThemePreferences()
    }, 1000) // Debounce saves by 1 second

    return () => clearTimeout(timeoutId)
  }, [saveThemePreferences])

  return {
    saveThemePreferences,
    loadThemePreferences,
    clearThemePreferences
  }
}

/**
 * Hook for managing draft state persistence (for forms)
 */
export function useDraftPersistence(key: string) {
  // Save draft data
  const saveDraft = useCallback((data: any) => {
    try {
      const draft = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(`draft-${key}`, JSON.stringify(draft))
    } catch (error) {
      console.warn(`Failed to save draft for ${key}:`, error)
    }
  }, [key])

  // Load draft data
  const loadDraft = useCallback(() => {
    try {
      const stored = localStorage.getItem(`draft-${key}`)
      if (stored) {
        const draft = JSON.parse(stored)
        // Check if draft is not too old (1 hour)
        const isRecent = Date.now() - draft.timestamp < 60 * 60 * 1000
        if (isRecent) {
          return draft.data
        }
      }
    } catch (error) {
      console.warn(`Failed to load draft for ${key}:`, error)
    }
    return null
  }, [key])

  // Clear draft data
  const clearDraft = useCallback(() => {
    try {
      localStorage.removeItem(`draft-${key}`)
    } catch (error) {
      console.warn(`Failed to clear draft for ${key}:`, error)
    }
  }, [key])

  return {
    saveDraft,
    loadDraft,
    clearDraft
  }
}

/**
 * Hook for managing user preferences
 */
export function useUserPreferences() {
  // Save user preferences
  const savePreferences = useCallback((preferences: Record<string, any>) => {
    try {
      const existing = localStorage.getItem('user-preferences')
      const current = existing ? JSON.parse(existing) : {}
      const updated = { ...current, ...preferences, timestamp: Date.now() }
      localStorage.setItem('user-preferences', JSON.stringify(updated))
    } catch (error) {
      console.warn('Failed to save user preferences:', error)
    }
  }, [])

  // Load user preferences
  const loadPreferences = useCallback(() => {
    try {
      const stored = localStorage.getItem('user-preferences')
      if (stored) {
        return JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error)
    }
    return {}
  }, [])

  // Clear user preferences
  const clearPreferences = useCallback(() => {
    try {
      localStorage.removeItem('user-preferences')
    } catch (error) {
      console.warn('Failed to clear user preferences:', error)
    }
  }, [])

  return {
    savePreferences,
    loadPreferences,
    clearPreferences
  }
}

/**
 * Hook for managing offline state and sync
 */
export function useOfflineSync() {
  const saveOfflineAction = useCallback((action: {
    type: string
    payload: any
    timestamp: number
  }) => {
    try {
      const existing = localStorage.getItem('offline-actions')
      const actions = existing ? JSON.parse(existing) : []
      actions.push(action)
      localStorage.setItem('offline-actions', JSON.stringify(actions))
    } catch (error) {
      console.warn('Failed to save offline action:', error)
    }
  }, [])

  const getOfflineActions = useCallback(() => {
    try {
      const stored = localStorage.getItem('offline-actions')
      return stored ? JSON.parse(stored) : []
    } catch (error) {
      console.warn('Failed to get offline actions:', error)
      return []
    }
  }, [])

  const clearOfflineActions = useCallback(() => {
    try {
      localStorage.removeItem('offline-actions')
    } catch (error) {
      console.warn('Failed to clear offline actions:', error)
    }
  }, [])

  return {
    saveOfflineAction,
    getOfflineActions,
    clearOfflineActions
  }
}