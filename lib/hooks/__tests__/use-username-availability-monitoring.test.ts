/**
 * Integration tests for username availability monitoring
 * Tests the complete flow from hook usage to performance monitoring
 */

import { renderHook, act, waitFor } from '@testing-library/react'
import { useUsernameAvailability } from '../use-username-availability'
import { performanceMonitor } from '@/lib/utils/performance-monitor'

// Mock fetch
global.fetch = jest.fn()
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('useUsernameAvailability - Monitoring Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    performanceMonitor.clearMetrics()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should record performance metrics for successful availability checks', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ username: 'testuser', available: true })
    } as Response)

    const { result } = renderHook(() => useUsernameAvailability())

    act(() => {
      result.current.checkAvailability('testuser')
    })

    // Fast-forward past debounce delay
    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify performance metrics were recorded
    const stats = performanceMonitor.getStats()
    expect(stats.performance.totalRequests).toBe(1)
    expect(stats.performance.successRate).toBe(100)
    expect(stats.performance.averageResponseTime).toBeGreaterThan(0)

    // Verify cache metrics were recorded
    expect(stats.cache.totalOperations).toBeGreaterThan(0)
  })

  it('should record error metrics for failed availability checks', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useUsernameAvailability())

    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify error metrics were recorded
    const stats = performanceMonitor.getStats()
    expect(stats.errors.totalErrors).toBeGreaterThan(0)
    expect(stats.errors.errorsByType.NETWORK_ERROR).toBeGreaterThan(0)
  })

  it('should record cache hit metrics for repeated checks', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ username: 'testuser', available: true })
    } as Response)

    const { result } = renderHook(() => useUsernameAvailability())

    // First check - should hit API
    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Second check - should hit cache
    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.cached).toBe(true)
    })

    // Verify cache metrics
    const stats = performanceMonitor.getStats()
    expect(stats.cache.totalHits).toBeGreaterThan(0)
    expect(stats.cache.hitRate).toBeGreaterThan(0)
  })

  it('should record timeout metrics for slow requests', async () => {
    // Mock a slow response that times out
    mockFetch.mockImplementation(() => 
      new Promise((resolve) => {
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ username: 'testuser', available: true })
        } as Response), 6000) // Longer than 5s timeout
      })
    )

    const { result } = renderHook(() => useUsernameAvailability())

    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300) // Debounce
    })

    act(() => {
      jest.advanceTimersByTime(5000) // Trigger timeout
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify timeout was recorded
    const stats = performanceMonitor.getStats()
    expect(stats.performance.timeouts).toBeGreaterThan(0)
    expect(stats.errors.errorsByType.TIMEOUT_ERROR).toBeGreaterThan(0)
  })

  it('should record validation error metrics for invalid usernames', async () => {
    const { result } = renderHook(() => useUsernameAvailability())

    act(() => {
      result.current.checkAvailability('ab') // Too short
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify validation error was recorded
    const stats = performanceMonitor.getStats()
    expect(stats.errors.totalErrors).toBe(1)
    expect(stats.errors.errorsByType.TOO_SHORT).toBe(1)
  })

  it('should record retry metrics for failed requests', async () => {
    // First call fails
    mockFetch.mockRejectedValueOnce(new Error('Network error'))
    // Second call (retry) succeeds
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ username: 'testuser', available: true })
    } as Response)

    const { result } = renderHook(() => useUsernameAvailability())

    // Initial check
    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Retry
    act(() => {
      result.current.retry()
    })

    act(() => {
      jest.advanceTimersByTime(1000) // Retry delay
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify both attempts were recorded
    const stats = performanceMonitor.getStats()
    expect(stats.performance.totalRequests).toBe(2)
    expect(stats.errors.totalErrors).toBe(1) // Only the first failed attempt
  })

  it('should record performance metrics with correct metadata', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ username: 'testuser', available: false })
    } as Response)

    const { result } = renderHook(() => useUsernameAvailability('currentuser'))

    act(() => {
      result.current.checkAvailability('testuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Verify metadata was recorded correctly
    const rawMetrics = performanceMonitor.getRawMetrics()
    const performanceMetric = rawMetrics.performance[0]
    
    expect(performanceMetric.metadata).toMatchObject({
      username: 'testuser'
    })
    expect(performanceMetric.metadata).toHaveProperty('available')
    expect(performanceMetric.metadata).toHaveProperty('cached')
  })

  it('should handle current username checks without API calls', async () => {
    const { result } = renderHook(() => useUsernameAvailability('currentuser'))

    act(() => {
      result.current.checkAvailability('currentuser')
    })

    act(() => {
      jest.advanceTimersByTime(300)
    })

    await waitFor(() => {
      expect(result.current.status.checking).toBe(false)
    })

    // Should not make API call, but should record performance metric
    expect(mockFetch).not.toHaveBeenCalled()
    
    const stats = performanceMonitor.getStats()
    expect(stats.performance.totalRequests).toBe(1)
    expect(stats.performance.successRate).toBe(100)

    const rawMetrics = performanceMonitor.getRawMetrics()
    expect(rawMetrics.performance[0].metadata?.reason).toBe('current-username')
  })

  it('should track cache memory management', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ username: 'testuser', available: true })
    } as Response)

    const { result } = renderHook(() => useUsernameAvailability())

    // Make multiple unique requests to test cache management
    for (let i = 0; i < 10; i++) {
      act(() => {
        result.current.checkAvailability(`user${i}`)
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.checking).toBe(false)
      })
    }

    // Verify cache operations were recorded
    const stats = performanceMonitor.getStats()
    expect(stats.cache.totalOperations).toBeGreaterThan(0)
    
    const rawMetrics = performanceMonitor.getRawMetrics()
    const cacheSetOperations = rawMetrics.cache.filter((m: any) => m.operation === 'set')
    expect(cacheSetOperations.length).toBeGreaterThan(0) // Some cache operations should be recorded
  })
})