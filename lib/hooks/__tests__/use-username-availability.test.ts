import { renderHook, act, waitFor } from '@testing-library/react'
import { useUsernameAvailability, __testExports } from '../use-username-availability'

// Mock fetch
const mockFetch = jest.fn()
global.fetch = mockFetch

describe('useUsernameAvailability', () => {
  beforeEach(() => {
    mockFetch.mockClear()
    // Clear the global cache
    __testExports.clearCache()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
  })

  describe('Basic functionality', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      expect(result.current.status).toEqual({
        checking: false
      })
      expect(typeof result.current.checkAvailability).toBe('function')
      expect(typeof result.current.reset).toBe('function')
      expect(typeof result.current.retry).toBe('function')
    })

    it('should skip check when username matches current username', () => {
      const { result } = renderHook(() => useUsernameAvailability('testuser'))
      
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(result.current.status).toEqual({
        checking: false,
        available: true
      })
      expect(mockFetch).not.toHaveBeenCalled()
    })
  })

  describe('Debouncing', () => {
    it('should use 300ms debounce by default', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      act(() => {
        result.current.checkAvailability('testuser')
      })

      expect(mockFetch).not.toHaveBeenCalled()

      act(() => {
        jest.advanceTimersByTime(299)
      })

      expect(mockFetch).not.toHaveBeenCalled()

      act(() => {
        jest.advanceTimersByTime(1)
      })

      expect(mockFetch).toHaveBeenCalled()
    })

    it('should use custom debounce delay', () => {
      const { result } = renderHook(() => useUsernameAvailability('', 500))
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(499)
      })

      expect(mockFetch).not.toHaveBeenCalled()

      act(() => {
        jest.advanceTimersByTime(1)
      })

      expect(mockFetch).toHaveBeenCalled()
    })

    it('should cancel previous debounced call when new one is made', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      act(() => {
        result.current.checkAvailability('user1')
      })

      act(() => {
        jest.advanceTimersByTime(200)
      })

      act(() => {
        result.current.checkAvailability('user2')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(mockFetch).toHaveBeenCalledTimes(1)
      expect(mockFetch).toHaveBeenCalledWith(
        '/api/account/username/availability?username=user2',
        expect.any(Object)
      )
    })
  })

  describe('Validation', () => {
    it('should show error for username less than 3 characters', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      act(() => {
        result.current.checkAvailability('ab')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(result.current.status).toEqual({
        checking: false,
        error: 'Username must be at least 3 characters'
      })
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('should show error for invalid characters', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      act(() => {
        result.current.checkAvailability('user@name')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(result.current.status).toEqual({
        checking: false,
        error: 'Username can only contain letters, numbers, hyphens, and underscores'
      })
      expect(mockFetch).not.toHaveBeenCalled()
    })
  })

  describe('API calls and responses', () => {
    it('should handle successful available response', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.checking).toBe(false)
      })

      expect(result.current.status.available).toBe(true)
      expect(result.current.status.cached).toBe(false)
      expect(result.current.status.error).toBeUndefined()
    })

    it('should handle successful unavailable response', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: false })
      })

      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.checking).toBe(false)
      })

      expect(result.current.status.available).toBe(false)
      expect(result.current.status.error).toBe('Username is already taken')
      expect(result.current.status.cached).toBe(false)
    })

    it('should handle network errors', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockRejectedValueOnce(new Error('Failed to fetch'))

      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.checking).toBe(false)
      })

      expect(result.current.status.error).toBe('Network error. Please check your connection and try again.')
    })
  })

  describe('Caching', () => {
    it('should return cached result for same username', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      // First call
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.cached).toBe(false)
      })

      // Second call should use cache
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(result.current.status.cached).toBe(true)
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    it('should cache case-insensitively', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ available: true })
      })

      // First call with lowercase
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.cached).toBe(false)
      })

      // Second call with uppercase should use cache
      act(() => {
        result.current.checkAvailability('TESTUSER')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(result.current.status.cached).toBe(true)
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })
  })

  describe('Retry functionality', () => {
    it('should retry with exponential backoff', async () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      mockFetch.mockRejectedValue(new Error('Network error'))

      // Initial call
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      await waitFor(() => {
        expect(result.current.status.error).toBeTruthy()
      })

      // First retry - 1 second delay
      act(() => {
        result.current.retry()
      })

      act(() => {
        jest.advanceTimersByTime(999)
      })
      expect(mockFetch).toHaveBeenCalledTimes(1)

      act(() => {
        jest.advanceTimersByTime(1)
      })
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })
  })

  describe('Reset functionality', () => {
    it('should cancel debounced check', () => {
      const { result } = renderHook(() => useUsernameAvailability())
      
      act(() => {
        result.current.checkAvailability('testuser')
      })

      act(() => {
        result.current.reset()
      })

      act(() => {
        jest.advanceTimersByTime(300)
      })

      expect(mockFetch).not.toHaveBeenCalled()
    })
  })

  describe('Cleanup on unmount', () => {
    it('should cleanup timers and abort controllers on unmount', () => {
      const { result, unmount } = renderHook(() => useUsernameAvailability())
      
      act(() => {
        result.current.checkAvailability('testuser')
      })

      // Should not throw or cause memory leaks
      expect(() => unmount()).not.toThrow()
    })
  })
})