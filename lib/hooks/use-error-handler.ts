'use client'

import { useCallback, useState } from 'react'
import { errorLogger } from '@/lib/utils/error-logger'
import { isAppError, getErrorMessage, ErrorFactory } from '@/lib/errors/app-errors'

export interface UseErrorHandlerOptions {
  context?: string
  onError?: (error: unknown) => void
  showToast?: boolean
}

export interface ErrorState {
  error: unknown | null
  isError: boolean
  errorMessage: string | null
  errorCode: string | null
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    errorMessage: null,
    errorCode: null
  })

  const handleError = useCallback((error: unknown, additionalContext?: Record<string, unknown>) => {
    const appError = isAppError(error) ? error : ErrorFactory.internalError(
      error instanceof Error ? error.message : String(error),
      error instanceof Error ? error : undefined
    )

    // Log the error
    errorLogger.error(`Component error in ${options.context || 'unknown'}`, {
      context: options.context,
      ...additionalContext,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined
    }, appError)

    // Update error state
    setErrorState({
      error: appError,
      isError: true,
      errorMessage: appError.getUserMessage(),
      errorCode: appError.code
    })

    // Call custom error handler
    if (options.onError) {
      options.onError(appError)
    }

    // Show toast notification if enabled
    if (options.showToast && typeof window !== 'undefined') {
      // You can integrate with your toast library here
      // For example, with react-hot-toast:
      // toast.error(appError.getUserMessage())
      console.error('Error:', appError.getUserMessage())
    }
  }, [options])

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      errorMessage: null,
      errorCode: null
    })
  }, [])

  const retry = useCallback((retryFn?: () => void | Promise<void>) => {
    clearError()
    if (retryFn) {
      try {
        const result = retryFn()
        if (result instanceof Promise) {
          result.catch(handleError)
        }
      } catch (error) {
        handleError(error)
      }
    }
  }, [clearError, handleError])

  return {
    ...errorState,
    handleError,
    clearError,
    retry
  }
}

// Specialized hooks for different contexts
export function useServerActionErrorHandler() {
  return useErrorHandler({
    context: 'server-action',
    showToast: true
  })
}

export function useApiErrorHandler() {
  return useErrorHandler({
    context: 'api-call',
    showToast: true
  })
}

export function useFormErrorHandler() {
  return useErrorHandler({
    context: 'form-submission'
  })
}

// Hook for handling async operations with error handling
export function useAsyncOperation<T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  options: UseErrorHandlerOptions = {}
) {
  const [isLoading, setIsLoading] = useState(false)
  const { handleError, clearError, ...errorState } = useErrorHandler(options)

  const execute = useCallback(async (...args: T): Promise<R | null> => {
    try {
      setIsLoading(true)
      clearError()
      const result = await operation(...args)
      return result
    } catch (error) {
      handleError(error, { operationArgs: args })
      return null
    } finally {
      setIsLoading(false)
    }
  }, [operation, handleError, clearError])

  const retry = useCallback((...args: T) => {
    return execute(...args)
  }, [execute])

  return {
    execute,
    retry,
    isLoading,
    ...errorState,
    clearError
  }
}

// Hook for handling form submissions with error handling
export function useFormSubmission<T>(
  submitFn: (data: T) => Promise<any>,
  options: UseErrorHandlerOptions = {}
) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const { handleError, clearError, ...errorState } = useErrorHandler({
    ...options,
    context: options.context || 'form-submission'
  })

  const submit = useCallback(async (data: T) => {
    try {
      setIsSubmitting(true)
      setIsSuccess(false)
      clearError()
      
      const result = await submitFn(data)
      
      // Check if result indicates an error (for Server Actions)
      if (result && typeof result === 'object' && 'success' in result && !result.success) {
        throw ErrorFactory.validationError('form', result.error || 'Submission failed')
      }
      
      setIsSuccess(true)
      return result
    } catch (error) {
      handleError(error, { formData: data })
      return null
    } finally {
      setIsSubmitting(false)
    }
  }, [submitFn, handleError, clearError])

  const reset = useCallback(() => {
    setIsSuccess(false)
    clearError()
  }, [clearError])

  return {
    submit,
    reset,
    isSubmitting,
    isSuccess,
    ...errorState,
    clearError
  }
}