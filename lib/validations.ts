import { z } from 'zod'

// URL validation
export const urlSchema = z.string().url('Please enter a valid URL')

// Username validation
export const usernameSchema = z.string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be less than 30 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, hyphens, and underscores')
  .regex(/^[a-zA-Z0-9]/, 'Username cannot start with special characters')
  .regex(/[a-zA-Z0-9]$/, 'Username cannot end with special characters')
  .regex(/^(?!.*[-_]{2,}).*$/, 'Username cannot contain consecutive special characters')

// User validation schemas
export const createUserSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  username: usernameSchema,
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  profileImage: z.string().url().optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
})

export const updateUserSchema = z.object({
  email: z.string().email('Please enter a valid email address').optional(),
  username: usernameSchema.optional(),
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters').optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional().nullable(),
  profileImage: z.string().url().optional().nullable(),
})

// Link validation schemas
export const createLinkSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  url: urlSchema,
  icon: z.string().optional(),
  isVisible: z.boolean().default(true),
  order: z.number().int().min(0).optional(),
  // Scheduling fields
  isScheduled: z.boolean().default(false),
  scheduleStart: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  scheduleEnd: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  timezone: z.string().default('UTC'),
}).refine((data) => {
  if (data.isScheduled && !data.scheduleStart) {
    return false
  }
  if (data.scheduleStart && data.scheduleEnd && data.scheduleStart >= data.scheduleEnd) {
    return false
  }
  return true
}, {
  message: "Invalid scheduling configuration",
  path: ["scheduleStart"]
})

export const updateLinkSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters').optional(),
  url: urlSchema.optional(),
  icon: z.string().optional(),
  isVisible: z.boolean().optional(),
  order: z.number().int().min(0).optional(),
  // Scheduling fields
  isScheduled: z.boolean().optional(),
  scheduleStart: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  scheduleEnd: z.union([z.date(), z.string().transform((str) => str ? new Date(str) : undefined)]).optional(),
  timezone: z.string().optional(),
}).refine((data) => {
  if (data.isScheduled && !data.scheduleStart) {
    return false
  }
  if (data.scheduleStart && data.scheduleEnd && data.scheduleStart >= data.scheduleEnd) {
    return false
  }
  return true
}, {
  message: "Invalid scheduling configuration",
  path: ["scheduleStart"]
})

export const linkSchema = createLinkSchema // Backward compatibility

// Profile validation schemas
export const createProfileSchema = z.object({
  userId: z.string().cuid(),
  slug: z.string().min(3, 'Slug must be at least 3 characters').max(20, 'Slug must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Slug can only contain letters, numbers, hyphens, and underscores'),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    fontFamily: z.string().min(1, 'Font family is required'),
    preset: z.string().optional(),
  }).default({
    primaryColor: '#000000',
    secondaryColor: '#666666',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    fontFamily: 'Inter',
  }),
  backgroundType: z.enum(['color', 'gradient', 'image']).default('color'),
  backgroundValue: z.string().default('#ffffff'),
  isPublic: z.boolean().default(true),
})

export const updateProfileSchema = z.object({
  slug: z.string().min(3, 'Slug must be at least 3 characters').max(20, 'Slug must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Slug can only contain letters, numbers, hyphens, and underscores').optional(),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
    fontFamily: z.string().min(1, 'Font family is required'),
    preset: z.string().optional(),
  }).optional(),
  backgroundType: z.enum(['color', 'gradient', 'image']).optional(),
  backgroundValue: z.string().optional(),
  isPublic: z.boolean().optional(),
})

export const profileUpdateSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  username: usernameSchema,
}) // Backward compatibility

// Type exports
export type CreateUserData = z.infer<typeof createUserSchema>
export type UpdateUserData = z.infer<typeof updateUserSchema>
export type CreateLinkData = z.infer<typeof createLinkSchema>
export type UpdateLinkData = z.infer<typeof updateLinkSchema>
export type CreateProfileData = z.infer<typeof createProfileSchema>
export type UpdateProfileData = z.infer<typeof updateProfileSchema>

// Backward compatibility
export type LinkData = z.infer<typeof linkSchema>
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>

// UI Form validation schemas
export const contactFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  message: z.string().min(1, 'Message is required').max(1000, 'Message must be less than 1000 characters'),
})

export const searchSchema = z.object({
  query: z.string().min(1, 'Search query is required').max(100, 'Search query must be less than 100 characters'),
})

// Theme validation
export const themeSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  backgroundColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  textColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  fontFamily: z.string().min(1, 'Font family is required'),
  preset: z.string().optional(),
})

// Account settings validation schemas
export const usernameUpdateSchema = z.object({
  username: usernameSchema,
})

export const accountDeletionSchema = z.object({
  confirmation: z.literal('DELETE', {
    errorMap: () => ({ message: 'You must type DELETE to confirm account deletion' })
  }),
})

export type ContactFormData = z.infer<typeof contactFormSchema>
export type SearchData = z.infer<typeof searchSchema>
export type ThemeData = z.infer<typeof themeSchema>
export type UsernameUpdateData = z.infer<typeof usernameUpdateSchema>
export type AccountDeletionData = z.infer<typeof accountDeletionSchema>