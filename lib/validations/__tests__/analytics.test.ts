import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import {
  isValidProfileId,
  isValidPeriod,
  isValidChartData,
  isValidLinkPerformanceData,
  isValidReferrerData,
  isValidAnalyticsDashboardResponse,
  sanitizeChartData,
  sanitizeLinkPerformanceData,
  sanitizeReferrerData,
  validateApiParameters,
  validateResponseData
} from '../analytics'

describe('Analytics Validation Functions', () => {
  describe('isValidProfileId', () => {
    it('should validate correct profile IDs', () => {
      expect(isValidProfileId('valid-profile-id')).toBe(true)
      expect(isValidProfileId('123e4567-e89b-12d3-a456-426614174000')).toBe(true)
      expect(isValidProfileId('profile_123')).toBe(true)
    })

    it('should reject invalid profile IDs', () => {
      expect(isValidProfileId('')).toBe(false)
      expect(isValidProfileId(null)).toBe(false)
      expect(isValidProfileId(undefined)).toBe(false)
      expect(isValidProfileId(123)).toBe(false)
      expect(isValidProfileId({})).toBe(false)
      expect(isValidProfileId([])).toBe(false)
    })

    it('should handle edge cases', () => {
      expect(isValidProfileId(' ')).toBe(false) // Whitespace only
      expect(isValidProfileId('a')).toBe(true) // Single character
      expect(isValidProfileId('a'.repeat(1000))).toBe(true) // Very long string
    })
  })

  describe('isValidPeriod', () => {
    it('should validate correct periods', () => {
      expect(isValidPeriod(1)).toBe(true)
      expect(isValidPeriod(7)).toBe(true)
      expect(isValidPeriod(30)).toBe(true)
      expect(isValidPeriod(90)).toBe(true)
      expect(isValidPeriod(365)).toBe(true)
    })

    it('should reject invalid periods', () => {
      expect(isValidPeriod(0)).toBe(false)
      expect(isValidPeriod(-1)).toBe(false)
      expect(isValidPeriod(366)).toBe(false)
      expect(isValidPeriod(1000)).toBe(false)
      expect(isValidPeriod(null)).toBe(false)
      expect(isValidPeriod(undefined)).toBe(false)
      expect(isValidPeriod('30')).toBe(false)
      expect(isValidPeriod(30.5)).toBe(false)
      expect(isValidPeriod(Infinity)).toBe(false)
      expect(isValidPeriod(NaN)).toBe(false)
    })
  })

  describe('isValidChartData', () => {
    it('should validate correct chart data', () => {
      const validData = [
        { date: '2024-01-01', count: 10 },
        { date: '2024-01-02', count: 15 },
        { date: '2024-01-03', count: 0 }
      ]
      expect(isValidChartData(validData)).toBe(true)
    })

    it('should validate empty arrays', () => {
      expect(isValidChartData([])).toBe(true)
    })

    it('should reject invalid chart data', () => {
      expect(isValidChartData(null)).toBe(false)
      expect(isValidChartData(undefined)).toBe(false)
      expect(isValidChartData('not-array')).toBe(false)
      expect(isValidChartData(123)).toBe(false)
      expect(isValidChartData({})).toBe(false)
    })

    it('should reject arrays with invalid items', () => {
      const invalidData = [
        { date: '2024-01-01', count: 10 },
        { invalidStructure: true },
        { date: '2024-01-03', count: 'invalid' }
      ]
      expect(isValidChartData(invalidData)).toBe(false)
    })

    it('should reject items missing required properties', () => {
      const missingDate = [{ count: 10 }]
      const missingCount = [{ date: '2024-01-01' }]
      
      expect(isValidChartData(missingDate)).toBe(false)
      expect(isValidChartData(missingCount)).toBe(false)
    })

    it('should reject items with wrong property types', () => {
      const wrongTypes = [
        { date: 123, count: '10' },
        { date: null, count: 10 },
        { date: '2024-01-01', count: null }
      ]
      expect(isValidChartData(wrongTypes)).toBe(false)
    })
  })

  describe('isValidLinkPerformanceData', () => {
    it('should validate correct link performance data', () => {
      const validData = [
        {
          id: 'link-1',
          title: 'My Website',
          url: 'https://example.com',
          totalClicks: 45,
          periodClicks: 25,
          isVisible: true
        },
        {
          id: 'link-2',
          title: 'GitHub',
          url: 'https://github.com/user',
          totalClicks: 0,
          periodClicks: 0,
          isVisible: false
        }
      ]
      expect(isValidLinkPerformanceData(validData)).toBe(true)
    })

    it('should validate empty arrays', () => {
      expect(isValidLinkPerformanceData([])).toBe(true)
    })

    it('should reject invalid link performance data', () => {
      expect(isValidLinkPerformanceData(null)).toBe(false)
      expect(isValidLinkPerformanceData(undefined)).toBe(false)
      expect(isValidLinkPerformanceData('not-array')).toBe(false)
    })

    it('should reject items missing required properties', () => {
      const missingId = [{ title: 'Test', url: 'https://example.com', totalClicks: 10, periodClicks: 5, isVisible: true }]
      const missingTitle = [{ id: 'link-1', url: 'https://example.com', totalClicks: 10, periodClicks: 5, isVisible: true }]
      
      expect(isValidLinkPerformanceData(missingId)).toBe(false)
      expect(isValidLinkPerformanceData(missingTitle)).toBe(false)
    })

    it('should reject items with wrong property types', () => {
      const wrongTypes = [
        {
          id: 123,
          title: 'Test',
          url: 'https://example.com',
          totalClicks: '10',
          periodClicks: 5,
          isVisible: 'true'
        }
      ]
      expect(isValidLinkPerformanceData(wrongTypes)).toBe(false)
    })

    it('should reject negative click counts', () => {
      const negativeClicks = [
        {
          id: 'link-1',
          title: 'Test',
          url: 'https://example.com',
          totalClicks: -5,
          periodClicks: 5,
          isVisible: true
        }
      ]
      expect(isValidLinkPerformanceData(negativeClicks)).toBe(false)
    })
  })

  describe('isValidReferrerData', () => {
    it('should validate correct referrer data', () => {
      const validData = [
        { referrer: 'https://google.com', count: 25 },
        { referrer: 'Direct', count: 15 },
        { referrer: 'https://twitter.com', count: 8 }
      ]
      expect(isValidReferrerData(validData)).toBe(true)
    })

    it('should validate empty arrays', () => {
      expect(isValidReferrerData([])).toBe(true)
    })

    it('should reject invalid referrer data', () => {
      expect(isValidReferrerData(null)).toBe(false)
      expect(isValidReferrerData(undefined)).toBe(false)
      expect(isValidReferrerData('not-array')).toBe(false)
    })

    it('should reject items with wrong property types', () => {
      const wrongTypes = [
        { referrer: 123, count: '25' },
        { referrer: null, count: 15 }
      ]
      expect(isValidReferrerData(wrongTypes)).toBe(false)
    })

    it('should reject negative counts', () => {
      const negativeCounts = [
        { referrer: 'https://google.com', count: -5 }
      ]
      expect(isValidReferrerData(negativeCounts)).toBe(false)
    })
  })

  describe('isValidAnalyticsDashboardResponse', () => {
    const validResponse = {
      overview: {
        totalViews: 100,
        totalClicks: 50,
        clickRate: 50.0,
        periodViews: 30,
        periodClicks: 15
      },
      charts: {
        viewsByDay: [{ date: '2024-01-01', count: 10 }],
        clicksByDay: [{ date: '2024-01-01', count: 5 }]
      },
      linkPerformance: [
        {
          id: 'link-1',
          title: 'Test',
          url: 'https://example.com',
          totalClicks: 25,
          periodClicks: 10,
          isVisible: true
        }
      ],
      topReferrers: [
        { referrer: 'https://google.com', count: 15 }
      ]
    }

    it('should validate correct dashboard response', () => {
      expect(isValidAnalyticsDashboardResponse(validResponse)).toBe(true)
    })

    it('should reject responses missing required sections', () => {
      const missingOverview = { ...validResponse }
      delete (missingOverview as any).overview
      expect(isValidAnalyticsDashboardResponse(missingOverview)).toBe(false)

      const missingCharts = { ...validResponse }
      delete (missingCharts as any).charts
      expect(isValidAnalyticsDashboardResponse(missingCharts)).toBe(false)
    })

    it('should reject responses with invalid overview', () => {
      const invalidOverview = {
        ...validResponse,
        overview: {
          totalViews: 'invalid',
          totalClicks: 50,
          clickRate: 50.0,
          periodViews: 30,
          periodClicks: 15
        }
      }
      expect(isValidAnalyticsDashboardResponse(invalidOverview)).toBe(false)
    })

    it('should reject responses with invalid charts', () => {
      const invalidCharts = {
        ...validResponse,
        charts: {
          viewsByDay: 'not-array',
          clicksByDay: [{ date: '2024-01-01', count: 5 }]
        }
      }
      expect(isValidAnalyticsDashboardResponse(invalidCharts)).toBe(false)
    })
  })

  describe('sanitizeChartData', () => {
    it('should sanitize valid chart data', () => {
      const validData = [
        { date: '2024-01-01', count: 10 },
        { date: '2024-01-02', count: 15 }
      ]
      const result = sanitizeChartData(validData, 'test')
      expect(result).toEqual(validData)
    })

    it('should filter out invalid items', () => {
      const mixedData = [
        { date: '2024-01-01', count: 10 },
        { invalidItem: true },
        { date: '2024-01-02', count: 15 },
        { date: null, count: 5 }
      ]
      const result = sanitizeChartData(mixedData as any, 'test')
      expect(result).toEqual([
        { date: '2024-01-01', count: 10 },
        { date: '2024-01-02', count: 15 }
      ])
    })

    it('should handle non-array input', () => {
      expect(sanitizeChartData(null as any, 'test')).toEqual([])
      expect(sanitizeChartData(undefined as any, 'test')).toEqual([])
      expect(sanitizeChartData('not-array' as any, 'test')).toEqual([])
    })

    it('should clamp negative counts to zero', () => {
      const dataWithNegatives = [
        { date: '2024-01-01', count: -5 },
        { date: '2024-01-02', count: 10 }
      ]
      const result = sanitizeChartData(dataWithNegatives, 'test')
      expect(result).toEqual([
        { date: '2024-01-01', count: 0 },
        { date: '2024-01-02', count: 10 }
      ])
    })
  })

  describe('sanitizeLinkPerformanceData', () => {
    it('should sanitize valid link performance data', () => {
      const validData = [
        {
          id: 'link-1',
          title: 'Test',
          url: 'https://example.com',
          totalClicks: 25,
          periodClicks: 10,
          isVisible: true
        }
      ]
      const result = sanitizeLinkPerformanceData(validData)
      expect(result).toEqual(validData)
    })

    it('should filter out invalid items', () => {
      const mixedData = [
        {
          id: 'link-1',
          title: 'Valid Link',
          url: 'https://example.com',
          totalClicks: 25,
          periodClicks: 10,
          isVisible: true
        },
        { invalidItem: true },
        {
          id: 'link-2',
          title: 'Another Valid Link',
          url: 'https://example2.com',
          totalClicks: 15,
          periodClicks: 5,
          isVisible: false
        }
      ]
      const result = sanitizeLinkPerformanceData(mixedData as any)
      expect(result).toHaveLength(2)
      expect(result[0].title).toBe('Valid Link')
      expect(result[1].title).toBe('Another Valid Link')
    })

    it('should clamp negative click counts', () => {
      const dataWithNegatives = [
        {
          id: 'link-1',
          title: 'Test',
          url: 'https://example.com',
          totalClicks: -5,
          periodClicks: -2,
          isVisible: true
        }
      ]
      const result = sanitizeLinkPerformanceData(dataWithNegatives)
      expect(result[0].totalClicks).toBe(0)
      expect(result[0].periodClicks).toBe(0)
    })

    it('should handle missing or invalid URLs', () => {
      const dataWithInvalidUrls = [
        {
          id: 'link-1',
          title: 'Test',
          url: '',
          totalClicks: 10,
          periodClicks: 5,
          isVisible: true
        },
        {
          id: 'link-2',
          title: 'Test 2',
          url: 'not-a-url',
          totalClicks: 15,
          periodClicks: 8,
          isVisible: true
        }
      ]
      const result = sanitizeLinkPerformanceData(dataWithInvalidUrls)
      // Should still include items but may sanitize URLs
      expect(result).toHaveLength(2)
    })
  })

  describe('sanitizeReferrerData', () => {
    it('should sanitize valid referrer data', () => {
      const validData = [
        { referrer: 'https://google.com', count: 25 },
        { referrer: 'Direct', count: 15 }
      ]
      const result = sanitizeReferrerData(validData)
      expect(result).toEqual(validData)
    })

    it('should filter out invalid items', () => {
      const mixedData = [
        { referrer: 'https://google.com', count: 25 },
        { invalidItem: true },
        { referrer: 'Direct', count: 15 }
      ]
      const result = sanitizeReferrerData(mixedData as any)
      expect(result).toEqual([
        { referrer: 'https://google.com', count: 25 },
        { referrer: 'Direct', count: 15 }
      ])
    })

    it('should clamp negative counts', () => {
      const dataWithNegatives = [
        { referrer: 'https://google.com', count: -5 },
        { referrer: 'Direct', count: 10 }
      ]
      const result = sanitizeReferrerData(dataWithNegatives)
      expect(result).toEqual([
        { referrer: 'https://google.com', count: 0 },
        { referrer: 'Direct', count: 10 }
      ])
    })

    it('should handle null referrers', () => {
      const dataWithNullReferrer = [
        { referrer: null, count: 10 },
        { referrer: 'https://google.com', count: 15 }
      ]
      const result = sanitizeReferrerData(dataWithNullReferrer as any)
      expect(result[0].referrer).toBe('Direct')
      expect(result[1].referrer).toBe('https://google.com')
    })
  })

  describe('validateApiParameters', () => {
    it('should validate correct API parameters', () => {
      const validParams = { period: '30' }
      const result = validateApiParameters(validParams)
      expect(result.period).toBe(30)
    })

    it('should use default period when not provided', () => {
      const emptyParams = {}
      const result = validateApiParameters(emptyParams)
      expect(result.period).toBe(30)
    })

    it('should reject invalid period values', () => {
      const invalidParams = { period: 'invalid' }
      expect(() => validateApiParameters(invalidParams)).toThrow()
    })

    it('should handle date range parameters', () => {
      const dateParams = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }
      const result = validateApiParameters(dateParams)
      expect(result.startDate).toBeInstanceOf(Date)
      expect(result.endDate).toBeInstanceOf(Date)
    })

    it('should reject invalid date formats', () => {
      const invalidDateParams = {
        startDate: 'invalid-date',
        endDate: '2024-01-31'
      }
      expect(() => validateApiParameters(invalidDateParams)).toThrow()
    })

    it('should reject end date before start date', () => {
      const invalidRangeParams = {
        startDate: '2024-01-31',
        endDate: '2024-01-01'
      }
      expect(() => validateApiParameters(invalidRangeParams)).toThrow()
    })
  })

  describe('validateResponseData', () => {
    const validResponseData = {
      overview: {
        totalViews: 100,
        totalClicks: 50,
        clickRate: 50.0,
        periodViews: 30,
        periodClicks: 15
      },
      charts: {
        viewsByDay: [{ date: '2024-01-01', count: 10 }],
        clicksByDay: [{ date: '2024-01-01', count: 5 }]
      },
      linkPerformance: [
        {
          id: 'link-1',
          title: 'Test',
          url: 'https://example.com',
          totalClicks: 25,
          periodClicks: 10,
          isVisible: true
        }
      ],
      topReferrers: [
        { referrer: 'https://google.com', count: 15 }
      ]
    }

    it('should validate and return correct response data', () => {
      const result = validateResponseData(validResponseData)
      expect(result).toEqual(validResponseData)
    })

    it('should throw error for invalid response structure', () => {
      const invalidResponse = { invalidStructure: true }
      expect(() => validateResponseData(invalidResponse)).toThrow()
    })

    it('should throw error for missing required sections', () => {
      const missingOverview = { ...validResponseData }
      delete (missingOverview as any).overview
      expect(() => validateResponseData(missingOverview)).toThrow()
    })

    it('should sanitize data during validation', () => {
      const responseWithInvalidData = {
        ...validResponseData,
        charts: {
          viewsByDay: [
            { date: '2024-01-01', count: 10 },
            { invalidItem: true }
          ],
          clicksByDay: [{ date: '2024-01-01', count: 5 }]
        }
      }
      
      const result = validateResponseData(responseWithInvalidData as any)
      expect(result.charts.viewsByDay).toHaveLength(1)
      expect(result.charts.viewsByDay[0]).toEqual({ date: '2024-01-01', count: 10 })
    })
  })

  describe('Edge Cases and Error Handling', () => {
    it('should handle extremely large numbers', () => {
      const largeNumberData = [
        { date: '2024-01-01', count: Number.MAX_SAFE_INTEGER },
        { date: '2024-01-02', count: Infinity }
      ]
      const result = sanitizeChartData(largeNumberData, 'test')
      expect(result[0].count).toBe(Number.MAX_SAFE_INTEGER)
      expect(isFinite(result[1].count)).toBe(true) // Should handle Infinity
    })

    it('should handle special string values', () => {
      const specialStringData = [
        { date: '', count: 10 },
        { date: '   ', count: 15 },
        { date: '\n\t', count: 20 }
      ]
      const result = sanitizeChartData(specialStringData as any, 'test')
      // Should filter out empty/whitespace-only dates
      expect(result.length).toBeLessThan(specialStringData.length)
    })

    it('should handle circular references in objects', () => {
      const circularData: any = { date: '2024-01-01', count: 10 }
      circularData.self = circularData
      
      const dataWithCircular = [circularData]
      
      // Should not crash when processing circular references
      expect(() => sanitizeChartData(dataWithCircular, 'test')).not.toThrow()
    })

    it('should handle prototype pollution attempts', () => {
      const maliciousData = [
        { date: '2024-01-01', count: 10, '__proto__': { polluted: true } },
        { date: '2024-01-02', count: 15, 'constructor': { prototype: { polluted: true } } }
      ]
      
      const result = sanitizeChartData(maliciousData as any, 'test')
      
      // Should sanitize the data and not pollute prototypes
      expect(result).toHaveLength(2)
      expect((Object.prototype as any).polluted).toBeUndefined()
    })
  })
})