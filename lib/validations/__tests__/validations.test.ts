import { usernameSchema } from '../../validations'

describe('usernameSchema', () => {
  describe('length validation', () => {
    it('should accept usernames with minimum 3 characters', () => {
      const result = usernameSchema.safeParse('abc')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with maximum 30 characters', () => {
      const result = usernameSchema.safeParse('a'.repeat(30))
      expect(result.success).toBe(true)
    })

    it('should reject usernames shorter than 3 characters', () => {
      const result = usernameSchema.safeParse('ab')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Username must be at least 3 characters')
      }
    })

    it('should reject usernames longer than 30 characters', () => {
      const result = usernameSchema.safeParse('a'.repeat(31))
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Username must be less than 30 characters')
      }
    })
  })

  describe('character validation', () => {
    it('should accept usernames with letters only', () => {
      const result = usernameSchema.safeParse('username')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with numbers only', () => {
      const result = usernameSchema.safeParse('123456')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with letters and numbers', () => {
      const result = usernameSchema.safeParse('user123')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with hyphens', () => {
      const result = usernameSchema.safeParse('user-name')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with underscores', () => {
      const result = usernameSchema.safeParse('user_name')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with mixed valid characters', () => {
      const result = usernameSchema.safeParse('user_name-123')
      expect(result.success).toBe(true)
    })

    it('should reject usernames with invalid characters', () => {
      const invalidChars = ['@', '#', '$', '%', '^', '&', '*', '(', ')', '+', '=', '[', ']', '{', '}', '|', '\\', ':', ';', '"', "'", '<', '>', ',', '.', '?', '/', '~', '`', ' ']
      
      invalidChars.forEach(char => {
        const result = usernameSchema.safeParse(`user${char}name`)
        expect(result.success).toBe(false)
        if (!result.success) {
          expect(result.error.issues[0].message).toBe('Username can only contain letters, numbers, hyphens, and underscores')
        }
      })
    })
  })

  describe('starting character validation', () => {
    it('should accept usernames starting with letters', () => {
      const result = usernameSchema.safeParse('username')
      expect(result.success).toBe(true)
    })

    it('should accept usernames starting with numbers', () => {
      const result = usernameSchema.safeParse('123user')
      expect(result.success).toBe(true)
    })

    it('should reject usernames starting with hyphen', () => {
      const result = usernameSchema.safeParse('-username')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot start with special characters'
        )).toBe(true)
      }
    })

    it('should reject usernames starting with underscore', () => {
      const result = usernameSchema.safeParse('_username')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot start with special characters'
        )).toBe(true)
      }
    })
  })

  describe('ending character validation', () => {
    it('should accept usernames ending with letters', () => {
      const result = usernameSchema.safeParse('username')
      expect(result.success).toBe(true)
    })

    it('should accept usernames ending with numbers', () => {
      const result = usernameSchema.safeParse('user123')
      expect(result.success).toBe(true)
    })

    it('should reject usernames ending with hyphen', () => {
      const result = usernameSchema.safeParse('username-')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot end with special characters'
        )).toBe(true)
      }
    })

    it('should reject usernames ending with underscore', () => {
      const result = usernameSchema.safeParse('username_')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot end with special characters'
        )).toBe(true)
      }
    })
  })

  describe('consecutive special characters validation', () => {
    it('should accept usernames with single hyphens', () => {
      const result = usernameSchema.safeParse('user-name')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with single underscores', () => {
      const result = usernameSchema.safeParse('user_name')
      expect(result.success).toBe(true)
    })

    it('should accept usernames with alternating special characters', () => {
      const result = usernameSchema.safeParse('user-name_test')
      expect(result.success).toBe(true)
    })

    it('should reject usernames with consecutive hyphens', () => {
      const result = usernameSchema.safeParse('user--name')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot contain consecutive special characters'
        )).toBe(true)
      }
    })

    it('should reject usernames with consecutive underscores', () => {
      const result = usernameSchema.safeParse('user__name')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot contain consecutive special characters'
        )).toBe(true)
      }
    })

    it('should reject usernames with mixed consecutive special characters', () => {
      const result = usernameSchema.safeParse('user-_name')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot contain consecutive special characters'
        )).toBe(true)
      }
    })

    it('should reject usernames with three consecutive special characters', () => {
      const result = usernameSchema.safeParse('user---name')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username cannot contain consecutive special characters'
        )).toBe(true)
      }
    })
  })

  describe('edge cases', () => {
    it('should accept valid usernames at boundary conditions', () => {
      const validUsernames = [
        'abc', // minimum length
        'a'.repeat(30), // maximum length
        'a1b', // mixed letters and numbers
        'user1name2test3', // mixed with numbers
        'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5', // 30 chars with numbers
      ]

      validUsernames.forEach(username => {
        const result = usernameSchema.safeParse(username)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid usernames with multiple validation errors', () => {
      const result = usernameSchema.safeParse('_user--name_')
      expect(result.success).toBe(false)
      if (!result.success) {
        const messages = result.error.issues.map(issue => issue.message)
        expect(messages).toContain('Username cannot start with special characters')
        expect(messages).toContain('Username cannot end with special characters')
        expect(messages).toContain('Username cannot contain consecutive special characters')
      }
    })

    it('should handle empty string', () => {
      const result = usernameSchema.safeParse('')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Username must be at least 3 characters')
      }
    })

    it('should handle whitespace', () => {
      const result = usernameSchema.safeParse('   ')
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.message === 'Username can only contain letters, numbers, hyphens, and underscores'
        )).toBe(true)
      }
    })
  })

  describe('case sensitivity', () => {
    it('should accept uppercase letters', () => {
      const result = usernameSchema.safeParse('USERNAME')
      expect(result.success).toBe(true)
    })

    it('should accept mixed case letters', () => {
      const result = usernameSchema.safeParse('UserName')
      expect(result.success).toBe(true)
    })

    it('should accept mixed case with numbers and special characters', () => {
      const result = usernameSchema.safeParse('User_Name123')
      expect(result.success).toBe(true)
    })
  })
})