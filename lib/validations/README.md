# Analytics Data Validation and Sanitization

This module provides comprehensive data validation and sanitization for analytics functionality in the LinksInBio application.

## Overview

The analytics validation system ensures data integrity and security by:

1. **Input Validation**: Validates API parameters before processing
2. **Response Data Structure Validation**: Ensures response data matches expected schemas
3. **Type Guards**: Provides runtime type checking for analytics data interfaces
4. **Data Sanitization**: Cleans and normalizes data before rendering in charts

## Features

### API Parameter Validation

- **Period Validation**: Ensures period is between 1-365 days
- **Profile ID Validation**: Validates profile ID format and length
- **Date Range Validation**: Validates start/end dates and ensures reasonable ranges

### Data Structure Validation

- **Chart Data**: Validates date format (YYYY-MM-DD) and non-negative counts
- **Link Performance**: Validates link IDs, titles, URLs, and click counts
- **Referrer Data**: Validates referrer names and counts
- **Analytics Overview**: Validates all numeric metrics and percentages

### Type Guards

Runtime type checking functions that return boolean values:

- `isValidProfileId(value)`: Checks if value is a valid profile ID
- `isValidPeriod(value)`: Checks if value is a valid period (1-365 days)
- `isValidChartData(value)`: Validates chart data array structure
- `isValidAnalyticsOverview(value)`: Validates overview metrics
- `isValidLinkPerformanceData(value)`: Validates link performance array
- `isValidReferrerData(value)`: Validates referrer data array
- `isValidAnalyticsDashboardResponse(value)`: Validates complete response structure

### Data Sanitization

Comprehensive data cleaning functions:

- `sanitizeChartData(data, chartType)`: Cleans chart data, removes invalid entries, sorts by date
- `sanitizeLinkPerformanceData(data)`: Cleans link data, truncates long strings, sorts by clicks
- `sanitizeReferrerData(data)`: Cleans referrer data, handles null referrers, sorts by count

## Usage Examples

### API Parameter Validation

```typescript
import { validateApiParameters } from '@/lib/validations/analytics'

try {
  const validatedParams = validateApiParameters({
    period: '30',
    startDate: undefined,
    endDate: undefined
  })
  // Use validatedParams.period (number)
} catch (error) {
  // Handle validation error
}
```

### Response Data Validation

```typescript
import { validateResponseData } from '@/lib/validations/analytics'

try {
  const validatedData = validateResponseData(rawAnalyticsData)
  // Data is guaranteed to match expected structure
} catch (error) {
  // Handle validation error
}
```

### Type Guards

```typescript
import { isValidChartData } from '@/lib/validations/analytics'

if (isValidChartData(data)) {
  // TypeScript knows data is ChartData[]
  data.forEach(item => {
    console.log(item.date, item.count) // Type-safe access
  })
}
```

### Data Sanitization

```typescript
import { sanitizeChartData } from '@/lib/validations/analytics'

const cleanData = sanitizeChartData(rawData, 'profile views')
// Returns clean, sorted data with invalid entries removed
```

## Error Handling

The validation system integrates with the analytics error handling system:

- Throws `AnalyticsError` instances with specific error codes
- Provides detailed error messages for debugging
- Logs validation issues for monitoring

## Security Features

- **Input Sanitization**: Prevents injection attacks through data cleaning
- **Length Limits**: Enforces maximum lengths for strings (titles, URLs, etc.)
- **Range Validation**: Ensures numeric values are within reasonable bounds
- **Type Safety**: Prevents type confusion attacks through strict typing

## Performance Considerations

- **Efficient Validation**: Uses Zod schemas for fast validation
- **Lazy Evaluation**: Only validates data when needed
- **Caching**: Validation results can be cached for repeated use
- **Minimal Overhead**: Designed to have minimal impact on API response times

## Integration Points

### API Routes
- `app/api/analytics/dashboard/route.ts`: Uses parameter and response validation

### Repository Layer
- `lib/repositories/analytics.ts`: Uses type guards and sanitization

### Component Layer
- `components/dashboard/analytics-chart.tsx`: Uses validation and sanitization for chart data

### Error Handling
- Integrates with `lib/errors/analytics-errors.ts` for consistent error reporting
- Uses `lib/utils/analytics-logger.ts` for comprehensive logging

## Validation Schemas

The module uses Zod schemas for validation:

- `periodSchema`: Validates time periods (1-365 days)
- `profileIdSchema`: Validates profile ID format
- `chartDataSchema`: Validates individual chart data points
- `analyticsOverviewSchema`: Validates overview metrics
- `linkPerformanceDataSchema`: Validates link performance data
- `referrerDataSchema`: Validates referrer information
- `analyticsDashboardResponseSchema`: Validates complete API responses

## Testing

The validation functions are designed to be easily testable:

```typescript
// Example test
import { isValidChartData, sanitizeChartData } from '@/lib/validations/analytics'

describe('Chart Data Validation', () => {
  it('should validate correct chart data', () => {
    const validData = [{ date: '2024-01-01', count: 5 }]
    expect(isValidChartData(validData)).toBe(true)
  })

  it('should sanitize invalid chart data', () => {
    const invalidData = [{ date: 'invalid', count: -1 }]
    const sanitized = sanitizeChartData(invalidData, 'test')
    expect(sanitized).toEqual([])
  })
})
```

## Future Enhancements

- **Schema Versioning**: Support for multiple API versions
- **Custom Validators**: Plugin system for custom validation rules
- **Performance Metrics**: Built-in performance monitoring
- **Batch Validation**: Efficient validation of large datasets