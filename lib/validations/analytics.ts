import { z } from 'zod'
import { AnalyticsErrorFactory } from '../errors/analytics-errors'
import { analyticsLogger } from '../utils/analytics-logger'

/**
 * Analytics API parameter validation schemas
 */

// Period validation - must be between 1 and 365 days
export const periodSchema = z.coerce.number()
  .int('Period must be a whole number')
  .min(1, 'Period must be at least 1 day')
  .max(365, 'Period cannot exceed 365 days')
  .default(30)

// Profile ID validation
export const profileIdSchema = z.string()
  .min(1, 'Profile ID cannot be empty')
  .max(50, 'Profile ID is too long')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Profile ID contains invalid characters')

// Date range validation
export const dateRangeSchema = z.object({
  startDate: z.coerce.date(),
  endDate: z.coerce.date()
}).refine(
  (data) => data.startDate < data.endDate,
  {
    message: 'Start date must be before end date',
    path: ['startDate']
  }
).refine(
  (data) => {
    const diffInDays = Math.ceil((data.endDate.getTime() - data.startDate.getTime()) / (1000 * 60 * 60 * 24))
    return diffInDays <= 365
  },
  {
    message: 'Date range cannot exceed 365 days',
    path: ['endDate']
  }
)

// Analytics dashboard request validation
export const analyticsDashboardRequestSchema = z.object({
  period: periodSchema.optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional()
}).refine(
  (data) => {
    // Either period OR date range should be provided, not both
    const hasPeriod = data.period !== undefined
    const hasDateRange = data.startDate !== undefined && data.endDate !== undefined
    return hasPeriod !== hasDateRange // XOR logic
  },
  {
    message: 'Provide either period or date range, not both',
    path: ['period']
  }
)

/**
 * Analytics data structure validation schemas
 */

// Chart data validation
export const chartDataSchema = z.object({
  date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
    .refine(
      (dateStr) => !isNaN(new Date(dateStr).getTime()),
      'Date must be a valid date'
    ),
  count: z.number()
    .int('Count must be a whole number')
    .min(0, 'Count cannot be negative')
})

export const chartDataArraySchema = z.array(chartDataSchema)
  .min(0, 'Chart data array cannot be null')
  .max(366, 'Chart data array cannot exceed 366 entries') // Max for leap year

// Analytics overview validation
export const analyticsOverviewSchema = z.object({
  totalViews: z.number().int().min(0),
  totalClicks: z.number().int().min(0),
  clickRate: z.number().min(0).max(100),
  periodViews: z.number().int().min(0),
  periodClicks: z.number().int().min(0)
})

// Link performance data validation
export const linkPerformanceDataSchema = z.object({
  id: z.string().min(1, 'Link ID cannot be empty'),
  title: z.string().min(1, 'Link title cannot be empty').max(200, 'Link title too long'),
  url: z.string().url('Invalid URL format').max(500, 'URL too long'),
  totalClicks: z.number().int().min(0),
  periodClicks: z.number().int().min(0),
  isVisible: z.boolean()
})

export const linkPerformanceArraySchema = z.array(linkPerformanceDataSchema)
  .max(100, 'Too many links in performance data')

// Referrer data validation
export const referrerDataSchema = z.object({
  referrer: z.string().max(100, 'Referrer name too long'),
  count: z.number().int().min(0)
})

export const referrerDataArraySchema = z.array(referrerDataSchema)
  .max(50, 'Too many referrers in data')

// Complete analytics dashboard response validation
export const analyticsDashboardResponseSchema = z.object({
  overview: analyticsOverviewSchema,
  charts: z.object({
    viewsByDay: chartDataArraySchema,
    clicksByDay: chartDataArraySchema
  }),
  linkPerformance: linkPerformanceArraySchema,
  topReferrers: referrerDataArraySchema
})

/**
 * Type guards for analytics data interfaces
 */

export function isValidProfileId(value: unknown): value is string {
  try {
    profileIdSchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidPeriod(value: unknown): value is number {
  try {
    periodSchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidChartData(value: unknown): value is Array<{ date: string; count: number }> {
  try {
    chartDataArraySchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidAnalyticsOverview(value: unknown): value is {
  totalViews: number
  totalClicks: number
  clickRate: number
  periodViews: number
  periodClicks: number
} {
  try {
    analyticsOverviewSchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidLinkPerformanceData(value: unknown): value is Array<{
  id: string
  title: string
  url: string
  totalClicks: number
  periodClicks: number
  isVisible: boolean
}> {
  try {
    linkPerformanceArraySchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidReferrerData(value: unknown): value is Array<{
  referrer: string
  count: number
}> {
  try {
    referrerDataArraySchema.parse(value)
    return true
  } catch {
    return false
  }
}

export function isValidAnalyticsDashboardResponse(value: unknown): value is {
  overview: {
    totalViews: number
    totalClicks: number
    clickRate: number
    periodViews: number
    periodClicks: number
  }
  charts: {
    viewsByDay: Array<{ date: string; count: number }>
    clicksByDay: Array<{ date: string; count: number }>
  }
  linkPerformance: Array<{
    id: string
    title: string
    url: string
    totalClicks: number
    periodClicks: number
    isVisible: boolean
  }>
  topReferrers: Array<{
    referrer: string
    count: number
  }>
} {
  try {
    analyticsDashboardResponseSchema.parse(value)
    return true
  } catch {
    return false
  }
}

/**
 * Data sanitization functions
 */

export function sanitizeChartData(
  data: unknown,
  chartType: string = 'unknown'
): Array<{ date: string; count: number }> {
  try {
    // First validate the data structure
    if (!Array.isArray(data)) {
      analyticsLogger.warn(`Invalid chart data structure for ${chartType}`, {
        operation: 'sanitizeChartData',
        chartType,
        dataType: typeof data
      })
      return []
    }

    const sanitized = data
      .filter((item): item is { date: string; count: number } => {
        if (!item || typeof item !== 'object') return false
        
        const hasValidDate = typeof item.date === 'string' && 
          /^\d{4}-\d{2}-\d{2}$/.test(item.date) &&
          !isNaN(new Date(item.date).getTime())
        
        const hasValidCount = typeof item.count === 'number' && 
          !isNaN(item.count) && 
          item.count >= 0

        if (!hasValidDate || !hasValidCount) {
          analyticsLogger.warn(`Filtering invalid chart data item for ${chartType}`, {
            operation: 'sanitizeChartData',
            chartType,
            item,
            hasValidDate,
            hasValidCount
          })
        }

        return hasValidDate && hasValidCount
      })
      .map(item => ({
        date: item.date.trim(),
        count: Math.max(0, Math.floor(item.count))
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    // Validate the final result
    const validationResult = chartDataArraySchema.safeParse(sanitized)
    if (!validationResult.success) {
      analyticsLogger.error(`Chart data sanitization failed validation for ${chartType}`, {
        operation: 'sanitizeChartData',
        chartType,
        errors: validationResult.error.errors
      })
      return []
    }

    if (sanitized.length !== data.length) {
      analyticsLogger.info(`Chart data sanitization completed for ${chartType}`, {
        operation: 'sanitizeChartData',
        chartType,
        originalCount: data.length,
        sanitizedCount: sanitized.length
      })
    }

    return sanitized
  } catch (error) {
    analyticsLogger.error(`Error during chart data sanitization for ${chartType}`, {
      operation: 'sanitizeChartData',
      chartType
    }, error as Error)
    return []
  }
}

export function sanitizeLinkPerformanceData(
  data: unknown
): Array<{
  id: string
  title: string
  url: string
  totalClicks: number
  periodClicks: number
  isVisible: boolean
}> {
  try {
    if (!Array.isArray(data)) {
      analyticsLogger.warn('Invalid link performance data structure', {
        operation: 'sanitizeLinkPerformanceData',
        dataType: typeof data
      })
      return []
    }

    const sanitized = data
      .filter((item): item is {
        id: string
        title: string
        url: string
        totalClicks: number
        periodClicks: number
        isVisible: boolean
      } => {
        if (!item || typeof item !== 'object') return false
        
        const hasValidId = typeof (item as Record<string, unknown>).id === 'string' && 
          (item as Record<string, unknown>).id.length > 0
        const hasValidTitle = typeof (item as Record<string, unknown>).title === 'string' && 
          (item as Record<string, unknown>).title.length > 0
        const hasValidUrl = typeof (item as Record<string, unknown>).url === 'string' && 
          (item as Record<string, unknown>).url.length > 0
        const hasValidClicks = typeof (item as Record<string, unknown>).totalClicks === 'number' && 
          typeof (item as Record<string, unknown>).periodClicks === 'number' &&
          !isNaN((item as Record<string, unknown>).totalClicks as number) && 
          !isNaN((item as Record<string, unknown>).periodClicks as number)
        const hasValidVisibility = typeof (item as Record<string, unknown>).isVisible === 'boolean'

        return hasValidId && hasValidTitle && hasValidUrl && hasValidClicks && hasValidVisibility
      })
      .map(item => ({
        id: String(item.id).trim(),
        title: String(item.title).trim().substring(0, 200),
        url: String(item.url).trim().substring(0, 500),
        totalClicks: Math.max(0, Math.floor(Number(item.totalClicks))),
        periodClicks: Math.max(0, Math.floor(Number(item.periodClicks))),
        isVisible: Boolean(item.isVisible)
      }))
      .sort((a, b) => b.totalClicks - a.totalClicks)

    // Validate the final result
    const validationResult = linkPerformanceArraySchema.safeParse(sanitized)
    if (!validationResult.success) {
      analyticsLogger.error('Link performance data sanitization failed validation', {
        operation: 'sanitizeLinkPerformanceData',
        errors: validationResult.error.errors
      })
      return []
    }

    return sanitized
  } catch (error) {
    analyticsLogger.error('Error during link performance data sanitization', {
      operation: 'sanitizeLinkPerformanceData'
    }, error as Error)
    return []
  }
}

export function sanitizeReferrerData(
  data: unknown
): Array<{ referrer: string; count: number }> {
  try {
    if (!Array.isArray(data)) {
      analyticsLogger.warn('Invalid referrer data structure', {
        operation: 'sanitizeReferrerData',
        dataType: typeof data
      })
      return []
    }

    const sanitized = data
      .filter((item): item is {
        referrer: string
        count: number
      } => {
        if (!item || typeof item !== 'object') return false
        
        const hasValidReferrer = typeof (item as Record<string, unknown>).referrer === 'string'
        const hasValidCount = typeof (item as Record<string, unknown>).count === 'number' && 
          !isNaN((item as Record<string, unknown>).count as number) && 
          (item as Record<string, unknown>).count > 0

        return hasValidReferrer && hasValidCount
      })
      .map(item => ({
        referrer: String(item.referrer || 'Direct').trim().substring(0, 100),
        count: Math.max(0, Math.floor(Number(item.count)))
      }))
      .filter(item => item.count > 0)
      .sort((a, b) => b.count - a.count)

    // Validate the final result
    const validationResult = referrerDataArraySchema.safeParse(sanitized)
    if (!validationResult.success) {
      analyticsLogger.error('Referrer data sanitization failed validation', {
        operation: 'sanitizeReferrerData',
        errors: validationResult.error.errors
      })
      return []
    }

    return sanitized
  } catch (error) {
    analyticsLogger.error('Error during referrer data sanitization', {
      operation: 'sanitizeReferrerData'
    }, error as Error)
    return []
  }
}

/**
 * Validation helper functions
 */

export function validateApiParameters(params: {
  period?: string | number
  startDate?: string | Date
  endDate?: string | Date
}): {
  period?: number
  startDate?: Date
  endDate?: Date
} {
  try {
    const validationResult = analyticsDashboardRequestSchema.safeParse(params)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      throw AnalyticsErrorFactory.dataValidationError(
        'API parameters',
        params,
        `Valid analytics request parameters. Errors: ${errors.join(', ')}`
      )
    }

    return validationResult.data
  } catch (error) {
    if (error instanceof Error && error.name === 'AnalyticsError') {
      throw error
    }
    throw AnalyticsErrorFactory.dataValidationError('API parameters', params, 'Valid analytics request parameters')
  }
}

export function validateResponseData(data: unknown): {
  overview: {
    totalViews: number
    totalClicks: number
    clickRate: number
    periodViews: number
    periodClicks: number
  }
  charts: {
    viewsByDay: Array<{ date: string; count: number }>
    clicksByDay: Array<{ date: string; count: number }>
  }
  linkPerformance: Array<{
    id: string
    title: string
    url: string
    totalClicks: number
    periodClicks: number
    isVisible: boolean
  }>
  topReferrers: Array<{
    referrer: string
    count: number
  }>
} {
  try {
    const validationResult = analyticsDashboardResponseSchema.safeParse(data)
    
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      throw AnalyticsErrorFactory.dataValidationError(
        'Response data structure',
        data,
        `Valid analytics response data. Errors: ${errors.join(', ')}`
      )
    }

    return validationResult.data
  } catch (error) {
    if (error instanceof Error && error.name === 'AnalyticsError') {
      throw error
    }
    throw AnalyticsErrorFactory.dataValidationError('Response data structure', data, 'Valid analytics response data')
  }
}

/**
 * Type exports for external use
 */
export type AnalyticsDashboardRequest = z.infer<typeof analyticsDashboardRequestSchema>
export type AnalyticsDashboardResponse = z.infer<typeof analyticsDashboardResponseSchema>
export type ChartData = z.infer<typeof chartDataSchema>
export type AnalyticsOverview = z.infer<typeof analyticsOverviewSchema>
export type LinkPerformanceData = z.infer<typeof linkPerformanceDataSchema>
export type ReferrerData = z.infer<typeof referrerDataSchema>