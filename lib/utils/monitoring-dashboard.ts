/**
 * Monitoring dashboard utility for username availability feature
 * Provides formatted metrics and dashboard data
 */

import { performanceMonitor, type MonitoringStats } from './performance-monitor'

export interface DashboardData {
  summary: {
    status: 'healthy' | 'warning' | 'critical'
    uptime: string
    lastUpdated: string
  }
  performance: {
    averageResponseTime: number
    successRate: number
    totalRequests: number
    slowQueries: number
    timeouts: number
    p95ResponseTime: number
    p99ResponseTime: number
  }
  cache: {
    hitRate: number
    missRate: number
    totalOperations: number
    efficiency: 'excellent' | 'good' | 'poor'
  }
  errors: {
    totalErrors: number
    errorRate: number
    topErrors: Array<{ type: string; count: number; percentage: number }>
    recentErrors: Array<{
      timestamp: string
      operation: string
      error: string
      errorCode?: string
    }>
  }
  trends: {
    responseTimeHistory: Array<{ timestamp: string; value: number }>
    errorRateHistory: Array<{ timestamp: string; value: number }>
    cacheHitRateHistory: Array<{ timestamp: string; value: number }>
  }
}

class MonitoringDashboard {
  private startTime = Date.now()

  /**
   * Get comprehensive dashboard data
   */
  getDashboardData(): DashboardData {
    const stats = performanceMonitor.getStats()
    const rawMetrics = performanceMonitor.getRawMetrics()

    return {
      summary: this.getSummary(stats),
      performance: this.getPerformanceData(stats, rawMetrics),
      cache: this.getCacheData(stats),
      errors: this.getErrorData(stats, rawMetrics),
      trends: this.getTrendsData(rawMetrics)
    }
  }

  /**
   * Get system health summary
   */
  private getSummary(stats: MonitoringStats): DashboardData['summary'] {
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'

    // Determine status based on metrics
    if (stats.errors.errorRate > 10 || stats.performance.successRate < 90) {
      status = 'critical'
    } else if (stats.errors.errorRate > 5 || stats.performance.successRate < 95 || stats.performance.averageResponseTime > 2000) {
      status = 'warning'
    }

    const uptime = this.formatUptime(Date.now() - this.startTime)

    return {
      status,
      uptime,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get detailed performance data
   */
  private getPerformanceData(stats: MonitoringStats, rawMetrics: any): DashboardData['performance'] {
    const performanceMetrics = rawMetrics.performance
    const responseTimes = performanceMetrics.map((m: any) => m.duration).sort((a: number, b: number) => a - b)
    
    const p95Index = Math.floor(responseTimes.length * 0.95)
    const p99Index = Math.floor(responseTimes.length * 0.99)
    
    return {
      averageResponseTime: stats.performance.averageResponseTime,
      successRate: stats.performance.successRate,
      totalRequests: stats.performance.totalRequests,
      slowQueries: stats.performance.slowQueries,
      timeouts: stats.performance.timeouts,
      p95ResponseTime: responseTimes[p95Index] || 0,
      p99ResponseTime: responseTimes[p99Index] || 0
    }
  }

  /**
   * Get cache performance data
   */
  private getCacheData(stats: MonitoringStats): DashboardData['cache'] {
    let efficiency: 'excellent' | 'good' | 'poor' = 'poor'
    
    if (stats.cache.hitRate >= 80) {
      efficiency = 'excellent'
    } else if (stats.cache.hitRate >= 60) {
      efficiency = 'good'
    }

    return {
      hitRate: stats.cache.hitRate,
      missRate: stats.cache.missRate,
      totalOperations: stats.cache.totalOperations,
      efficiency
    }
  }

  /**
   * Get error analysis data
   */
  private getErrorData(stats: MonitoringStats, rawMetrics: any): DashboardData['errors'] {
    const errorMetrics = rawMetrics.errors
    const totalErrors = stats.errors.totalErrors

    // Calculate top errors with percentages
    const topErrors = Object.entries(stats.errors.errorsByType)
      .map(([type, count]) => ({
        type,
        count: count as number,
        percentage: totalErrors > 0 ? Math.round(((count as number) / totalErrors) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // Get recent errors (last 10)
    const recentErrors = errorMetrics
      .slice(-10)
      .map((error: any) => ({
        timestamp: new Date(error.timestamp).toISOString(),
        operation: error.operation,
        error: error.error,
        errorCode: error.errorCode
      }))
      .reverse()

    return {
      totalErrors,
      errorRate: stats.errors.errorRate,
      topErrors,
      recentErrors
    }
  }

  /**
   * Get trend data for charts
   */
  private getTrendsData(rawMetrics: any): DashboardData['trends'] {
    const now = Date.now()
    const oneHourAgo = now - (60 * 60 * 1000)
    const intervals = 12 // 5-minute intervals over 1 hour
    const intervalDuration = (60 * 60 * 1000) / intervals

    const responseTimeHistory: Array<{ timestamp: string; value: number }> = []
    const errorRateHistory: Array<{ timestamp: string; value: number }> = []
    const cacheHitRateHistory: Array<{ timestamp: string; value: number }> = []

    for (let i = 0; i < intervals; i++) {
      const intervalStart = oneHourAgo + (i * intervalDuration)
      const intervalEnd = intervalStart + intervalDuration
      const timestamp = new Date(intervalEnd).toISOString()

      // Filter metrics for this interval
      const intervalPerformance = rawMetrics.performance.filter(
        (m: any) => m.timestamp >= intervalStart && m.timestamp < intervalEnd
      )
      const intervalCache = rawMetrics.cache.filter(
        (m: any) => m.timestamp >= intervalStart && m.timestamp < intervalEnd
      )
      const intervalErrors = rawMetrics.errors.filter(
        (m: any) => m.timestamp >= intervalStart && m.timestamp < intervalEnd
      )

      // Calculate average response time for interval
      const avgResponseTime = intervalPerformance.length > 0
        ? intervalPerformance.reduce((sum: number, m: any) => sum + m.duration, 0) / intervalPerformance.length
        : 0

      // Calculate error rate for interval
      const errorRate = intervalPerformance.length > 0
        ? (intervalErrors.length / intervalPerformance.length) * 100
        : 0

      // Calculate cache hit rate for interval
      const cacheHits = intervalCache.filter((m: any) => m.operation === 'hit').length
      const cacheMisses = intervalCache.filter((m: any) => m.operation === 'miss').length
      const totalCacheOps = cacheHits + cacheMisses
      const hitRate = totalCacheOps > 0 ? (cacheHits / totalCacheOps) * 100 : 0

      responseTimeHistory.push({ timestamp, value: Math.round(avgResponseTime) })
      errorRateHistory.push({ timestamp, value: Math.round(errorRate * 100) / 100 })
      cacheHitRateHistory.push({ timestamp, value: Math.round(hitRate * 100) / 100 })
    }

    return {
      responseTimeHistory,
      errorRateHistory,
      cacheHitRateHistory
    }
  }

  /**
   * Format uptime duration
   */
  private formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  /**
   * Generate monitoring report
   */
  generateReport(): string {
    const data = this.getDashboardData()
    
    return `
# Username Availability Monitoring Report

## System Status: ${data.summary.status.toUpperCase()}
- Uptime: ${data.summary.uptime}
- Last Updated: ${new Date(data.summary.lastUpdated).toLocaleString()}

## Performance Metrics
- Average Response Time: ${data.performance.averageResponseTime}ms
- Success Rate: ${data.performance.successRate}%
- Total Requests: ${data.performance.totalRequests}
- Slow Queries: ${data.performance.slowQueries}
- Timeouts: ${data.performance.timeouts}
- P95 Response Time: ${data.performance.p95ResponseTime}ms
- P99 Response Time: ${data.performance.p99ResponseTime}ms

## Cache Performance
- Hit Rate: ${data.cache.hitRate}%
- Miss Rate: ${data.cache.missRate}%
- Total Operations: ${data.cache.totalOperations}
- Efficiency: ${data.cache.efficiency}

## Error Analysis
- Total Errors: ${data.errors.totalErrors}
- Error Rate: ${data.errors.errorRate}%

### Top Error Types:
${data.errors.topErrors.map(error => 
  `- ${error.type}: ${error.count} (${error.percentage}%)`
).join('\n')}

### Recent Errors:
${data.errors.recentErrors.slice(0, 5).map(error => 
  `- ${new Date(error.timestamp).toLocaleString()}: ${error.error} (${error.errorCode || 'N/A'})`
).join('\n')}
    `.trim()
  }

  /**
   * Get health check status
   */
  getHealthCheck(): { status: string; checks: Record<string, boolean> } {
    const stats = performanceMonitor.getStats()
    
    const checks = {
      responseTime: stats.performance.averageResponseTime < 2000,
      successRate: stats.performance.successRate >= 95,
      errorRate: stats.errors.errorRate < 5,
      cacheEfficiency: stats.cache.hitRate >= 60
    }

    const allHealthy = Object.values(checks).every(check => check)
    const status = allHealthy ? 'healthy' : 'unhealthy'

    return { status, checks }
  }
}

// Export singleton instance
export const monitoringDashboard = new MonitoringDashboard()

// Export for testing
export const __testExports = {
  MonitoringDashboard,
  monitoringDashboard
}