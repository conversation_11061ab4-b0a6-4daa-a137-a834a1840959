# Visitor Context Detection System

A comprehensive, privacy-compliant visitor context detection system for analytics tracking. This system provides detailed visitor insights while maintaining user privacy and GDPR compliance.

## Features

### 🔍 Device Detection
- **User Agent Parsing**: Uses `ua-parser-js` for accurate browser, OS, and device detection
- **Device Categorization**: Automatically categorizes visitors as mobile, tablet, or desktop
- **Bot Detection**: Identifies automated traffic and crawlers
- **Browser Family Normalization**: Standardizes browser names for consistent reporting

### 🌍 Location Detection
- **Vercel Geo Headers**: Leverages Vercel's built-in geolocation headers
- **IP-based Location**: Extracts country, region, city, and timezone information
- **Privacy-Safe Coordinates**: Handles latitude/longitude with proper sanitization
- **Timezone Detection**: Multi-source timezone detection with fallbacks

### 🔗 Referrer Analysis
- **UTM Parameter Extraction**: Captures campaign, source, and medium parameters
- **Traffic Source Classification**: Categorizes traffic as search, social, referral, or direct
- **Internal vs External**: Distinguishes between internal and external referrers
- **Domain Sanitization**: Cleans and normalizes referrer domains

### 🔒 Privacy Compliance
- **IP Address Hashing**: Privacy-safe IP hashing with salt rotation
- **Data Anonymization**: Automatic anonymization of old data
- **Consent Management**: Filters data based on user consent preferences
- **GDPR Compliance**: Built-in data retention and deletion policies

## Installation

```bash
npm install ua-parser-js
npm install --save-dev @types/ua-parser-js
```

## Usage

### Basic Visitor Context Creation

```typescript
import { createVisitorContext } from '@/lib/utils/visitor-context'

// In your API route
export async function POST(request: NextRequest) {
  const headers = request.headers
  const userAgent = headers.get('user-agent') || ''
  const referrer = headers.get('referer')
  const currentDomain = headers.get('host')?.split(':')[0] || 'localhost'

  const context = createVisitorContext(
    userAgent,
    headers,
    referrer,
    currentDomain
  )

  // Use context for analytics tracking
  await AnalyticsRepository.trackProfileView(
    profileId,
    userAgent,
    headers,
    referrer,
    currentDomain
  )
}
```

### Advanced Analytics

```typescript
import { 
  generateVisitorInsights,
  aggregateVisitorAnalytics,
  calculateEngagementScore 
} from '@/lib/utils/visitor-analytics'

// Generate insights from visitor context
const insights = generateVisitorInsights(context, previousVisits, sessionStart)

// Calculate engagement score
const engagementScore = calculateEngagementScore(
  context,
  insights,
  pageViews,
  timeOnSite,
  interactions
)

// Aggregate multiple visitor contexts for reporting
const analytics = aggregateVisitorAnalytics(contexts, insights)
```

### Privacy-Compliant IP Handling

```typescript
import { 
  createPrivacyCompliantIPHash,
  anonymizeIPAddress,
  filterContextByConsent 
} from '@/lib/utils/privacy-utils'

// Create privacy-safe IP hash
const ipHash = createPrivacyCompliantIPHash(ipAddress)

// Anonymize IP address
const anonymizedIP = anonymizeIPAddress(ipAddress)

// Filter context based on user consent
const filteredContext = filterContextByConsent(context, {
  analytics: true,
  geolocation: false, // User declined location tracking
  fingerprinting: true,
  advertising: false
})
```

## Data Structures

### VisitorContext

```typescript
interface VisitorContext {
  device: DeviceInfo
  location: LocationInfo | null
  referrer: ReferrerInfo | null
  timezone: string
  ipHash: string | null
  timestamp: Date
}
```

### DeviceInfo

```typescript
interface DeviceInfo {
  browser: { name: string; version: string }
  os: { name: string; version: string }
  device: { type: string; vendor: string; model: string }
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}
```

### LocationInfo

```typescript
interface LocationInfo {
  country: string
  region: string
  city: string
  latitude: string
  longitude: string
  timezone: string
}
```

### ReferrerInfo

```typescript
interface ReferrerInfo {
  domain: string
  source: string
  medium: string
  campaign: string | null
  isInternal: boolean
  isSocial: boolean
  isSearch: boolean
}
```

## Privacy Features

### Automatic Data Anonymization

The system automatically anonymizes visitor data based on configurable retention policies:

```typescript
// Check if data should be anonymized (default: 7 days)
if (shouldAnonymizeData(timestamp)) {
  const anonymized = anonymizeVisitorContext(context)
  // Store anonymized version
}

// Check if data should be deleted (default: 90 days)
if (shouldDeleteData(timestamp)) {
  // Delete the record
}
```

### IP Address Privacy

- **Anonymization**: Removes last octet from IPv4 and last 64 bits from IPv6
- **Hashing**: Uses SHA-256 with rotating salts
- **No Storage**: Raw IP addresses are never stored in the database

### Consent Management

```typescript
const consentConfig = {
  analytics: true,      // Basic analytics tracking
  geolocation: false,   // Location-based tracking
  fingerprinting: true, // Device fingerprinting
  advertising: false    // Advertising-related tracking
}

const filteredContext = filterContextByConsent(context, consentConfig)
```

## Database Schema Updates

The system requires enhanced database fields for comprehensive tracking:

```sql
-- Enhanced ProfileView table
ALTER TABLE ProfileView ADD COLUMN city TEXT;
ALTER TABLE ProfileView ADD COLUMN region TEXT;
ALTER TABLE ProfileView ADD COLUMN timezone TEXT;
ALTER TABLE ProfileView ADD COLUMN deviceType TEXT;
ALTER TABLE ProfileView ADD COLUMN osName TEXT;
ALTER TABLE ProfileView ADD COLUMN browserName TEXT;
ALTER TABLE ProfileView ADD COLUMN isMobile BOOLEAN DEFAULT FALSE;
ALTER TABLE ProfileView ADD COLUMN referrerSource TEXT;
ALTER TABLE ProfileView ADD COLUMN referrerMedium TEXT;
ALTER TABLE ProfileView ADD COLUMN isInternalReferrer BOOLEAN DEFAULT FALSE;

-- Similar updates for LinkClick table
-- (See prisma/schema.prisma for complete schema)
```

## Analytics Insights

### Device Analytics

```typescript
const deviceBreakdown = {
  mobile: 45,    // 45% mobile traffic
  tablet: 10,    // 10% tablet traffic
  desktop: 45    // 45% desktop traffic
}

const browserBreakdown = {
  'Chrome': 60,
  'Safari': 25,
  'Firefox': 10,
  'Edge': 5
}
```

### Traffic Source Analysis

```typescript
const trafficSources = {
  direct: 30,    // 30% direct traffic
  search: 40,    // 40% from search engines
  social: 20,    // 20% from social media
  referral: 8,   // 8% from other websites
  internal: 2    // 2% internal navigation
}
```

### Geographic Distribution

```typescript
const locationBreakdown = {
  'US-CA': 25,   // 25% from California, US
  'US-NY': 15,   // 15% from New York, US
  'GB': 12,      // 12% from United Kingdom
  'DE': 8        // 8% from Germany
}
```

## Performance Considerations

### Optimized Processing

- **Parallel Queries**: Database queries execute in parallel for better performance
- **Chunked Processing**: Large datasets are processed in chunks to avoid memory issues
- **Caching**: Frequently accessed data is cached for faster retrieval
- **Lazy Loading**: Optional data is loaded only when needed

### Memory Management

- **String Length Limits**: All string inputs are limited to prevent memory issues
- **Data Sanitization**: Invalid data is filtered out early in the process
- **Garbage Collection**: Temporary objects are properly cleaned up

## Security Features

### Input Validation

- **User Agent Sanitization**: Malicious user agents are detected and handled
- **IP Address Validation**: Only valid IP addresses are processed
- **Referrer Sanitization**: Referrer URLs are validated and sanitized
- **Length Limits**: All inputs have reasonable length limits

### XSS Prevention

- **HTML Encoding**: All user inputs are properly encoded
- **Script Tag Removal**: Potential script tags are stripped from inputs
- **URL Validation**: Referrer URLs are validated before processing

## Testing

The system includes comprehensive test suites:

```bash
# Run visitor context tests
npm test -- lib/utils/__tests__/visitor-context.test.ts

# Run visitor analytics tests
npm test -- lib/utils/__tests__/visitor-analytics.test.ts

# Run privacy utils tests
npm test -- lib/utils/__tests__/privacy-utils.test.ts

# Run all visitor context related tests
npm test -- lib/utils/__tests__/
```

### Test Coverage

- ✅ User agent parsing (39 tests)
- ✅ Location detection (8 tests)
- ✅ Referrer analysis (7 tests)
- ✅ Privacy compliance (37 tests)
- ✅ Analytics insights (27 tests)
- ✅ Edge cases and security (15+ tests)

## Environment Variables

```env
# IP hashing salt (rotated automatically)
IP_HASH_SALT=your-secret-salt-here

# Privacy configuration
VISITOR_CONTEXT_ANONYMIZE_AFTER_DAYS=7
VISITOR_CONTEXT_RETENTION_DAYS=90
VISITOR_CONTEXT_ENABLE_GEOLOCATION=true
VISITOR_CONTEXT_ENABLE_FINGERPRINTING=true
```

## Compliance

### GDPR Compliance

- ✅ **Data Minimization**: Only necessary data is collected
- ✅ **Purpose Limitation**: Data is used only for stated purposes
- ✅ **Storage Limitation**: Data is automatically deleted after retention period
- ✅ **Accuracy**: Data validation ensures accuracy
- ✅ **Integrity**: Data is protected from unauthorized access
- ✅ **Accountability**: Full audit trail of data processing

### CCPA Compliance

- ✅ **Transparency**: Clear disclosure of data collection
- ✅ **Consumer Rights**: Support for data deletion requests
- ✅ **Opt-Out**: Respect for do-not-sell preferences
- ✅ **Data Security**: Appropriate security measures

## Migration Guide

### From Basic Analytics

If you're migrating from a basic analytics system:

1. **Update Database Schema**: Run the Prisma migration to add new fields
2. **Update API Routes**: Replace old tracking calls with new visitor context system
3. **Update Analytics Queries**: Use new fields for enhanced reporting
4. **Test Privacy Features**: Ensure compliance with your privacy policy

### Example Migration

```typescript
// Old way
await AnalyticsRepository.trackProfileView(profileId, {
  userAgent: request.headers.get('user-agent'),
  ip: getClientIP(request),
  referrer: request.headers.get('referer')
})

// New way
await AnalyticsRepository.trackProfileView(
  profileId,
  request.headers.get('user-agent') || '',
  request.headers,
  request.headers.get('referer'),
  getCurrentDomain(request)
)
```

## Troubleshooting

### Common Issues

1. **Invalid User Agent**: System gracefully handles malformed user agents
2. **Missing Location Data**: Falls back to timezone detection from other sources
3. **Bot Traffic**: Automatically detected and flagged in analytics
4. **Privacy Compliance**: Built-in anonymization and deletion policies

### Debug Mode

Enable debug logging for troubleshooting:

```typescript
// Enable debug logging in development
if (process.env.NODE_ENV === 'development') {
  console.log('Visitor Context:', context)
  console.log('Visitor Insights:', insights)
}
```

## Contributing

When contributing to the visitor context system:

1. **Add Tests**: All new features must include comprehensive tests
2. **Privacy First**: Consider privacy implications of any new data collection
3. **Performance**: Ensure new features don't impact performance
4. **Documentation**: Update this README with any new features

## License

This visitor context detection system is part of the Links in Bio project and follows the same license terms.