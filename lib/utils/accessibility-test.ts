/**
 * Accessibility testing utilities for development
 */

// Check if all images have alt text
export function checkImageAltText(): void {
  if (typeof window === 'undefined') return
  
  const images = document.querySelectorAll('img')
  const imagesWithoutAlt: HTMLImageElement[] = []
  
  images.forEach(img => {
    if (!img.alt && !img.getAttribute('aria-hidden')) {
      imagesWithoutAlt.push(img)
    }
  })
  
  if (imagesWithoutAlt.length > 0) {
    console.warn('Images without alt text found:', imagesWithoutAlt)
  }
}

// Check if all form inputs have labels
export function checkFormLabels(): void {
  if (typeof window === 'undefined') return
  
  const inputs = document.querySelectorAll('input, select, textarea')
  const inputsWithoutLabels: Element[] = []
  
  inputs.forEach(input => {
    const id = input.id
    const ariaLabel = input.getAttribute('aria-label')
    const ariaLabelledBy = input.getAttribute('aria-labelledby')
    const label = id ? document.querySelector(`label[for="${id}"]`) : null
    
    if (!label && !ariaLabel && !ariaLabelledBy) {
      inputsWithoutLabels.push(input)
    }
  })
  
  if (inputsWithoutLabels.length > 0) {
    console.warn('Form inputs without labels found:', inputsWithoutLabels)
  }
}

// Check heading hierarchy
export function checkHeadingHierarchy(): void {
  if (typeof window === 'undefined') return
  
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
  const headingLevels: number[] = []
  
  headings.forEach(heading => {
    const level = parseInt(heading.tagName.charAt(1))
    headingLevels.push(level)
  })
  
  let hasIssues = false
  for (let i = 1; i < headingLevels.length; i++) {
    const current = headingLevels[i]
    const previous = headingLevels[i - 1]
    
    if (current > previous + 1) {
      console.warn(`Heading hierarchy issue: h${previous} followed by h${current}`)
      hasIssues = true
    }
  }
  
  if (!hasIssues) {
    console.log('Heading hierarchy is correct')
  }
}

// Check color contrast (simplified)
export function checkColorContrast(): void {
  if (typeof window === 'undefined') return
  
  console.log('Color contrast checking requires manual verification or specialized tools')
  console.log('Ensure all text meets WCAG AA standards (4.5:1 for normal text, 3:1 for large text)')
}

// Check keyboard navigation
export function checkKeyboardNavigation(): void {
  if (typeof window === 'undefined') return
  
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )
  
  console.log(`Found ${focusableElements.length} focusable elements`)
  
  // Check for elements with tabindex > 0
  const positiveTabIndex = document.querySelectorAll('[tabindex]:not([tabindex="0"]):not([tabindex="-1"])')
  if (positiveTabIndex.length > 0) {
    console.warn('Elements with positive tabindex found (avoid using):', positiveTabIndex)
  }
}

// Run all accessibility checks
export function runAccessibilityChecks(): void {
  console.group('🔍 Accessibility Checks')
  checkImageAltText()
  checkFormLabels()
  checkHeadingHierarchy()
  checkColorContrast()
  checkKeyboardNavigation()
  console.groupEnd()
}

// Mobile responsiveness checks
export function checkMobileResponsiveness(): void {
  if (typeof window === 'undefined') return
  
  console.group('📱 Mobile Responsiveness Checks')
  
  // Check viewport meta tag
  const viewport = document.querySelector('meta[name="viewport"]')
  if (!viewport) {
    console.warn('Viewport meta tag missing')
  } else {
    console.log('Viewport meta tag found:', viewport.getAttribute('content'))
  }
  
  // Check for horizontal scrolling
  if (document.body.scrollWidth > window.innerWidth) {
    console.warn('Horizontal scrolling detected - check for elements wider than viewport')
  }
  
  // Check touch target sizes
  const buttons = document.querySelectorAll('button, [role="button"], a')
  const smallTargets: Element[] = []
  
  buttons.forEach(button => {
    const rect = button.getBoundingClientRect()
    if (rect.width < 44 || rect.height < 44) {
      smallTargets.push(button)
    }
  })
  
  if (smallTargets.length > 0) {
    console.warn('Touch targets smaller than 44px found:', smallTargets)
  }
  
  console.groupEnd()
}

// Development helper to run checks on page load
export function initAccessibilityChecks(): void {
  if (process.env.NODE_ENV === 'development') {
    // Run checks after page load
    if (typeof window !== 'undefined') {
      window.addEventListener('load', () => {
        setTimeout(() => {
          runAccessibilityChecks()
          checkMobileResponsiveness()
          
          // Import and run comprehensive audit
          import('./accessibility-audit').then(({ logAccessibilityReport }) => {
            logAccessibilityReport()
          })
        }, 1000)
      })
    }
  }
}