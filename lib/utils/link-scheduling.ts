import type { Link } from '@prisma/client'

/**
 * Check if a link should be visible based on its scheduling configuration
 */
export function isLinkScheduleActive(link: Link, currentTime: Date = new Date()): boolean {
  // If not scheduled, use the regular visibility setting
  if (!link.isScheduled) {
    return link.isVisible
  }

  // If scheduled but no start time, treat as not active
  if (!link.scheduleStart) {
    return false
  }

  // Check if current time is after start time
  const startTime = new Date(link.scheduleStart)
  if (currentTime < startTime) {
    return false
  }

  // Check if current time is before end time (if set)
  if (link.scheduleEnd) {
    const endTime = new Date(link.scheduleEnd)
    if (currentTime > endTime) {
      return false
    }
  }

  // Link is within schedule and visible
  return link.isVisible
}

/**
 * Filter links to only show those that are currently active based on scheduling
 */
export function filterActiveLinks(links: Link[], currentTime: Date = new Date()): Link[] {
  return links.filter(link => isLinkScheduleActive(link, currentTime))
}

/**
 * Get the next scheduled event for a link (start or end)
 */
export function getNextScheduleEvent(link: Link, currentTime: Date = new Date()): {
  type: 'start' | 'end' | null
  date: Date | null
  isActive: boolean
} {
  if (!link.isScheduled) {
    return { type: null, date: null, isActive: link.isVisible }
  }

  const isActive = isLinkScheduleActive(link, currentTime)
  
  // If not started yet, next event is start
  if (link.scheduleStart && currentTime < new Date(link.scheduleStart)) {
    return {
      type: 'start',
      date: new Date(link.scheduleStart),
      isActive: false
    }
  }

  // If has end time and not ended yet, next event is end
  if (link.scheduleEnd && currentTime < new Date(link.scheduleEnd)) {
    return {
      type: 'end',
      date: new Date(link.scheduleEnd),
      isActive
    }
  }

  // No upcoming events
  return { type: null, date: null, isActive }
}

/**
 * Format a schedule status for display
 */
export function formatScheduleStatus(link: Link, currentTime: Date = new Date()): string {
  if (!link.isScheduled) {
    return link.isVisible ? 'Always visible' : 'Hidden'
  }

  const nextEvent = getNextScheduleEvent(link, currentTime)
  
  if (nextEvent.type === 'start') {
    return `Starts ${nextEvent.date?.toLocaleString()}`
  }
  
  if (nextEvent.type === 'end') {
    return `Ends ${nextEvent.date?.toLocaleString()}`
  }
  
  if (nextEvent.isActive) {
    return 'Active (no end time)'
  }
  
  return 'Schedule ended'
}