import {
  generateVisitorInsights,
  aggregateVisitorAnalytics,
  calculateEngagementScore,
  generateVisitorFingerprint,
  detectVisitorPreferences,
  VisitorInsights,
  VisitorAnalytics
} from '../visitor-analytics'
import { VisitorContext } from '../visitor-context'

describe('Visitor Analytics', () => {
  const mockVisitorContext: VisitorContext = {
    device: {
      browser: { name: 'Chrome', version: '91.0' },
      os: { name: 'Windows', version: '10' },
      device: { type: 'desktop', vendor: 'Unknown', model: 'Unknown' },
      isMobile: false,
      isTablet: false,
      isDesktop: true
    },
    location: {
      country: 'US',
      region: 'CA',
      city: 'San Francisco',
      latitude: '37.7749',
      longitude: '-122.4194',
      timezone: 'America/Los_Angeles'
    },
    referrer: {
      domain: 'google.com',
      source: 'google',
      medium: 'search',
      campaign: null,
      isInternal: false,
      isSocial: false,
      isSearch: true
    },
    timezone: 'America/Los_Angeles',
    ipHash: 'abc123def456',
    timestamp: new Date('2023-01-01T12:00:00Z')
  }

  describe('generateVisitorInsights', () => {
    it('should generate correct insights for desktop visitor', () => {
      const insights = generateVisitorInsights(mockVisitorContext)

      expect(insights.deviceCategory).toBe('desktop')
      expect(insights.browserFamily).toBe('Chrome')
      expect(insights.osFamily).toBe('Windows')
      expect(insights.locationRegion).toBe('US-CA')
      expect(insights.trafficSource).toBe('search')
      expect(insights.isBot).toBe(false)
      expect(insights.isReturningVisitor).toBe(false)
    })

    it('should detect mobile device correctly', () => {
      const mobileContext = {
        ...mockVisitorContext,
        device: {
          ...mockVisitorContext.device,
          device: { type: 'mobile', vendor: 'Apple', model: 'iPhone' },
          isMobile: true,
          isDesktop: false
        }
      }

      const insights = generateVisitorInsights(mobileContext)

      expect(insights.deviceCategory).toBe('mobile')
    })

    it('should detect returning visitor', () => {
      const insights = generateVisitorInsights(mockVisitorContext, 3)

      expect(insights.isReturningVisitor).toBe(true)
    })

    it('should calculate session duration', () => {
      const sessionStart = new Date('2023-01-01T11:55:00Z')
      // Mock Date.now to return the context timestamp
      const originalNow = Date.now
      Date.now = jest.fn(() => mockVisitorContext.timestamp.getTime())
      
      const insights = generateVisitorInsights(mockVisitorContext, 0, sessionStart)

      expect(insights.sessionDuration).toBe(5 * 60 * 1000) // 5 minutes in ms
      
      // Restore Date.now
      Date.now = originalNow
    })

    it('should detect social traffic source', () => {
      const socialContext = {
        ...mockVisitorContext,
        referrer: {
          domain: 'facebook.com',
          source: 'facebook',
          medium: 'social',
          campaign: null,
          isInternal: false,
          isSocial: true,
          isSearch: false
        }
      }

      const insights = generateVisitorInsights(socialContext)

      expect(insights.trafficSource).toBe('social')
    })

    it('should detect direct traffic when no referrer', () => {
      const directContext = {
        ...mockVisitorContext,
        referrer: null
      }

      const insights = generateVisitorInsights(directContext)

      expect(insights.trafficSource).toBe('direct')
    })
  })

  describe('aggregateVisitorAnalytics', () => {
    const contexts: VisitorContext[] = [
      mockVisitorContext,
      {
        ...mockVisitorContext,
        device: {
          ...mockVisitorContext.device,
          device: { type: 'mobile', vendor: 'Apple', model: 'iPhone' },
          isMobile: true,
          isDesktop: false
        },
        ipHash: 'def456ghi789'
      },
      {
        ...mockVisitorContext,
        referrer: {
          domain: 'facebook.com',
          source: 'facebook',
          medium: 'social',
          campaign: null,
          isInternal: false,
          isSocial: true,
          isSearch: false
        },
        ipHash: 'ghi789jkl012'
      }
    ]

    const insights: VisitorInsights[] = contexts.map(context => 
      generateVisitorInsights(context)
    )

    it('should aggregate device breakdown correctly', () => {
      const analytics = aggregateVisitorAnalytics(contexts, insights)

      expect(analytics.totalVisitors).toBe(3)
      expect(analytics.uniqueVisitors).toBe(3)
      expect(analytics.deviceBreakdown.desktop).toBe(2)
      expect(analytics.deviceBreakdown.mobile).toBe(1)
      expect(analytics.deviceBreakdown.tablet).toBe(0)
    })

    it('should aggregate browser breakdown correctly', () => {
      const analytics = aggregateVisitorAnalytics(contexts, insights)

      expect(analytics.browserBreakdown.Chrome).toBe(3)
    })

    it('should aggregate traffic sources correctly', () => {
      const analytics = aggregateVisitorAnalytics(contexts, insights)

      expect(analytics.trafficSources.search).toBe(2)
      expect(analytics.trafficSources.social).toBe(1)
      expect(analytics.trafficSources.direct).toBe(0)
    })

    it('should calculate top referrers with percentages', () => {
      const analytics = aggregateVisitorAnalytics(contexts, insights)

      expect(analytics.topReferrers).toHaveLength(2)
      expect(analytics.topReferrers[0].domain).toBe('google.com')
      expect(analytics.topReferrers[0].count).toBe(2)
      expect(analytics.topReferrers[0].percentage).toBe(66.7)
    })
  })

  describe('calculateEngagementScore', () => {
    const baseInsights: VisitorInsights = {
      deviceCategory: 'desktop',
      browserFamily: 'Chrome',
      osFamily: 'Windows',
      locationRegion: 'US-CA',
      trafficSource: 'direct',
      isBot: false,
      isReturningVisitor: false,
      sessionDuration: 60000 // 1 minute
    }

    it('should calculate base engagement score', () => {
      const score = calculateEngagementScore(mockVisitorContext, baseInsights)

      expect(score).toBeGreaterThan(0)
      expect(score).toBeLessThanOrEqual(100)
    })

    it('should give bonus for returning visitors', () => {
      const returningInsights = { ...baseInsights, isReturningVisitor: true }
      const newScore = calculateEngagementScore(mockVisitorContext, returningInsights)
      const baseScore = calculateEngagementScore(mockVisitorContext, baseInsights)

      expect(newScore).toBeGreaterThan(baseScore)
    })

    it('should give bonus for direct traffic', () => {
      const directInsights = { ...baseInsights, trafficSource: 'direct' as const }
      const searchInsights = { ...baseInsights, trafficSource: 'search' as const }
      
      const directScore = calculateEngagementScore(mockVisitorContext, directInsights)
      const searchScore = calculateEngagementScore(mockVisitorContext, searchInsights)

      expect(directScore).toBeGreaterThan(searchScore)
    })

    it('should penalize bot traffic heavily', () => {
      const botInsights = { ...baseInsights, isBot: true }
      const humanScore = calculateEngagementScore(mockVisitorContext, baseInsights)
      const botScore = calculateEngagementScore(mockVisitorContext, botInsights)

      expect(botScore).toBeLessThan(humanScore * 0.2)
    })

    it('should give bonus for multiple page views', () => {
      const singlePageScore = calculateEngagementScore(mockVisitorContext, baseInsights, 1)
      const multiPageScore = calculateEngagementScore(mockVisitorContext, baseInsights, 5)

      expect(multiPageScore).toBeGreaterThan(singlePageScore)
    })

    it('should give bonus for longer time on site', () => {
      const shortTimeScore = calculateEngagementScore(mockVisitorContext, baseInsights, 1, 30000) // 30 seconds
      const longTimeScore = calculateEngagementScore(mockVisitorContext, baseInsights, 1, 300000) // 5 minutes

      expect(longTimeScore).toBeGreaterThan(shortTimeScore)
    })
  })

  describe('generateVisitorFingerprint', () => {
    it('should generate consistent fingerprint for same context', () => {
      const fingerprint1 = generateVisitorFingerprint(mockVisitorContext)
      const fingerprint2 = generateVisitorFingerprint(mockVisitorContext)

      expect(fingerprint1).toBe(fingerprint2)
      expect(fingerprint1).toHaveLength(16)
    })

    it('should generate different fingerprints for different contexts', () => {
      const context2 = {
        ...mockVisitorContext,
        device: {
          ...mockVisitorContext.device,
          browser: { name: 'Firefox', version: '89.0' }
        }
      }

      const fingerprint1 = generateVisitorFingerprint(mockVisitorContext)
      const fingerprint2 = generateVisitorFingerprint(context2)

      expect(fingerprint1).not.toBe(fingerprint2)
    })

    it('should handle missing location gracefully', () => {
      const contextWithoutLocation = {
        ...mockVisitorContext,
        location: null
      }

      const fingerprint = generateVisitorFingerprint(contextWithoutLocation)

      expect(fingerprint).toBeDefined()
      expect(fingerprint).toHaveLength(16)
    })
  })

  describe('detectVisitorPreferences', () => {
    it('should detect language from US location', () => {
      const preferences = detectVisitorPreferences(mockVisitorContext)

      expect(preferences.preferredLanguage).toBe('en')
      expect(preferences.timeZone).toBe('America/Los_Angeles')
    })

    it('should detect language from German location', () => {
      const germanContext = {
        ...mockVisitorContext,
        location: {
          ...mockVisitorContext.location!,
          country: 'DE'
        }
      }

      const preferences = detectVisitorPreferences(germanContext)

      expect(preferences.preferredLanguage).toBe('de')
    })

    it('should handle unknown country', () => {
      const unknownContext = {
        ...mockVisitorContext,
        location: {
          ...mockVisitorContext.location!,
          country: 'XX'
        }
      }

      const preferences = detectVisitorPreferences(unknownContext)

      expect(preferences.preferredLanguage).toBeNull()
    })

    it('should handle missing location', () => {
      const noLocationContext = {
        ...mockVisitorContext,
        location: null
      }

      const preferences = detectVisitorPreferences(noLocationContext)

      expect(preferences.preferredLanguage).toBeNull()
      expect(preferences.timeZone).toBe('America/Los_Angeles')
    })
  })

  describe('Edge cases and security', () => {
    it('should handle malformed visitor context gracefully', () => {
      const malformedContext = {
        ...mockVisitorContext,
        device: {
          ...mockVisitorContext.device,
          browser: { name: '', version: '' }
        }
      } as VisitorContext

      expect(() => generateVisitorInsights(malformedContext)).not.toThrow()
      expect(() => generateVisitorFingerprint(malformedContext)).not.toThrow()
    })

    it('should not expose sensitive data in fingerprint', () => {
      const fingerprint = generateVisitorFingerprint(mockVisitorContext)

      expect(fingerprint).not.toContain(mockVisitorContext.ipHash!)
      expect(fingerprint).not.toContain('San Francisco')
      expect(fingerprint).not.toContain('37.7749')
    })

    it('should handle empty contexts array in aggregation', () => {
      const analytics = aggregateVisitorAnalytics([], [])

      expect(analytics.totalVisitors).toBe(0)
      expect(analytics.uniqueVisitors).toBe(0)
      expect(analytics.topReferrers).toHaveLength(0)
    })

    it('should cap engagement score at 100', () => {
      const highEngagementInsights = {
        ...generateVisitorInsights(mockVisitorContext),
        isReturningVisitor: true,
        trafficSource: 'direct' as const
      }

      const score = calculateEngagementScore(
        mockVisitorContext,
        highEngagementInsights,
        20, // many page views
        3600000, // 1 hour
        50 // many interactions
      )

      expect(score).toBeLessThanOrEqual(100)
    })
  })
})