import { MonitoringDashboard, __testExports } from '../monitoring-dashboard'
import { performanceMonitor } from '../performance-monitor'

describe('MonitoringDashboard', () => {
  let dashboard: MonitoringDashboard

  beforeEach(() => {
    dashboard = new __testExports.MonitoringDashboard()
    performanceMonitor.clearMetrics()
  })

  afterEach(() => {
    performanceMonitor.clearMetrics()
  })

  describe('getDashboardData', () => {
    it('should return comprehensive dashboard data', () => {
      // Add some test metrics
      performanceMonitor.recordPerformance('test-op', 500, true)
      performanceMonitor.recordCache('hit', 'key1')
      performanceMonitor.recordError('test-op', 'Test error', 'TEST_ERROR')

      const data = dashboard.getDashboardData()

      expect(data).toHaveProperty('summary')
      expect(data).toHaveProperty('performance')
      expect(data).toHaveProperty('cache')
      expect(data).toHaveProperty('errors')
      expect(data).toHaveProperty('trends')

      expect(data.summary.status).toMatch(/healthy|warning|critical/)
      expect(data.summary.uptime).toBeDefined()
      expect(data.summary.lastUpdated).toBeDefined()
    })

    it('should calculate system status correctly', () => {
      // Test healthy status
      performanceMonitor.recordPerformance('op1', 100, true)
      performanceMonitor.recordPerformance('op2', 200, true)
      
      let data = dashboard.getDashboardData()
      expect(data.summary.status).toBe('healthy')

      // Clear and test warning status (adjust to get exactly warning threshold)
      performanceMonitor.clearMetrics()
      // Add 9 successful requests and 1 failed to get exactly 10% error rate (warning threshold)
      for (let i = 0; i < 9; i++) {
        performanceMonitor.recordPerformance(`op${i}`, 100, true)
      }
      performanceMonitor.recordPerformance('op9', 200, false, 'error')
      performanceMonitor.recordError('op9', 'Test error', 'ERROR')
      
      data = dashboard.getDashboardData()
      expect(data.summary.status).toBe('warning')

      // Clear and test critical status
      performanceMonitor.clearMetrics()
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordPerformance(`op${i}`, 100, false, 'error')
        performanceMonitor.recordError(`op${i}`, 'Test error', 'ERROR')
      }
      
      data = dashboard.getDashboardData()
      expect(data.summary.status).toBe('critical')
    })
  })

  describe('performance data', () => {
    it('should calculate percentiles correctly', () => {
      // Add metrics with known response times
      const responseTimes = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
      responseTimes.forEach((time, index) => {
        performanceMonitor.recordPerformance(`op${index}`, time, true)
      })

      const data = dashboard.getDashboardData()
      
      expect(data.performance.p95ResponseTime).toBe(1000) // 95th percentile (9th index = 1000)
      expect(data.performance.p99ResponseTime).toBe(1000) // 99th percentile (9th index = 1000)
    })

    it('should track slow queries and timeouts', () => {
      performanceMonitor.recordPerformance('slow-op', 2000, true)
      performanceMonitor.recordPerformance('timeout-op', 5000, false, 'timeout')

      const data = dashboard.getDashboardData()
      
      expect(data.performance.slowQueries).toBe(2) // Both operations are slow
      expect(data.performance.timeouts).toBe(1)
    })
  })

  describe('cache data', () => {
    it('should calculate cache efficiency correctly', () => {
      // Test excellent efficiency (>= 80% hit rate)
      for (let i = 0; i < 8; i++) {
        performanceMonitor.recordCache('hit', `key${i}`)
      }
      for (let i = 0; i < 2; i++) {
        performanceMonitor.recordCache('miss', `key${i + 8}`)
      }

      let data = dashboard.getDashboardData()
      expect(data.cache.efficiency).toBe('excellent')

      // Clear and test good efficiency (>= 60% hit rate)
      performanceMonitor.clearMetrics()
      for (let i = 0; i < 6; i++) {
        performanceMonitor.recordCache('hit', `key${i}`)
      }
      for (let i = 0; i < 4; i++) {
        performanceMonitor.recordCache('miss', `key${i + 6}`)
      }

      data = dashboard.getDashboardData()
      expect(data.cache.efficiency).toBe('good')

      // Clear and test poor efficiency (< 60% hit rate)
      performanceMonitor.clearMetrics()
      for (let i = 0; i < 4; i++) {
        performanceMonitor.recordCache('hit', `key${i}`)
      }
      for (let i = 0; i < 6; i++) {
        performanceMonitor.recordCache('miss', `key${i + 4}`)
      }

      data = dashboard.getDashboardData()
      expect(data.cache.efficiency).toBe('poor')
    })
  })

  describe('error data', () => {
    it('should calculate top errors with percentages', () => {
      // Add various error types
      performanceMonitor.recordError('op1', 'Error A', 'TYPE_A')
      performanceMonitor.recordError('op2', 'Error A', 'TYPE_A')
      performanceMonitor.recordError('op3', 'Error A', 'TYPE_A')
      performanceMonitor.recordError('op4', 'Error B', 'TYPE_B')
      performanceMonitor.recordError('op5', 'Error B', 'TYPE_B')
      performanceMonitor.recordError('op6', 'Error C', 'TYPE_C')

      const data = dashboard.getDashboardData()
      
      expect(data.errors.topErrors).toHaveLength(3)
      expect(data.errors.topErrors[0]).toEqual({
        type: 'TYPE_A',
        count: 3,
        percentage: 50 // 3 out of 6 errors
      })
      expect(data.errors.topErrors[1]).toEqual({
        type: 'TYPE_B',
        count: 2,
        percentage: 33 // 2 out of 6 errors
      })
    })

    it('should limit recent errors to 10', () => {
      // Add more than 10 errors
      for (let i = 0; i < 15; i++) {
        performanceMonitor.recordError(`op${i}`, `Error ${i}`, 'ERROR')
      }

      const data = dashboard.getDashboardData()
      expect(data.errors.recentErrors).toHaveLength(10)
      
      // Should show most recent errors first
      expect(data.errors.recentErrors[0].error).toBe('Error 14')
    })
  })

  describe('trends data', () => {
    it('should generate trend data with correct intervals', () => {
      // Add some metrics spread over time
      const now = Date.now()
      const oneHourAgo = now - (60 * 60 * 1000)
      
      // Mock timestamps for different intervals
      const originalNow = Date.now
      
      for (let i = 0; i < 12; i++) {
        const timestamp = oneHourAgo + (i * 5 * 60 * 1000) // 5-minute intervals
        Date.now = jest.fn(() => timestamp)
        
        performanceMonitor.recordPerformance(`op${i}`, 100 + (i * 10), true)
        performanceMonitor.recordCache(i % 2 === 0 ? 'hit' : 'miss', `key${i}`)
      }
      
      Date.now = originalNow

      const data = dashboard.getDashboardData()
      
      expect(data.trends.responseTimeHistory).toHaveLength(12)
      expect(data.trends.errorRateHistory).toHaveLength(12)
      expect(data.trends.cacheHitRateHistory).toHaveLength(12)
    })
  })

  describe('generateReport', () => {
    it('should generate a formatted text report', () => {
      performanceMonitor.recordPerformance('test-op', 500, true)
      performanceMonitor.recordCache('hit', 'key1')
      performanceMonitor.recordError('test-op', 'Test error', 'TEST_ERROR')

      const report = dashboard.generateReport()
      
      expect(report).toContain('Username Availability Monitoring Report')
      expect(report).toContain('System Status:')
      expect(report).toContain('Performance Metrics')
      expect(report).toContain('Cache Performance')
      expect(report).toContain('Error Analysis')
    })
  })

  describe('getHealthCheck', () => {
    it('should return health check with individual checks', () => {
      // Add metrics that should pass all health checks
      performanceMonitor.recordPerformance('op1', 500, true) // Good response time
      performanceMonitor.recordPerformance('op2', 600, true) // Good success rate
      
      for (let i = 0; i < 8; i++) {
        performanceMonitor.recordCache('hit', `key${i}`) // Good cache hit rate
      }
      for (let i = 0; i < 2; i++) {
        performanceMonitor.recordCache('miss', `key${i + 8}`)
      }

      const healthCheck = dashboard.getHealthCheck()
      
      expect(healthCheck.status).toBe('healthy')
      expect(healthCheck.checks.responseTime).toBe(true)
      expect(healthCheck.checks.successRate).toBe(true)
      expect(healthCheck.checks.errorRate).toBe(true)
      expect(healthCheck.checks.cacheEfficiency).toBe(true)
    })

    it('should return unhealthy status when checks fail', () => {
      // Add metrics that should fail health checks
      performanceMonitor.recordPerformance('slow-op', 3000, false, 'error') // Slow and failed
      performanceMonitor.recordError('slow-op', 'Test error', 'ERROR')
      
      const healthCheck = dashboard.getHealthCheck()
      
      expect(healthCheck.status).toBe('unhealthy')
      expect(healthCheck.checks.responseTime).toBe(false)
      expect(healthCheck.checks.successRate).toBe(false)
    })
  })
})