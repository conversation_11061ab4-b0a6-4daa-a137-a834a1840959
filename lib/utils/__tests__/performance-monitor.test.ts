import { PerformanceMonitor, __testExports } from '../performance-monitor'

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor

  beforeEach(() => {
    monitor = new __testExports.PerformanceMonitor()
  })

  afterEach(() => {
    monitor.clearMetrics()
  })

  describe('recordPerformance', () => {
    it('should record performance metrics correctly', () => {
      monitor.recordPerformance('test-operation', 500, true, undefined, { test: 'data' })
      
      const stats = monitor.getStats()
      expect(stats.performance.totalRequests).toBe(1)
      expect(stats.performance.averageResponseTime).toBe(500)
      expect(stats.performance.successRate).toBe(100)
    })

    it('should track failed operations', () => {
      monitor.recordPerformance('test-operation', 1000, false, 'Test error')
      
      const stats = monitor.getStats()
      expect(stats.performance.totalRequests).toBe(1)
      expect(stats.performance.successRate).toBe(0)
    })

    it('should identify slow queries', () => {
      // Mock console.warn to capture slow query warnings
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
      
      monitor.recordPerformance('slow-operation', 2000, true)
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Slow operation detected'),
        expect.any(Object)
      )
      
      const stats = monitor.getStats()
      expect(stats.performance.slowQueries).toBe(1)
      
      consoleSpy.mockRestore()
    })

    it('should calculate average response time correctly', () => {
      monitor.recordPerformance('op1', 100, true)
      monitor.recordPerformance('op2', 200, true)
      monitor.recordPerformance('op3', 300, true)
      
      const stats = monitor.getStats()
      expect(stats.performance.averageResponseTime).toBe(200)
    })
  })

  describe('recordCache', () => {
    it('should record cache hits and misses', () => {
      monitor.recordCache('hit', 'test-key')
      monitor.recordCache('miss', 'test-key2')
      monitor.recordCache('hit', 'test-key3')
      
      const stats = monitor.getStats()
      expect(stats.cache.totalHits).toBe(2)
      expect(stats.cache.totalMisses).toBe(1)
      expect(stats.cache.hitRate).toBe(66.67)
      expect(stats.cache.missRate).toBe(33.33)
    })

    it('should handle cache operations with metadata', () => {
      monitor.recordCache('set', 'test-key', { available: true })
      monitor.recordCache('clear', 'test-key', { reason: 'expired' })
      
      const rawMetrics = monitor.getRawMetrics()
      expect(rawMetrics.cache).toHaveLength(2)
      expect(rawMetrics.cache[0].metadata).toEqual({ available: true })
      expect(rawMetrics.cache[1].metadata).toEqual({ reason: 'expired' })
    })
  })

  describe('recordError', () => {
    it('should record error metrics', () => {
      monitor.recordError('test-operation', 'Test error', 'TEST_ERROR', { userId: '123' })
      
      const stats = monitor.getStats()
      expect(stats.errors.totalErrors).toBe(1)
      expect(stats.errors.errorsByType.TEST_ERROR).toBe(1)
    })

    it('should group errors by type', () => {
      monitor.recordError('op1', 'Error 1', 'TYPE_A')
      monitor.recordError('op2', 'Error 2', 'TYPE_A')
      monitor.recordError('op3', 'Error 3', 'TYPE_B')
      
      const stats = monitor.getStats()
      expect(stats.errors.totalErrors).toBe(3)
      expect(stats.errors.errorsByType.TYPE_A).toBe(2)
      expect(stats.errors.errorsByType.TYPE_B).toBe(1)
    })

    it('should calculate error rate correctly', () => {
      // Record some successful operations
      monitor.recordPerformance('op1', 100, true)
      monitor.recordPerformance('op2', 200, true)
      
      // Record some errors
      monitor.recordError('op3', 'Error 1', 'ERROR')
      
      const stats = monitor.getStats()
      expect(stats.errors.errorRate).toBe(50) // 1 error out of 2 total operations
    })
  })

  describe('getStats', () => {
    it('should return comprehensive statistics', () => {
      // Add some test data
      monitor.recordPerformance('op1', 100, true)
      monitor.recordPerformance('op2', 2000, false, 'timeout')
      monitor.recordCache('hit', 'key1')
      monitor.recordCache('miss', 'key2')
      monitor.recordError('op3', 'Test error', 'TEST_ERROR')
      
      const stats = monitor.getStats()
      
      expect(stats).toHaveProperty('performance')
      expect(stats).toHaveProperty('cache')
      expect(stats).toHaveProperty('errors')
      
      expect(stats.performance.totalRequests).toBe(2)
      expect(stats.performance.successRate).toBe(50)
      expect(stats.performance.timeouts).toBe(1)
      
      expect(stats.cache.totalOperations).toBe(2)
      expect(stats.cache.hitRate).toBe(50)
      
      expect(stats.errors.totalErrors).toBe(1)
      expect(stats.errors.errorsByType.TEST_ERROR).toBe(1)
    })

    it('should filter metrics to last hour only', () => {
      // Mock Date.now to simulate old metrics
      const originalNow = Date.now
      const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000)
      
      Date.now = jest.fn(() => twoHoursAgo)
      monitor.recordPerformance('old-op', 100, true)
      
      Date.now = originalNow
      monitor.recordPerformance('new-op', 200, true)
      
      const stats = monitor.getStats()
      expect(stats.performance.totalRequests).toBe(1) // Only recent metric
    })
  })

  describe('clearMetrics', () => {
    it('should clear all metrics', () => {
      monitor.recordPerformance('op1', 100, true)
      monitor.recordCache('hit', 'key1')
      monitor.recordError('op2', 'Error', 'ERROR')
      
      monitor.clearMetrics()
      
      const stats = monitor.getStats()
      expect(stats.performance.totalRequests).toBe(0)
      expect(stats.cache.totalOperations).toBe(0)
      expect(stats.errors.totalErrors).toBe(0)
    })
  })

  describe('memory management', () => {
    it('should limit metrics to prevent memory leaks', () => {
      // Add more than maxMetrics (1000) entries
      for (let i = 0; i < 1100; i++) {
        monitor.recordPerformance(`op-${i}`, 100, true)
      }
      
      const rawMetrics = monitor.getRawMetrics()
      expect(rawMetrics.performance.length).toBeLessThanOrEqual(1000)
    })

    it('should remove expired metrics', () => {
      // Mock Date.now to simulate old metrics
      const originalNow = Date.now
      const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000)
      
      Date.now = jest.fn(() => twoHoursAgo)
      monitor.recordPerformance('old-op', 100, true)
      
      Date.now = originalNow
      monitor.recordPerformance('new-op', 200, true)
      
      // Trigger cleanup by adding another metric
      monitor.recordPerformance('trigger-cleanup', 300, true)
      
      const rawMetrics = monitor.getRawMetrics()
      expect(rawMetrics.performance.length).toBe(2) // Old metric should be removed
    })
  })
})