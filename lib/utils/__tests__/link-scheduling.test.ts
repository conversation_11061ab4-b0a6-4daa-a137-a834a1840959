import { isLinkScheduleActive, filterActiveLinks, formatScheduleStatus } from '../link-scheduling'
import type { Link } from '@prisma/client'

// Mock link data
const createMockLink = (overrides: Partial<Link> = {}): Link => ({
  id: 'test-id',
  profileId: 'profile-id',
  title: 'Test Link',
  url: 'https://example.com',
  icon: null,
  isVisible: true,
  order: 0,
  clickCount: 0,
  isScheduled: false,
  scheduleStart: null,
  scheduleEnd: null,
  timezone: 'UTC',
  hasConditions: false,
  defaultBehavior: 'show',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
})

describe('Link Scheduling Utils', () => {
  const now = new Date('2024-01-15T12:00:00Z')
  const pastDate = new Date('2024-01-10T12:00:00Z')
  const futureDate = new Date('2024-01-20T12:00:00Z')

  describe('isLinkScheduleActive', () => {
    it('should return isVisible for non-scheduled links', () => {
      const visibleLink = createMockLink({ isVisible: true, isScheduled: false })
      const hiddenLink = createMockLink({ isVisible: false, isScheduled: false })

      expect(isLinkScheduleActive(visibleLink, now)).toBe(true)
      expect(isLinkScheduleActive(hiddenLink, now)).toBe(false)
    })

    it('should return false for scheduled links without start time', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: null 
      })

      expect(isLinkScheduleActive(link, now)).toBe(false)
    })

    it('should return false for links not yet started', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: futureDate 
      })

      expect(isLinkScheduleActive(link, now)).toBe(false)
    })

    it('should return true for active scheduled links', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: pastDate 
      })

      expect(isLinkScheduleActive(link, now)).toBe(true)
    })

    it('should return false for expired scheduled links', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: pastDate,
        scheduleEnd: new Date('2024-01-12T12:00:00Z')
      })

      expect(isLinkScheduleActive(link, now)).toBe(false)
    })

    it('should respect isVisible even for active scheduled links', () => {
      const link = createMockLink({ 
        isVisible: false, 
        isScheduled: true, 
        scheduleStart: pastDate 
      })

      expect(isLinkScheduleActive(link, now)).toBe(false)
    })
  })

  describe('filterActiveLinks', () => {
    it('should filter out inactive links', () => {
      const links = [
        createMockLink({ id: '1', isVisible: true, isScheduled: false }),
        createMockLink({ id: '2', isVisible: false, isScheduled: false }),
        createMockLink({ id: '3', isVisible: true, isScheduled: true, scheduleStart: futureDate }),
        createMockLink({ id: '4', isVisible: true, isScheduled: true, scheduleStart: pastDate }),
      ]

      const activeLinks = filterActiveLinks(links, now)
      expect(activeLinks).toHaveLength(2)
      expect(activeLinks.map(l => l.id)).toEqual(['1', '4'])
    })
  })

  describe('formatScheduleStatus', () => {
    it('should format non-scheduled links', () => {
      const visibleLink = createMockLink({ isVisible: true, isScheduled: false })
      const hiddenLink = createMockLink({ isVisible: false, isScheduled: false })

      expect(formatScheduleStatus(visibleLink, now)).toBe('Always visible')
      expect(formatScheduleStatus(hiddenLink, now)).toBe('Hidden')
    })

    it('should format upcoming scheduled links', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: futureDate 
      })

      const status = formatScheduleStatus(link, now)
      expect(status).toContain('Starts')
    })

    it('should format active scheduled links with end time', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: pastDate,
        scheduleEnd: futureDate
      })

      const status = formatScheduleStatus(link, now)
      expect(status).toContain('Ends')
    })

    it('should format active scheduled links without end time', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: pastDate,
        scheduleEnd: null
      })

      expect(formatScheduleStatus(link, now)).toBe('Active (no end time)')
    })

    it('should format expired scheduled links', () => {
      const link = createMockLink({ 
        isVisible: true, 
        isScheduled: true, 
        scheduleStart: pastDate,
        scheduleEnd: new Date('2024-01-12T12:00:00Z')
      })

      expect(formatScheduleStatus(link, now)).toBe('Schedule ended')
    })
  })
})