import {
  parseUserAgent,
  parseLocationFromHeaders,
  parseReferrer,
  detectTimezone,
  hashIP,
  extractIPAddress,
  createVisitorContext,
  DeviceInfo,
  LocationInfo,
  ReferrerInfo,
  VisitorContext
} from '../visitor-context'

describe('Visitor Context Detection', () => {
  describe('parseUserAgent', () => {
    it('should parse Chrome desktop user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      
      const result = parseUserAgent(userAgent)
      
      expect(result.browser.name).toBe('Chrome')
      expect(result.os.name).toBe('Windows')
      expect(result.isDesktop).toBe(true)
      expect(result.isMobile).toBe(false)
      expect(result.isTablet).toBe(false)
    })

    it('should parse iPhone user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
      
      const result = parseUserAgent(userAgent)
      
      expect(result.browser.name).toBe('Mobile Safari')
      expect(result.os.name).toBe('iOS')
      expect(result.device.type).toBe('mobile')
      expect(result.isMobile).toBe(true)
      expect(result.isDesktop).toBe(false)
    })

    it('should parse iPad user agent correctly', () => {
      const userAgent = 'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
      
      const result = parseUserAgent(userAgent)
      
      expect(result.device.type).toBe('tablet')
      expect(result.isTablet).toBe(true)
      expect(result.isMobile).toBe(false)
      expect(result.isDesktop).toBe(false)
    })

    it('should handle invalid user agent gracefully', () => {
      const result = parseUserAgent('')
      
      expect(result.browser.name).toBe('Unknown')
      expect(result.os.name).toBe('Unknown')
      expect(result.device.type).toBe('desktop')
      expect(result.isDesktop).toBe(true)
    })

    it('should handle malformed user agent', () => {
      const result = parseUserAgent('invalid-user-agent-string')
      
      expect(result).toBeDefined()
      expect(result.browser).toBeDefined()
      expect(result.os).toBeDefined()
      expect(result.device).toBeDefined()
    })
  })

  describe('parseLocationFromHeaders', () => {
    it('should parse Vercel geo headers correctly', () => {
      const headers = new Headers({
        'x-vercel-ip-country': 'US',
        'x-vercel-ip-country-region': 'CA',
        'x-vercel-ip-city': 'San Francisco',
        'x-vercel-ip-latitude': '37.7749',
        'x-vercel-ip-longitude': '-122.4194',
        'x-vercel-ip-timezone': 'America/Los_Angeles'
      })
      
      const result = parseLocationFromHeaders(headers)
      
      expect(result).toEqual({
        country: 'US',
        region: 'CA',
        city: 'San Francisco',
        latitude: '37.7749',
        longitude: '-122.4194',
        timezone: 'America/Los_Angeles'
      })
    })

    it('should return null when no location headers present', () => {
      const headers = new Headers()
      
      const result = parseLocationFromHeaders(headers)
      
      expect(result).toBeNull()
    })

    it('should sanitize location values', () => {
      const headers = new Headers({
        'x-vercel-ip-country': 'US<script>',
        'x-vercel-ip-city': 'San Francisco"test'
      })
      
      const result = parseLocationFromHeaders(headers)
      
      expect(result?.country).toBe('USscript')
      expect(result?.city).toBe('San Franciscotest')
    })

    it('should handle invalid coordinates', () => {
      const headers = new Headers({
        'x-vercel-ip-country': 'US',
        'x-vercel-ip-latitude': 'invalid',
        'x-vercel-ip-longitude': '999'
      })
      
      const result = parseLocationFromHeaders(headers)
      
      expect(result?.latitude).toBe('')
      expect(result?.longitude).toBe('')
    })
  })

  describe('parseReferrer', () => {
    it('should parse Google referrer correctly', () => {
      const referrer = 'https://www.google.com/search?q=test'
      const currentDomain = 'example.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result).toEqual({
        domain: 'www.google.com',
        source: 'google',
        medium: 'search',
        campaign: null,
        isInternal: false,
        isSocial: false,
        isSearch: true
      })
    })

    it('should parse Facebook referrer correctly', () => {
      const referrer = 'https://www.facebook.com/page'
      const currentDomain = 'example.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result).toEqual({
        domain: 'www.facebook.com',
        source: 'facebook',
        medium: 'social',
        campaign: null,
        isInternal: false,
        isSocial: true,
        isSearch: false
      })
    })

    it('should parse UTM parameters correctly', () => {
      const referrer = 'https://example.com/page?utm_source=newsletter&utm_medium=email&utm_campaign=summer2023'
      const currentDomain = 'mysite.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result?.source).toBe('newsletter')
      expect(result?.medium).toBe('email')
      expect(result?.campaign).toBe('summer2023')
    })

    it('should detect internal referrers', () => {
      const referrer = 'https://example.com/page1'
      const currentDomain = 'example.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result?.isInternal).toBe(true)
    })

    it('should return null for empty referrer', () => {
      const result = parseReferrer('', 'example.com')
      
      expect(result).toBeNull()
    })

    it('should handle malformed URLs gracefully', () => {
      const referrer = 'not-a-valid-url'
      const currentDomain = 'example.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result).toBeNull()
    })

    it('should sanitize UTM values', () => {
      const referrer = 'https://example.com/?utm_source=test<script>&utm_campaign=safe"value'
      const currentDomain = 'mysite.com'
      
      const result = parseReferrer(referrer, currentDomain)
      
      expect(result?.source).toBe('testscript')
      expect(result?.campaign).toBe('safevalue')
    })
  })

  describe('detectTimezone', () => {
    it('should use location timezone when valid', () => {
      const result = detectTimezone('America/New_York', 'en-US')
      
      expect(result).toBe('America/New_York')
    })

    it('should fall back to language-based timezone', () => {
      const result = detectTimezone('', 'en-US,en;q=0.9')
      
      expect(result).toBe('America/New_York')
    })

    it('should fall back to UTC for invalid timezone', () => {
      const result = detectTimezone('Invalid/Timezone', 'invalid-lang')
      
      expect(result).toBe('UTC')
    })

    it('should handle multiple languages in Accept-Language', () => {
      const result = detectTimezone('', 'fr-FR,en-US;q=0.8,en;q=0.6')
      
      expect(result).toBe('Europe/Paris')
    })
  })

  describe('hashIP', () => {
    it('should create consistent hash for same IP', () => {
      const ip = '***********'
      const hash1 = hashIP(ip)
      const hash2 = hashIP(ip)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toHaveLength(64) // SHA-256 hex length
    })

    it('should create different hashes for different IPs', () => {
      const hash1 = hashIP('***********')
      const hash2 = hashIP('***********')
      
      expect(hash1).not.toBe(hash2)
    })

    it('should handle empty IP', () => {
      const result = hashIP('')
      
      expect(result).toBe('')
    })

    it('should use salt when provided', () => {
      const ip = '***********'
      const hash1 = hashIP(ip, 'salt1')
      const hash2 = hashIP(ip, 'salt2')
      
      expect(hash1).not.toBe(hash2)
    })
  })

  describe('extractIPAddress', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********, ********'
      })
      
      const result = extractIPAddress(headers)
      
      expect(result).toBe('***********')
    })

    it('should extract IP from x-real-ip header', () => {
      const headers = new Headers({
        'x-real-ip': '***********'
      })
      
      const result = extractIPAddress(headers)
      
      expect(result).toBe('***********')
    })

    it('should prioritize headers correctly', () => {
      const headers = new Headers({
        'x-real-ip': '********',
        'x-forwarded-for': '***********'
      })
      
      const result = extractIPAddress(headers)
      
      expect(result).toBe('***********') // x-forwarded-for has higher priority
    })

    it('should return null when no IP headers present', () => {
      const headers = new Headers()
      
      const result = extractIPAddress(headers)
      
      expect(result).toBeNull()
    })

    it('should validate IPv4 addresses', () => {
      const headers = new Headers({
        'x-forwarded-for': '999.999.999.999'
      })
      
      const result = extractIPAddress(headers)
      
      expect(result).toBeNull()
    })

    it('should handle IPv6 addresses', () => {
      const headers = new Headers({
        'x-forwarded-for': '2001:0db8:85a3:0000:0000:8a2e:0370:7334'
      })
      
      const result = extractIPAddress(headers)
      
      expect(result).toBe('2001:0db8:85a3:0000:0000:8a2e:0370:7334')
    })
  })

  describe('createVisitorContext', () => {
    it('should create complete visitor context', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      const headers = new Headers({
        'x-forwarded-for': '***********',
        'x-vercel-ip-country': 'US',
        'x-vercel-ip-city': 'San Francisco',
        'x-vercel-ip-timezone': 'America/Los_Angeles',
        'accept-language': 'en-US,en;q=0.9'
      })
      const referrer = 'https://www.google.com/search?q=test'
      const currentDomain = 'example.com'
      
      const result = createVisitorContext(userAgent, headers, referrer, currentDomain)
      
      expect(result.device.browser.name).toBe('Chrome')
      expect(result.device.isDesktop).toBe(true)
      expect(result.location?.country).toBe('US')
      expect(result.referrer?.source).toBe('google')
      expect(result.timezone).toBe('America/Los_Angeles')
      expect(result.ipHash).toBeTruthy()
      expect(result.timestamp).toBeInstanceOf(Date)
    })

    it('should handle missing optional data gracefully', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      const headers = new Headers()
      
      const result = createVisitorContext(userAgent, headers)
      
      expect(result.device).toBeDefined()
      expect(result.location).toBeNull()
      expect(result.referrer).toBeNull()
      expect(result.timezone).toBe('UTC')
      expect(result.ipHash).toBeNull()
    })

    it('should use default domain when not provided', () => {
      const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      const headers = new Headers()
      const referrer = 'https://localhost:3000/page'
      
      const result = createVisitorContext(userAgent, headers, referrer)
      
      expect(result.referrer?.isInternal).toBe(true)
    })
  })

  describe('Edge cases and security', () => {
    it('should handle extremely long user agent strings', () => {
      const longUserAgent = 'Mozilla/5.0 ' + 'a'.repeat(10000)
      
      const result = parseUserAgent(longUserAgent)
      
      expect(result).toBeDefined()
      expect(typeof result.browser.name).toBe('string')
    })

    it('should sanitize malicious location data', () => {
      const headers = new Headers({
        'x-vercel-ip-country': '<script>alert("xss")</script>',
        'x-vercel-ip-city': 'City"onmouseover="alert(1)"'
      })
      
      const result = parseLocationFromHeaders(headers)
      
      expect(result?.country).not.toContain('<script>')
      expect(result?.city).not.toContain('"')
    })

    it('should handle null and undefined inputs safely', () => {
      expect(() => parseUserAgent(null as any)).not.toThrow()
      expect(() => parseLocationFromHeaders(null as any)).not.toThrow()
      expect(() => parseReferrer(null as any, 'example.com')).not.toThrow()
    })

    it('should limit string lengths to prevent memory issues', () => {
      const longDomain = 'a'.repeat(1000) + '.com'
      const referrer = `https://${longDomain}/path`
      
      const result = parseReferrer(referrer, 'example.com')
      
      expect(result?.domain.length).toBeLessThanOrEqual(100)
    })
  })

  describe('Privacy compliance', () => {
    it('should hash IP addresses consistently', () => {
      const ip = '***********'
      const hash1 = hashIP(ip)
      const hash2 = hashIP(ip)
      
      expect(hash1).toBe(hash2)
      expect(hash1).not.toBe(ip) // Should not store raw IP
    })

    it('should not expose raw IP in visitor context', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********'
      })
      
      const result = createVisitorContext('Mozilla/5.0', headers)
      
      expect(result.ipHash).toBeTruthy()
      expect(result).not.toHaveProperty('ip')
      expect(JSON.stringify(result)).not.toContain('***********')
    })
  })
})