import {
  createPrivacyCompliantIPHash,
  anonymizeIPAddress,
  shouldAnonymizeData,
  shouldDeleteData,
  anonymizeVisitorContext,
  isValidIPAddress,
  isPrivateIPAddress,
  extractRealIP,
  filterContextByConsent,
  auditPrivacyCompliance,
  DEFAULT_PRIVACY_CONFIG,
  ConsentConfig,
  PrivacyConfig
} from '../privacy-utils'

describe('Privacy Utils', () => {
  describe('createPrivacyCompliantIPHash', () => {
    it('should create consistent hash for same IP', () => {
      const ip = '*************'
      const hash1 = createPrivacyCompliantIPHash(ip)
      const hash2 = createPrivacyCompliantIPHash(ip)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toHaveLength(64) // SHA-256 hex length
    })

    it('should create different hashes for different IPs', () => {
      const hash1 = createPrivacyCompliantIPHash('*************')
      const hash2 = createPrivacyCompliantIPHash('*************')
      
      expect(hash1).not.toBe(hash2)
    })

    it('should handle empty IP gracefully', () => {
      expect(createPrivacyCompliantIPHash('')).toBe('')
      expect(createPrivacyCompliantIPHash('unknown')).toBe('')
    })

    it('should handle invalid IP gracefully', () => {
      const result = createPrivacyCompliantIPHash('not-an-ip')
      expect(typeof result).toBe('string')
    })
  })

  describe('anonymizeIPAddress', () => {
    it('should anonymize IPv4 address correctly', () => {
      expect(anonymizeIPAddress('*************')).toBe('***********')
      expect(anonymizeIPAddress('********')).toBe('10.0.0.0')
      expect(anonymizeIPAddress('************')).toBe('************')
    })

    it('should anonymize IPv6 address correctly', () => {
      const ipv6 = '2001:0db8:85a3:0000:0000:8a2e:0370:7334'
      const anonymized = anonymizeIPAddress(ipv6)
      
      expect(anonymized).toBe('2001:0db8:85a3:0000::0')
    })

    it('should handle malformed IPs gracefully', () => {
      expect(anonymizeIPAddress('192.168.1')).toBe('192.168.1')
      expect(anonymizeIPAddress('not-an-ip')).toBe('not-an-ip')
      expect(anonymizeIPAddress('')).toBe('')
    })
  })

  describe('shouldAnonymizeData', () => {
    it('should return true for old data', () => {
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 10) // 10 days ago
      
      expect(shouldAnonymizeData(oldDate)).toBe(true)
    })

    it('should return false for recent data', () => {
      const recentDate = new Date()
      recentDate.setDate(recentDate.getDate() - 5) // 5 days ago
      
      expect(shouldAnonymizeData(recentDate)).toBe(false)
    })

    it('should respect custom config', () => {
      const customConfig: PrivacyConfig = {
        ...DEFAULT_PRIVACY_CONFIG,
        anonymizeAfterDays: 1
      }
      
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 2)
      
      expect(shouldAnonymizeData(yesterday, customConfig)).toBe(true)
    })
  })

  describe('shouldDeleteData', () => {
    it('should return true for very old data', () => {
      const veryOldDate = new Date()
      veryOldDate.setDate(veryOldDate.getDate() - 100) // 100 days ago
      
      expect(shouldDeleteData(veryOldDate)).toBe(true)
    })

    it('should return false for recent data', () => {
      const recentDate = new Date()
      recentDate.setDate(recentDate.getDate() - 30) // 30 days ago
      
      expect(shouldDeleteData(recentDate)).toBe(false)
    })
  })

  describe('anonymizeVisitorContext', () => {
    const mockContext = {
      ipHash: 'abcdef123456789',
      location: {
        country: 'US',
        region: 'CA',
        city: 'San Francisco',
        latitude: '37.7749',
        longitude: '-122.4194',
        timezone: 'America/Los_Angeles'
      },
      device: {
        browser: { name: 'Chrome', version: '91.0' }
      },
      timestamp: new Date()
    }

    it('should anonymize sensitive location data', () => {
      const anonymized = anonymizeVisitorContext(mockContext)
      
      expect(anonymized.location.country).toBe('US')
      expect(anonymized.location.timezone).toBe('America/Los_Angeles')
      expect(anonymized.location.region).toBeNull()
      expect(anonymized.location.city).toBeNull()
      expect(anonymized.location.latitude).toBe('')
      expect(anonymized.location.longitude).toBe('')
    })

    it('should partially anonymize IP hash', () => {
      const anonymized = anonymizeVisitorContext(mockContext)
      
      expect(anonymized.ipHash).toBe('abcdef12xxxx')
      expect(anonymized.ipHash).not.toBe(mockContext.ipHash)
    })

    it('should preserve non-sensitive data', () => {
      const anonymized = anonymizeVisitorContext(mockContext)
      
      expect(anonymized.device).toEqual(mockContext.device)
      expect(anonymized.timestamp).toEqual(mockContext.timestamp)
    })
  })

  describe('isValidIPAddress', () => {
    it('should validate IPv4 addresses correctly', () => {
      expect(isValidIPAddress('***********')).toBe(true)
      expect(isValidIPAddress('********')).toBe(true)
      expect(isValidIPAddress('***************')).toBe(true)
      expect(isValidIPAddress('0.0.0.0')).toBe(true)
    })

    it('should reject invalid IPv4 addresses', () => {
      expect(isValidIPAddress('256.1.1.1')).toBe(false)
      expect(isValidIPAddress('192.168.1')).toBe(false)
      expect(isValidIPAddress('***********.1')).toBe(false)
      expect(isValidIPAddress('************')).toBe(false) // Leading zeros
    })

    it('should validate IPv6 addresses correctly', () => {
      expect(isValidIPAddress('2001:0db8:85a3:0000:0000:8a2e:0370:7334')).toBe(true)
      expect(isValidIPAddress('::1')).toBe(true)
      expect(isValidIPAddress('2001:db8::1')).toBe(true)
    })

    it('should handle edge cases', () => {
      expect(isValidIPAddress('')).toBe(false)
      expect(isValidIPAddress(null as any)).toBe(false)
      expect(isValidIPAddress(undefined as any)).toBe(false)
      expect(isValidIPAddress('not-an-ip')).toBe(false)
    })
  })

  describe('isPrivateIPAddress', () => {
    it('should detect private IPv4 addresses', () => {
      expect(isPrivateIPAddress('***********')).toBe(true)
      expect(isPrivateIPAddress('********')).toBe(true)
      expect(isPrivateIPAddress('**********')).toBe(true)
      expect(isPrivateIPAddress('127.0.0.1')).toBe(true)
    })

    it('should detect public IPv4 addresses', () => {
      expect(isPrivateIPAddress('*******')).toBe(false)
      expect(isPrivateIPAddress('*******')).toBe(false)
      expect(isPrivateIPAddress('**************')).toBe(false)
    })

    it('should detect private IPv6 addresses', () => {
      expect(isPrivateIPAddress('::1')).toBe(true)
      expect(isPrivateIPAddress('fc00::1')).toBe(true)
      expect(isPrivateIPAddress('fe80::1')).toBe(true)
    })
  })

  describe('extractRealIP', () => {
    it('should extract IP from x-forwarded-for header', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********, ***********'
      })
      
      expect(extractRealIP(headers)).toBe('***********')
    })

    it('should extract IP from cf-connecting-ip header', () => {
      const headers = new Headers({
        'cf-connecting-ip': '***********'
      })
      
      expect(extractRealIP(headers)).toBe('***********')
    })

    it('should prioritize headers correctly', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********',
        'cf-connecting-ip': '***********'
      })
      
      expect(extractRealIP(headers)).toBe('***********') // Cloudflare has higher priority
    })

    it('should return null when no valid IP found', () => {
      const headers = new Headers()
      
      expect(extractRealIP(headers)).toBeNull()
    })

    it('should skip private IPs in forwarded chain', () => {
      const headers = new Headers({
        'x-forwarded-for': '***********, ***********, ********'
      })
      
      expect(extractRealIP(headers)).toBe('***********')
    })
  })

  describe('filterContextByConsent', () => {
    const mockContext = {
      device: {
        browser: { name: 'Chrome', version: '91.0' },
        os: { name: 'Windows', version: '10' }
      },
      location: {
        country: 'US',
        city: 'San Francisco'
      },
      timezone: 'America/Los_Angeles'
    }

    it('should return null when analytics consent is false', () => {
      const consent: ConsentConfig = {
        analytics: false,
        geolocation: true,
        fingerprinting: true,
        advertising: true
      }
      
      expect(filterContextByConsent(mockContext, consent)).toBeNull()
    })

    it('should remove location when geolocation consent is false', () => {
      const consent: ConsentConfig = {
        analytics: true,
        geolocation: false,
        fingerprinting: true,
        advertising: true
      }
      
      const filtered = filterContextByConsent(mockContext, consent)
      
      expect(filtered.location).toBeNull()
      expect(filtered.timezone).toBe('UTC')
    })

    it('should anonymize device info when fingerprinting consent is false', () => {
      const consent: ConsentConfig = {
        analytics: true,
        geolocation: true,
        fingerprinting: false,
        advertising: true
      }
      
      const filtered = filterContextByConsent(mockContext, consent)
      
      expect(filtered.device.browser.name).toBe('Unknown')
      expect(filtered.device.os.name).toBe('Unknown')
    })

    it('should preserve data when all consents are true', () => {
      const consent: ConsentConfig = {
        analytics: true,
        geolocation: true,
        fingerprinting: true,
        advertising: true
      }
      
      const filtered = filterContextByConsent(mockContext, consent)
      
      expect(filtered.device).toEqual(mockContext.device)
      expect(filtered.location).toEqual(mockContext.location)
    })
  })

  describe('auditPrivacyCompliance', () => {
    it('should audit compliance correctly', () => {
      const now = new Date()
      const records = [
        { timestamp: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000) }, // 5 days ago
        { timestamp: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), anonymized: true }, // 10 days ago
        { timestamp: new Date(now.getTime() - 100 * 24 * 60 * 60 * 1000) } // 100 days ago (should be deleted)
      ]
      
      const report = auditPrivacyCompliance(records)
      
      expect(report.totalRecords).toBe(3)
      expect(report.anonymizedRecords).toBe(1)
      expect(report.deletedRecords).toBe(1)
      expect(report.retentionCompliance).toBe(false) // Has records older than retention period
    })

    it('should report compliance when all records are within retention period', () => {
      const now = new Date()
      const records = [
        { timestamp: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000) },
        { timestamp: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) }
      ]
      
      const report = auditPrivacyCompliance(records)
      
      expect(report.retentionCompliance).toBe(true)
      expect(report.deletedRecords).toBe(0)
    })

    it('should handle empty records array', () => {
      const report = auditPrivacyCompliance([])
      
      expect(report.totalRecords).toBe(0)
      expect(report.oldestRecord).toBeNull()
      expect(report.newestRecord).toBeNull()
      expect(report.retentionCompliance).toBe(true)
    })
  })

  describe('Edge cases and security', () => {
    it('should handle null inputs gracefully', () => {
      expect(() => anonymizeIPAddress(null as any)).not.toThrow()
      expect(() => isValidIPAddress(null as any)).not.toThrow()
      expect(() => createPrivacyCompliantIPHash(null as any)).not.toThrow()
    })

    it('should not expose sensitive data in error messages', () => {
      // This is more of a conceptual test - in practice you'd check logs
      const sensitiveIP = '*************'
      const result = createPrivacyCompliantIPHash(sensitiveIP)
      
      expect(result).not.toContain(sensitiveIP)
    })

    it('should handle extremely long IP strings', () => {
      const longString = 'a'.repeat(1000)
      
      expect(() => isValidIPAddress(longString)).not.toThrow()
      expect(isValidIPAddress(longString)).toBe(false)
    })
  })
})