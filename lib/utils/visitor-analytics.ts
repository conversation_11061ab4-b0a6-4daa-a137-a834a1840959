import { VisitorContext, DeviceInfo, LocationInfo, ReferrerInfo } from './visitor-context'

/**
 * Analytics insights derived from visitor context
 */
export interface VisitorInsights {
  deviceCategory: 'mobile' | 'tablet' | 'desktop'
  browserFamily: string
  osFamily: string
  locationRegion: string | null
  trafficSource: 'direct' | 'search' | 'social' | 'referral' | 'internal'
  isBot: boolean
  isReturningVisitor: boolean
  sessionDuration: number | null
}

/**
 * Aggregated visitor analytics for reporting
 */
export interface VisitorAnalytics {
  totalVisitors: number
  uniqueVisitors: number
  deviceBreakdown: {
    mobile: number
    tablet: number
    desktop: number
  }
  browserBreakdown: Record<string, number>
  osBreakdown: Record<string, number>
  locationBreakdown: Record<string, number>
  trafficSources: {
    direct: number
    search: number
    social: number
    referral: number
    internal: number
  }
  topReferrers: Array<{
    domain: string
    count: number
    percentage: number
  }>
  botTraffic: number
  averageSessionDuration: number
}

/**
 * Generate insights from visitor context
 */
export function generateVisitorInsights(
  context: VisitorContext,
  previousVisits?: number,
  sessionStart?: Date
): VisitorInsights {
  const deviceCategory = determineDeviceCategory(context.device)
  const browserFamily = normalizeBrowserName(context.device.browser.name)
  const osFamily = normalizeOSName(context.device.os.name)
  const locationRegion = context.location ? 
    `${context.location.country}${context.location.region ? `-${context.location.region}` : ''}` : 
    null
  const trafficSource = determineTrafficSource(context.referrer)
  const isBot = detectBot(context.device)
  const isReturningVisitor = (previousVisits || 0) > 0
  const sessionDuration = sessionStart ? 
    Date.now() - sessionStart.getTime() : 
    null

  return {
    deviceCategory,
    browserFamily,
    osFamily,
    locationRegion,
    trafficSource,
    isBot,
    isReturningVisitor,
    sessionDuration
  }
}

/**
 * Aggregate visitor analytics from multiple contexts
 */
export function aggregateVisitorAnalytics(
  contexts: VisitorContext[],
  insights: VisitorInsights[]
): VisitorAnalytics {
  const totalVisitors = contexts.length
  const uniqueVisitors = new Set(contexts.map(c => c.ipHash).filter(Boolean)).size

  // Device breakdown
  const deviceBreakdown = {
    mobile: insights.filter(i => i.deviceCategory === 'mobile').length,
    tablet: insights.filter(i => i.deviceCategory === 'tablet').length,
    desktop: insights.filter(i => i.deviceCategory === 'desktop').length
  }

  // Browser breakdown
  const browserBreakdown = insights.reduce((acc, insight) => {
    acc[insight.browserFamily] = (acc[insight.browserFamily] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // OS breakdown
  const osBreakdown = insights.reduce((acc, insight) => {
    acc[insight.osFamily] = (acc[insight.osFamily] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Location breakdown
  const locationBreakdown = insights.reduce((acc, insight) => {
    if (insight.locationRegion) {
      acc[insight.locationRegion] = (acc[insight.locationRegion] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  // Traffic sources
  const trafficSources = {
    direct: insights.filter(i => i.trafficSource === 'direct').length,
    search: insights.filter(i => i.trafficSource === 'search').length,
    social: insights.filter(i => i.trafficSource === 'social').length,
    referral: insights.filter(i => i.trafficSource === 'referral').length,
    internal: insights.filter(i => i.trafficSource === 'internal').length
  }

  // Top referrers
  const referrerCounts = contexts.reduce((acc, context) => {
    if (context.referrer?.domain) {
      acc[context.referrer.domain] = (acc[context.referrer.domain] || 0) + 1
    }
    return acc
  }, {} as Record<string, number>)

  const topReferrers = Object.entries(referrerCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([domain, count]) => ({
      domain,
      count,
      percentage: Math.round((count / totalVisitors) * 100 * 10) / 10
    }))

  // Bot traffic
  const botTraffic = insights.filter(i => i.isBot).length

  // Average session duration
  const validDurations = insights
    .map(i => i.sessionDuration)
    .filter((d): d is number => d !== null && d > 0)
  const averageSessionDuration = validDurations.length > 0 ?
    Math.round(validDurations.reduce((sum, d) => sum + d, 0) / validDurations.length) :
    0

  return {
    totalVisitors,
    uniqueVisitors,
    deviceBreakdown,
    browserBreakdown,
    osBreakdown,
    locationBreakdown,
    trafficSources,
    topReferrers,
    botTraffic,
    averageSessionDuration
  }
}

/**
 * Determine device category from device info
 */
function determineDeviceCategory(device: DeviceInfo): 'mobile' | 'tablet' | 'desktop' {
  if (device.isMobile) return 'mobile'
  if (device.isTablet) return 'tablet'
  return 'desktop'
}

/**
 * Normalize browser names for consistent reporting
 */
function normalizeBrowserName(browserName: string): string {
  const normalized = browserName.toLowerCase()
  
  if (normalized.includes('chrome')) return 'Chrome'
  if (normalized.includes('firefox')) return 'Firefox'
  if (normalized.includes('safari') && !normalized.includes('chrome')) return 'Safari'
  if (normalized.includes('edge')) return 'Edge'
  if (normalized.includes('opera')) return 'Opera'
  if (normalized.includes('internet explorer') || normalized.includes('ie')) return 'Internet Explorer'
  if (normalized.includes('samsung')) return 'Samsung Browser'
  if (normalized.includes('mobile safari')) return 'Mobile Safari'
  
  return browserName || 'Unknown'
}

/**
 * Normalize OS names for consistent reporting
 */
function normalizeOSName(osName: string): string {
  const normalized = osName.toLowerCase()
  
  if (normalized.includes('windows')) return 'Windows'
  if (normalized.includes('mac') || normalized.includes('darwin')) return 'macOS'
  if (normalized.includes('linux')) return 'Linux'
  if (normalized.includes('android')) return 'Android'
  if (normalized.includes('ios') || normalized.includes('iphone') || normalized.includes('ipad')) return 'iOS'
  if (normalized.includes('chrome os') || normalized.includes('chromeos')) return 'Chrome OS'
  
  return osName || 'Unknown'
}

/**
 * Determine traffic source from referrer info
 */
function determineTrafficSource(referrer: ReferrerInfo | null): 'direct' | 'search' | 'social' | 'referral' | 'internal' {
  if (!referrer) return 'direct'
  if (referrer.isInternal) return 'internal'
  if (referrer.isSearch) return 'search'
  if (referrer.isSocial) return 'social'
  return 'referral'
}

/**
 * Detect if visitor is likely a bot based on device info
 */
function detectBot(device: DeviceInfo): boolean {
  const userAgent = `${device.browser.name} ${device.os.name}`.toLowerCase()
  
  const botIndicators = [
    'bot', 'crawler', 'spider', 'scraper', 'fetch', 'curl', 'wget',
    'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider',
    'yandexbot', 'facebookexternalhit', 'twitterbot', 'linkedinbot',
    'whatsapp', 'telegram', 'discord', 'slack', 'zoom', 'skype',
    'headless', 'phantom', 'selenium', 'puppeteer', 'playwright'
  ]
  
  return botIndicators.some(indicator => userAgent.includes(indicator))
}

/**
 * Calculate visitor engagement score based on context and behavior
 */
export function calculateEngagementScore(
  context: VisitorContext,
  insights: VisitorInsights,
  pageViews: number = 1,
  timeOnSite: number = 0,
  interactions: number = 0
): number {
  let score = 0
  
  // Base score for visiting
  score += 10
  
  // Device type bonus (mobile users might be more engaged)
  if (insights.deviceCategory === 'mobile') score += 5
  
  // Returning visitor bonus
  if (insights.isReturningVisitor) score += 15
  
  // Traffic source quality
  switch (insights.trafficSource) {
    case 'direct': score += 20; break
    case 'search': score += 15; break
    case 'social': score += 10; break
    case 'referral': score += 12; break
    case 'internal': score += 8; break
  }
  
  // Page views bonus
  score += Math.min(pageViews * 5, 25)
  
  // Time on site bonus (diminishing returns)
  const timeMinutes = timeOnSite / (1000 * 60)
  score += Math.min(Math.log(timeMinutes + 1) * 10, 30)
  
  // Interactions bonus
  score += Math.min(interactions * 3, 20)
  
  // Penalize bots
  if (insights.isBot) score = Math.max(score * 0.1, 1)
  
  return Math.round(Math.max(0, Math.min(100, score)))
}

/**
 * Generate privacy-safe visitor fingerprint for deduplication
 */
export function generateVisitorFingerprint(context: VisitorContext): string {
  const components = [
    context.device.browser.name,
    context.device.os.name,
    context.device.device.type,
    context.location?.country || '',
    context.timezone,
    context.ipHash?.substring(0, 8) || '' // Only use first 8 chars of IP hash
  ]
  
  // Create a hash of the components for privacy
  const fingerprint = components.join('|')
  return Buffer.from(fingerprint).toString('base64').substring(0, 16)
}

/**
 * Detect visitor preferences from context
 */
export function detectVisitorPreferences(context: VisitorContext): {
  preferredLanguage: string | null
  timeZone: string
  isDarkModePreferred: boolean | null
  isHighContrastPreferred: boolean | null
} {
  return {
    preferredLanguage: extractLanguageFromLocation(context.location),
    timeZone: context.timezone,
    isDarkModePreferred: null, // Would need additional detection logic
    isHighContrastPreferred: null // Would need additional detection logic
  }
}

/**
 * Extract likely language preference from location
 */
function extractLanguageFromLocation(location: LocationInfo | null): string | null {
  if (!location?.country) return null
  
  const countryLanguageMap: Record<string, string> = {
    'US': 'en',
    'GB': 'en',
    'CA': 'en',
    'AU': 'en',
    'DE': 'de',
    'FR': 'fr',
    'ES': 'es',
    'IT': 'it',
    'JP': 'ja',
    'KR': 'ko',
    'CN': 'zh',
    'BR': 'pt',
    'RU': 'ru',
    'IN': 'en',
    'MX': 'es',
    'NL': 'nl',
    'SE': 'sv',
    'NO': 'no',
    'DK': 'da',
    'FI': 'fi'
  }
  
  return countryLanguageMap[location.country.toUpperCase()] || null
}