/**
 * Comprehensive accessibility audit utilities
 */

export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info'
  message: string
  element?: HTMLElement
  wcagRule?: string
}

export interface AccessibilityAuditResult {
  passed: boolean
  issues: AccessibilityIssue[]
  score: number
}

// Comprehensive accessibility audit
export function runAccessibilityAudit(): AccessibilityAuditResult {
  if (typeof window === 'undefined') {
    return { passed: true, issues: [], score: 100 }
  }

  const issues: AccessibilityIssue[] = []

  // Check 1: Images without alt text
  const imagesWithoutAlt = Array.from(document.querySelectorAll('img')).filter(
    img => !img.alt && !img.getAttribute('aria-hidden') && !img.getAttribute('role')
  )
  
  imagesWithoutAlt.forEach(img => {
    issues.push({
      type: 'error',
      message: 'Image missing alt text',
      element: img,
      wcagRule: 'WCAG 1.1.1'
    })
  })

  // Check 2: Form inputs without labels
  const inputsWithoutLabels = Array.from(document.querySelectorAll('input, select, textarea')).filter(
    input => {
      const id = input.id
      const ariaLabel = input.getAttribute('aria-label')
      const ariaLabelledBy = input.getAttribute('aria-labelledby')
      const label = id ? document.querySelector(`label[for="${id}"]`) : null
      
      return !label && !ariaLabel && !ariaLabelledBy
    }
  )
  
  inputsWithoutLabels.forEach(input => {
    issues.push({
      type: 'error',
      message: 'Form input missing label',
      element: input as HTMLElement,
      wcagRule: 'WCAG 1.3.1'
    })
  })

  // Check 3: Heading hierarchy
  const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
  const headingLevels = headings.map(h => parseInt(h.tagName.charAt(1)))
  
  for (let i = 1; i < headingLevels.length; i++) {
    const current = headingLevels[i]
    const previous = headingLevels[i - 1]
    
    if (current > previous + 1) {
      issues.push({
        type: 'warning',
        message: `Heading hierarchy skip: h${previous} followed by h${current}`,
        element: headings[i] as HTMLElement,
        wcagRule: 'WCAG 1.3.1'
      })
    }
  }

  // Check 4: Links without accessible names
  const linksWithoutNames = Array.from(document.querySelectorAll('a')).filter(
    link => {
      const text = link.textContent?.trim()
      const ariaLabel = link.getAttribute('aria-label')
      const ariaLabelledBy = link.getAttribute('aria-labelledby')
      const title = link.getAttribute('title')
      
      return !text && !ariaLabel && !ariaLabelledBy && !title
    }
  )
  
  linksWithoutNames.forEach(link => {
    issues.push({
      type: 'error',
      message: 'Link without accessible name',
      element: link,
      wcagRule: 'WCAG 2.4.4'
    })
  })

  // Check 5: Buttons without accessible names
  const buttonsWithoutNames = Array.from(document.querySelectorAll('button')).filter(
    button => {
      const text = button.textContent?.trim()
      const ariaLabel = button.getAttribute('aria-label')
      const ariaLabelledBy = button.getAttribute('aria-labelledby')
      const title = button.getAttribute('title')
      
      return !text && !ariaLabel && !ariaLabelledBy && !title
    }
  )
  
  buttonsWithoutNames.forEach(button => {
    issues.push({
      type: 'error',
      message: 'Button without accessible name',
      element: button,
      wcagRule: 'WCAG 4.1.2'
    })
  })

  // Check 6: Touch target sizes (mobile)
  if (window.innerWidth <= 768) {
    const smallTouchTargets = Array.from(document.querySelectorAll('button, a, [role="button"]')).filter(
      element => {
        const rect = element.getBoundingClientRect()
        return rect.width < 44 || rect.height < 44
      }
    )
    
    smallTouchTargets.forEach(element => {
      issues.push({
        type: 'warning',
        message: 'Touch target smaller than 44px',
        element: element as HTMLElement,
        wcagRule: 'WCAG 2.5.5'
      })
    })
  }

  // Check 7: Focus indicators
  const focusableElements = Array.from(document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ))
  
  // This is a simplified check - in practice you'd test actual focus styles
  const elementsWithoutFocusStyles = focusableElements.filter(element => {
    const styles = window.getComputedStyle(element as HTMLElement)
    return !styles.outline && !styles.boxShadow
  })
  
  if (elementsWithoutFocusStyles.length > 0) {
    issues.push({
      type: 'info',
      message: `${elementsWithoutFocusStyles.length} elements may lack focus indicators`,
      wcagRule: 'WCAG 2.4.7'
    })
  }

  // Calculate score
  const errorCount = issues.filter(i => i.type === 'error').length
  const warningCount = issues.filter(i => i.type === 'warning').length
  const infoCount = issues.filter(i => i.type === 'info').length
  
  const totalDeductions = (errorCount * 10) + (warningCount * 5) + (infoCount * 1)
  const score = Math.max(0, 100 - totalDeductions)
  
  return {
    passed: errorCount === 0,
    issues,
    score
  }
}

// Mobile responsiveness audit
export function runMobileResponsivenessAudit(): AccessibilityAuditResult {
  if (typeof window === 'undefined') {
    return { passed: true, issues: [], score: 100 }
  }

  const issues: AccessibilityIssue[] = []

  // Check viewport meta tag
  const viewport = document.querySelector('meta[name="viewport"]')
  if (!viewport) {
    issues.push({
      type: 'error',
      message: 'Missing viewport meta tag',
      wcagRule: 'Mobile Best Practice'
    })
  }

  // Check for horizontal scrolling
  if (document.body.scrollWidth > window.innerWidth) {
    issues.push({
      type: 'warning',
      message: 'Horizontal scrolling detected',
      wcagRule: 'Mobile Best Practice'
    })
  }

  // Check font sizes (minimum 16px on mobile to prevent zoom)
  if (window.innerWidth <= 768) {
    const smallTextElements = Array.from(document.querySelectorAll('*')).filter(element => {
      const styles = window.getComputedStyle(element as HTMLElement)
      const fontSize = parseFloat(styles.fontSize)
      return fontSize < 16 && element.textContent?.trim()
    })

    if (smallTextElements.length > 0) {
      issues.push({
        type: 'warning',
        message: `${smallTextElements.length} elements with font size < 16px on mobile`,
        wcagRule: 'Mobile Best Practice'
      })
    }
  }

  const score = Math.max(0, 100 - (issues.length * 10))
  
  return {
    passed: issues.filter(i => i.type === 'error').length === 0,
    issues,
    score
  }
}

// Generate accessibility report
export function generateAccessibilityReport(): string {
  const accessibilityAudit = runAccessibilityAudit()
  const mobileAudit = runMobileResponsivenessAudit()
  
  let report = '# Accessibility Audit Report\n\n'
  
  report += `## Overall Accessibility Score: ${accessibilityAudit.score}/100\n`
  report += `## Mobile Responsiveness Score: ${mobileAudit.score}/100\n\n`
  
  if (accessibilityAudit.issues.length > 0) {
    report += '## Accessibility Issues\n\n'
    accessibilityAudit.issues.forEach((issue, index) => {
      report += `${index + 1}. **${issue.type.toUpperCase()}**: ${issue.message}`
      if (issue.wcagRule) {
        report += ` (${issue.wcagRule})`
      }
      report += '\n'
    })
    report += '\n'
  }
  
  if (mobileAudit.issues.length > 0) {
    report += '## Mobile Responsiveness Issues\n\n'
    mobileAudit.issues.forEach((issue, index) => {
      report += `${index + 1}. **${issue.type.toUpperCase()}**: ${issue.message}`
      if (issue.wcagRule) {
        report += ` (${issue.wcagRule})`
      }
      report += '\n'
    })
  }
  
  if (accessibilityAudit.issues.length === 0 && mobileAudit.issues.length === 0) {
    report += '## ✅ No accessibility issues found!\n\n'
    report += 'Your application meets WCAG 2.1 AA standards and mobile responsiveness best practices.'
  }
  
  return report
}

// Development helper
export function logAccessibilityReport(): void {
  if (process.env.NODE_ENV === 'development') {
    console.group('🔍 Accessibility Audit Report')
    console.log(generateAccessibilityReport())
    console.groupEnd()
  }
}