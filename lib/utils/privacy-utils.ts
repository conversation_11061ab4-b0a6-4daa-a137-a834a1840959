import { createHash, randomBytes } from 'crypto'

/**
 * Privacy-compliant IP address handling utilities
 */

/**
 * Configuration for IP hashing and privacy
 */
export interface PrivacyConfig {
  saltRotationDays: number
  ipRetentionDays: number
  enableGeolocation: boolean
  enableFingerprinting: boolean
  anonymizeAfterDays: number
}

/**
 * Default privacy configuration
 */
export const DEFAULT_PRIVACY_CONFIG: PrivacyConfig = {
  saltRotationDays: 30,
  ipRetentionDays: 90,
  enableGeolocation: true,
  enableFingerprinting: true,
  anonymizeAfterDays: 7
}

/**
 * Generate or retrieve current salt for IP hashing
 */
export function getCurrentSalt(): string {
  const saltKey = 'IP_HASH_SALT'
  const rotationKey = 'SALT_ROTATION_DATE'
  
  // Check if we have a stored salt and when it was created
  const storedSalt = process.env[saltKey]
  const rotationDate = process.env[rotationKey]
  
  const now = new Date()
  const shouldRotate = !rotationDate || 
    (now.getTime() - new Date(rotationDate).getTime()) > 
    (DEFAULT_PRIVACY_CONFIG.saltRotationDays * 24 * 60 * 60 * 1000)
  
  if (!storedSalt || shouldRotate) {
    // Generate new salt
    const newSalt = randomBytes(32).toString('hex')
    
    // In a real application, you would store this securely
    // For now, we'll use a deterministic fallback
    return process.env.IP_HASH_SALT || 'fallback-salt-' + now.getFullYear() + '-' + Math.floor(now.getMonth() / 3)
  }
  
  return storedSalt
}

/**
 * Create privacy-compliant IP hash with automatic salt rotation
 */
export function createPrivacyCompliantIPHash(ip: string): string {
  if (!ip || ip === 'unknown') return ''
  
  try {
    // Anonymize IP by removing last octet for IPv4 or last 64 bits for IPv6
    const anonymizedIP = anonymizeIPAddress(ip)
    
    // Hash with current salt
    const salt = getCurrentSalt()
    const hash = createHash('sha256')
    hash.update(anonymizedIP + salt)
    
    return hash.digest('hex')
  } catch (error) {
    console.error('Error creating IP hash:', error)
    return ''
  }
}

/**
 * Anonymize IP address by removing identifying parts
 */
export function anonymizeIPAddress(ip: string): string {
  if (!ip) return ''
  
  try {
    // IPv4 anonymization - remove last octet
    if (ip.includes('.') && !ip.includes(':')) {
      const parts = ip.split('.')
      if (parts.length === 4) {
        return `${parts[0]}.${parts[1]}.${parts[2]}.0`
      }
    }
    
    // IPv6 anonymization - remove last 64 bits
    if (ip.includes(':')) {
      const parts = ip.split(':')
      if (parts.length >= 4) {
        // Keep first 4 groups (64 bits), zero out the rest
        const anonymized = parts.slice(0, 4).join(':') + '::0'
        return anonymized
      }
    }
    
    return ip
  } catch (error) {
    return ip
  }
}

/**
 * Check if data should be anonymized based on age
 */
export function shouldAnonymizeData(timestamp: Date, config: PrivacyConfig = DEFAULT_PRIVACY_CONFIG): boolean {
  const now = new Date()
  const ageInDays = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60 * 24)
  
  return ageInDays > config.anonymizeAfterDays
}

/**
 * Check if data should be deleted based on retention policy
 */
export function shouldDeleteData(timestamp: Date, config: PrivacyConfig = DEFAULT_PRIVACY_CONFIG): boolean {
  const now = new Date()
  const ageInDays = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60 * 24)
  
  return ageInDays > config.ipRetentionDays
}

/**
 * Create anonymized visitor context for long-term storage
 */
export function anonymizeVisitorContext(context: any): any {
  return {
    ...context,
    ipHash: context.ipHash ? context.ipHash.substring(0, 8) + 'xxxx' : null,
    location: context.location ? {
      country: context.location.country,
      region: null, // Remove region for privacy
      city: null,   // Remove city for privacy
      latitude: '',
      longitude: '',
      timezone: context.location.timezone
    } : null,
    timestamp: context.timestamp
  }
}

/**
 * Validate IP address format
 */
export function isValidIPAddress(ip: string): boolean {
  if (!ip || typeof ip !== 'string') return false
  
  // IPv4 validation
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (ipv4Regex.test(ip)) {
    const octets = ip.split('.')
    return octets.every(octet => {
      const num = parseInt(octet, 10)
      return num >= 0 && num <= 255 && octet === num.toString()
    })
  }
  
  // IPv6 validation (more comprehensive)
  // Handle various IPv6 formats including compressed notation
  const ipv6Patterns = [
    /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/, // Full format
    /^::1$/, // Localhost
    /^::$/, // All zeros
    /^([0-9a-fA-F]{1,4}:){1,7}:$/, // Ending with ::
    /^:([0-9a-fA-F]{1,4}:){1,7}$/, // Starting with ::
    /^([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}$/, // One :: in middle
    /^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$/, // Multiple groups
    /^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$/, // Multiple groups
    /^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$/, // Multiple groups
    /^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$/, // Multiple groups
    /^[0-9a-fA-F]{1,4}:(:[0-9a-fA-F]{1,4}){1,6}$/, // Multiple groups
    /^:((:[0-9a-fA-F]{1,4}){1,7}|:)$/, // Starting with :: variations
    /^([0-9a-fA-F]{1,4}:){1,7}:$/, // Ending with :: variations
  ]
  
  return ipv6Patterns.some(pattern => pattern.test(ip))
}

/**
 * Check if IP address is from a private network
 */
export function isPrivateIPAddress(ip: string): boolean {
  if (!isValidIPAddress(ip)) return false
  
  // IPv4 private ranges
  if (ip.includes('.') && !ip.includes(':')) {
    const octets = ip.split('.').map(Number)
    
    // 10.0.0.0/8
    if (octets[0] === 10) return true
    
    // **********/12
    if (octets[0] === 172 && octets[1] >= 16 && octets[1] <= 31) return true
    
    // ***********/16
    if (octets[0] === 192 && octets[1] === 168) return true
    
    // *********/8 (localhost)
    if (octets[0] === 127) return true
  }
  
  // IPv6 private ranges
  if (ip.includes(':')) {
    const normalized = ip.toLowerCase()
    
    // ::1 (localhost)
    if (normalized === '::1') return true
    
    // fc00::/7 (unique local addresses)
    if (normalized.startsWith('fc00:') || normalized.startsWith('fd')) return true
    
    // fe80::/10 (link-local addresses)
    if (normalized.startsWith('fe80:')) return true
    
    // Check for compressed notation
    if (normalized.includes('::')) {
      // Expand to check prefixes properly
      const parts = normalized.split('::')
      if (parts[0] === '' && parts[1].startsWith('1')) return true // ::1
      if (parts[0].startsWith('fc') || parts[0].startsWith('fd')) return true
      if (parts[0].startsWith('fe8')) return true
    }
  }
  
  return false
}

/**
 * Extract real IP from various proxy headers
 */
export function extractRealIP(headers: Headers): string | null {
  const ipHeaders = [
    'cf-connecting-ip',      // Cloudflare
    'x-real-ip',            // Nginx
    'x-forwarded-for',      // Standard proxy header
    'x-client-ip',          // Apache
    'x-cluster-client-ip',  // Cluster
    'x-forwarded',          // Proxy
    'forwarded-for',        // Proxy
    'forwarded'             // RFC 7239
  ]
  
  for (const header of ipHeaders) {
    const value = headers.get(header)
    if (value) {
      // Handle comma-separated IPs (take the first non-private one)
      const ips = value.split(',').map(ip => ip.trim())
      
      for (const ip of ips) {
        if (isValidIPAddress(ip) && !isPrivateIPAddress(ip)) {
          return ip
        }
      }
      
      // If no public IP found, return the first valid one
      for (const ip of ips) {
        if (isValidIPAddress(ip)) {
          return ip
        }
      }
    }
  }
  
  return null
}

/**
 * Create consent-aware tracking configuration
 */
export interface ConsentConfig {
  analytics: boolean
  geolocation: boolean
  fingerprinting: boolean
  advertising: boolean
}

/**
 * Filter visitor context based on consent
 */
export function filterContextByConsent(
  context: any,
  consent: ConsentConfig
): any {
  const filtered = { ...context }
  
  if (!consent.analytics) {
    // Remove all tracking data
    return null
  }
  
  if (!consent.geolocation) {
    // Remove location data
    filtered.location = null
    filtered.timezone = 'UTC'
  }
  
  if (!consent.fingerprinting) {
    // Remove detailed device info
    filtered.device = {
      browser: { name: 'Unknown', version: 'Unknown' },
      os: { name: 'Unknown', version: 'Unknown' },
      device: { type: 'desktop', vendor: 'Unknown', model: 'Unknown' },
      isMobile: false,
      isTablet: false,
      isDesktop: true
    }
  }
  
  return filtered
}

/**
 * Generate privacy report for compliance
 */
export interface PrivacyReport {
  totalRecords: number
  anonymizedRecords: number
  deletedRecords: number
  retentionCompliance: boolean
  oldestRecord: Date | null
  newestRecord: Date | null
}

/**
 * Audit data retention and privacy compliance
 */
export function auditPrivacyCompliance(
  records: Array<{ timestamp: Date; anonymized?: boolean }>,
  config: PrivacyConfig = DEFAULT_PRIVACY_CONFIG
): PrivacyReport {
  const now = new Date()
  const totalRecords = records.length
  let anonymizedRecords = 0
  let deletedRecords = 0
  let oldestRecord: Date | null = null
  let newestRecord: Date | null = null
  
  for (const record of records) {
    const age = (now.getTime() - record.timestamp.getTime()) / (1000 * 60 * 60 * 24)
    
    if (record.anonymized) {
      anonymizedRecords++
    }
    
    if (age > config.ipRetentionDays) {
      deletedRecords++
    }
    
    if (!oldestRecord || record.timestamp < oldestRecord) {
      oldestRecord = record.timestamp
    }
    
    if (!newestRecord || record.timestamp > newestRecord) {
      newestRecord = record.timestamp
    }
  }
  
  const retentionCompliance = deletedRecords === 0 || 
    (oldestRecord && (now.getTime() - oldestRecord.getTime()) / (1000 * 60 * 60 * 24) <= config.ipRetentionDays)
  
  return {
    totalRecords,
    anonymizedRecords,
    deletedRecords,
    retentionCompliance,
    oldestRecord,
    newestRecord
  }
}