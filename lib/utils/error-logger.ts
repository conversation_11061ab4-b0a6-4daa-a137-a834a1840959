/**
 * Comprehensive error logging and monitoring system
 * Provides structured logging, error tracking, and monitoring capabilities
 */

import { AppError, isAppError, shouldReportError } from '@/lib/errors/app-errors'

export interface LogContext {
  userId?: string
  sessionId?: string
  userAgent?: string
  ip?: string
  url?: string
  method?: string
  timestamp?: string
  requestId?: string
  [key: string]: unknown
}

export interface ErrorLogEntry {
  id: string
  timestamp: string
  level: 'error' | 'warn' | 'info' | 'debug'
  message: string
  error?: AppError | Error
  context?: LogContext
  stack?: string
  fingerprint?: string
}

export type LogLevel = 'error' | 'warn' | 'info' | 'debug'

class ErrorLogger {
  private logs: ErrorLogEntry[] = []
  private maxLogs = 1000
  private logLevel: LogLevel = process.env.NODE_ENV === 'development' ? 'debug' : 'error'

  /**
   * Generate unique ID for log entries
   */
  private generateId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Generate error fingerprint for deduplication
   */
  private generateFingerprint(error: Error, context?: LogContext): string {
    const errorInfo = `${error.name}:${error.message}`
    const contextInfo = context?.url || context?.method || ''
    return btoa(`${errorInfo}:${contextInfo}`).replace(/[^a-zA-Z0-9]/g, '').substr(0, 16)
  }

  /**
   * Check if log level should be recorded
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = levels.indexOf(this.logLevel)
    const messageLevelIndex = levels.indexOf(level)
    return messageLevelIndex >= currentLevelIndex
  }

  /**
   * Add log entry to internal storage
   */
  private addLogEntry(entry: ErrorLogEntry): void {
    this.logs.unshift(entry)
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs)
    }
  }

  /**
   * Log error with context
   */
  error(message: string, context?: LogContext, error?: Error | AppError): void {
    if (!this.shouldLog('error')) return

    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'error',
      message,
      error,
      context,
      stack: error?.stack,
      fingerprint: error ? this.generateFingerprint(error, context) : undefined
    }

    this.addLogEntry(entry)

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[ERROR] ${message}`, {
        context,
        error: error?.message,
        stack: error?.stack
      })
    }

    // Report to external monitoring if configured
    this.reportToMonitoring(entry)
  }

  /**
   * Log warning with context
   */
  warn(message: string, context?: LogContext, error?: Error): void {
    if (!this.shouldLog('warn')) return

    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'warn',
      message,
      error,
      context,
      stack: error?.stack
    }

    this.addLogEntry(entry)

    if (process.env.NODE_ENV === 'development') {
      console.warn(`[WARN] ${message}`, { context, error: error?.message })
    }
  }

  /**
   * Log info with context
   */
  info(message: string, context?: LogContext): void {
    if (!this.shouldLog('info')) return

    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'info',
      message,
      context
    }

    this.addLogEntry(entry)

    if (process.env.NODE_ENV === 'development') {
      console.info(`[INFO] ${message}`, { context })
    }
  }

  /**
   * Log debug information
   */
  debug(message: string, context?: LogContext): void {
    if (!this.shouldLog('debug')) return

    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: 'debug',
      message,
      context
    }

    this.addLogEntry(entry)

    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, { context })
    }
  }

  /**
   * Report error to external monitoring service
   */
  private reportToMonitoring(entry: ErrorLogEntry): void {
    // Skip reporting if error shouldn't be reported
    if (entry.error && !shouldReportError(entry.error)) {
      return
    }

    // Report to external service (e.g., Sentry, LogRocket, etc.)
    if (typeof window !== 'undefined') {
      // Client-side reporting
      this.reportToClientMonitoring(entry)
    } else {
      // Server-side reporting
      this.reportToServerMonitoring(entry)
    }
  }

  /**
   * Report to client-side monitoring
   */
  private reportToClientMonitoring(entry: ErrorLogEntry): void {
    // Example: Report to Sentry
    if ((window as any).Sentry) {
      (window as any).Sentry.captureException(entry.error || new Error(entry.message), {
        tags: {
          level: entry.level,
          fingerprint: entry.fingerprint
        },
        contexts: {
          custom: entry.context
        }
      })
    }

    // Example: Report to custom monitoring endpoint
    if (entry.level === 'error' && typeof fetch !== 'undefined') {
      fetch('/api/monitoring/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: entry.id,
          message: entry.message,
          error: entry.error ? {
            name: entry.error.name,
            message: entry.error.message,
            stack: entry.error.stack
          } : null,
          context: entry.context,
          timestamp: entry.timestamp,
          fingerprint: entry.fingerprint
        })
      }).catch(err => {
        console.error('Failed to report error to monitoring:', err)
      })
    }
  }

  /**
   * Report to server-side monitoring
   */
  private reportToServerMonitoring(entry: ErrorLogEntry): void {
    // Example: Log to file system or external service
    if (process.env.NODE_ENV === 'production') {
      // In production, you might want to:
      // - Send to external logging service (e.g., Winston, Pino)
      // - Store in database for analysis
      // - Send to monitoring service (e.g., DataDog, New Relic)
      
      // For now, just console.error in production for server logs
      console.error(`[${entry.level.toUpperCase()}] ${entry.message}`, {
        id: entry.id,
        timestamp: entry.timestamp,
        context: entry.context,
        error: entry.error ? {
          name: entry.error.name,
          message: entry.error.message,
          stack: entry.error.stack
        } : null
      })
    }
  }

  /**
   * Get recent logs for debugging
   */
  getRecentLogs(count: number = 50): ErrorLogEntry[] {
    return this.logs.slice(0, count)
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): ErrorLogEntry[] {
    return this.logs.filter(log => log.level === level)
  }

  /**
   * Get logs by fingerprint (for error grouping)
   */
  getLogsByFingerprint(fingerprint: string): ErrorLogEntry[] {
    return this.logs.filter(log => log.fingerprint === fingerprint)
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = []
  }

  /**
   * Set log level
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }

  /**
   * Get current log level
   */
  getLogLevel(): LogLevel {
    return this.logLevel
  }

  /**
   * Export logs for analysis
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// Create singleton instance
export const errorLogger = new ErrorLogger()

/**
 * Utility function to handle and log errors in Server Actions
 */
export function handleServerActionError(
  error: unknown,
  operation: string,
  context?: LogContext
): { success: false; error: string; code?: string } {
  const appError = isAppError(error) ? error : new AppError(
    error instanceof Error ? error.message : 'Unknown error',
    'INTERNAL_ERROR',
    500,
    { originalError: error }
  )

  errorLogger.error(`Server Action error: ${operation}`, context, appError)

  return {
    success: false,
    error: appError.getUserMessage(),
    code: appError.code
  }
}

/**
 * Utility function to handle and log errors in API routes
 */
export function handleApiError(
  error: unknown,
  operation: string,
  context?: LogContext
): Response {
  const appError = isAppError(error) ? error : new AppError(
    error instanceof Error ? error.message : 'Unknown error',
    'INTERNAL_ERROR',
    500,
    { originalError: error }
  )

  errorLogger.error(`API error: ${operation}`, context, appError)

  return new Response(
    JSON.stringify({
      error: {
        message: appError.getUserMessage(),
        code: appError.code
      },
      timestamp: new Date().toISOString()
    }),
    {
      status: appError.statusCode,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  )
}

/**
 * Middleware to add request context to error logging
 */
export function withErrorContext(
  request: Request,
  additionalContext?: Record<string, unknown>
): LogContext {
  return {
    url: request.url,
    method: request.method,
    userAgent: request.headers.get('user-agent') || undefined,
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
    ...additionalContext
  }
}

/**
 * React hook for error handling in components
 */
export function useErrorHandler() {
  const handleError = (error: unknown, context?: LogContext) => {
    errorLogger.error('Component error', context, error instanceof Error ? error : new Error(String(error)))
  }

  const handleWarning = (message: string, context?: LogContext) => {
    errorLogger.warn(message, context)
  }

  return { handleError, handleWarning }
}