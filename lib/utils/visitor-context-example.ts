/**
 * Example usage of the Visitor Context Detection System
 * 
 * This file demonstrates how to integrate the visitor context system
 * into your Next.js application for comprehensive analytics tracking.
 */

import { NextRequest } from 'next/server'
import { 
  createVisitorContext, 
  VisitorContext 
} from './visitor-context'
import { 
  generateVisitorInsights, 
  calculateEngagementScore,
  generateVisitorFingerprint,
  detectVisitorPreferences 
} from './visitor-analytics'
import { 
  createPrivacyCompliantIPHash,
  filterContextByConsent,
  ConsentConfig 
} from './privacy-utils'

/**
 * Example: Track a profile view with enhanced visitor context
 */
export async function trackProfileViewExample(
  request: NextRequest,
  profileId: string
) {
  try {
    // Extract request information
    const headers = request.headers
    const userAgent = headers.get('user-agent') || ''
    const referrer = headers.get('referer') || undefined
    const host = headers.get('host') || 'localhost'
    const currentDomain = host.split(':')[0]

    // Create comprehensive visitor context
    const context = createVisitorContext(
      userAgent,
      headers,
      referrer,
      currentDomain
    )

    // Generate visitor insights
    const insights = generateVisitorInsights(context)

    // Calculate engagement score (you would get these from your session tracking)
    const pageViews = 1 // This would come from session data
    const timeOnSite = 0 // This would come from session tracking
    const interactions = 0 // This would come from user interactions

    const engagementScore = calculateEngagementScore(
      context,
      insights,
      pageViews,
      timeOnSite,
      interactions
    )

    // Generate privacy-safe visitor fingerprint for deduplication
    const fingerprint = generateVisitorFingerprint(context)

    // Detect visitor preferences
    const preferences = detectVisitorPreferences(context)

    // Log the collected data (in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Visitor Context Example:', {
        profileId,
        deviceCategory: insights.deviceCategory,
        trafficSource: insights.trafficSource,
        location: context.location?.country,
        engagementScore,
        fingerprint,
        preferences
      })
    }

    // Here you would save to your analytics system
    // await AnalyticsRepository.trackProfileView(...)

    return {
      success: true,
      context,
      insights,
      engagementScore,
      fingerprint
    }

  } catch (error) {
    console.error('Error tracking profile view:', error)
    return { success: false, error: 'Failed to track view' }
  }
}

/**
 * Example: Handle user consent and filter data accordingly
 */
export async function trackWithConsentExample(
  request: NextRequest,
  profileId: string,
  userConsent: ConsentConfig
) {
  try {
    // Create full visitor context
    const headers = request.headers
    const userAgent = headers.get('user-agent') || ''
    const referrer = headers.get('referer') || undefined
    const currentDomain = headers.get('host')?.split(':')[0] || 'localhost'

    const fullContext = createVisitorContext(
      userAgent,
      headers,
      referrer,
      currentDomain
    )

    // Filter context based on user consent
    const filteredContext = filterContextByConsent(fullContext, userConsent)

    if (!filteredContext) {
      // User declined analytics tracking
      return { success: true, message: 'Tracking declined by user' }
    }

    // Generate insights from filtered context
    const insights = generateVisitorInsights(filteredContext)

    // Example consent-aware logging
    console.log('Consent-filtered tracking:', {
      profileId,
      hasLocation: !!filteredContext.location,
      hasDeviceInfo: filteredContext.device.browser.name !== 'Unknown',
      trafficSource: insights.trafficSource
    })

    // Save filtered data to analytics
    // await AnalyticsRepository.trackProfileView(...)

    return { success: true, context: filteredContext, insights }

  } catch (error) {
    console.error('Error in consent-aware tracking:', error)
    return { success: false, error: 'Failed to track with consent' }
  }
}

/**
 * Example: Batch process visitor contexts for reporting
 */
export async function generateAnalyticsReportExample(
  contexts: VisitorContext[]
) {
  try {
    // Generate insights for all contexts
    const insights = contexts.map(context => 
      generateVisitorInsights(context)
    )

    // Calculate engagement scores
    const engagementScores = contexts.map((context, index) => 
      calculateEngagementScore(context, insights[index])
    )

    // Aggregate analytics
    const { aggregateVisitorAnalytics } = await import('./visitor-analytics')
    const analytics = aggregateVisitorAnalytics(contexts, insights)

    // Generate report
    const report = {
      summary: {
        totalVisitors: analytics.totalVisitors,
        uniqueVisitors: analytics.uniqueVisitors,
        averageEngagement: Math.round(
          engagementScores.reduce((sum, score) => sum + score, 0) / 
          engagementScores.length
        )
      },
      devices: analytics.deviceBreakdown,
      browsers: analytics.browserBreakdown,
      traffic: analytics.trafficSources,
      locations: analytics.locationBreakdown,
      topReferrers: analytics.topReferrers.slice(0, 5),
      insights: {
        mobileTrafficPercentage: Math.round(
          (analytics.deviceBreakdown.mobile / analytics.totalVisitors) * 100
        ),
        organicTrafficPercentage: Math.round(
          (analytics.trafficSources.search / analytics.totalVisitors) * 100
        ),
        socialTrafficPercentage: Math.round(
          (analytics.trafficSources.social / analytics.totalVisitors) * 100
        ),
        botTrafficPercentage: Math.round(
          (analytics.botTraffic / analytics.totalVisitors) * 100
        )
      }
    }

    return report

  } catch (error) {
    console.error('Error generating analytics report:', error)
    throw new Error('Failed to generate report')
  }
}

/**
 * Example: Real-time visitor monitoring
 */
export class VisitorMonitor {
  private recentVisitors: Map<string, VisitorContext> = new Map()
  private readonly maxRecentVisitors = 1000

  /**
   * Add a new visitor to monitoring
   */
  addVisitor(context: VisitorContext): void {
    const fingerprint = generateVisitorFingerprint(context)
    
    // Store recent visitor
    this.recentVisitors.set(fingerprint, context)

    // Cleanup old visitors
    if (this.recentVisitors.size > this.maxRecentVisitors) {
      const firstKey = this.recentVisitors.keys().next().value
      this.recentVisitors.delete(firstKey)
    }
  }

  /**
   * Get real-time visitor statistics
   */
  getRealTimeStats() {
    const contexts = Array.from(this.recentVisitors.values())
    const insights = contexts.map(context => generateVisitorInsights(context))

    const now = Date.now()
    const fiveMinutesAgo = now - (5 * 60 * 1000)
    
    const recentContexts = contexts.filter(
      context => context.timestamp.getTime() > fiveMinutesAgo
    )

    return {
      totalVisitors: contexts.length,
      recentVisitors: recentContexts.length,
      deviceBreakdown: {
        mobile: insights.filter(i => i.deviceCategory === 'mobile').length,
        tablet: insights.filter(i => i.deviceCategory === 'tablet').length,
        desktop: insights.filter(i => i.deviceCategory === 'desktop').length
      },
      trafficSources: {
        direct: insights.filter(i => i.trafficSource === 'direct').length,
        search: insights.filter(i => i.trafficSource === 'search').length,
        social: insights.filter(i => i.trafficSource === 'social').length,
        referral: insights.filter(i => i.trafficSource === 'referral').length
      },
      topCountries: this.getTopCountries(contexts),
      averageEngagement: this.calculateAverageEngagement(contexts, insights)
    }
  }

  private getTopCountries(contexts: VisitorContext[]): Array<{country: string, count: number}> {
    const countryCounts = contexts.reduce((acc, context) => {
      const country = context.location?.country || 'Unknown'
      acc[country] = (acc[country] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(countryCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([country, count]) => ({ country, count }))
  }

  private calculateAverageEngagement(
    contexts: VisitorContext[], 
    insights: ReturnType<typeof generateVisitorInsights>[]
  ): number {
    const scores = contexts.map((context, index) => 
      calculateEngagementScore(context, insights[index])
    )
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
  }
}

/**
 * Example: Privacy compliance audit
 */
export async function auditPrivacyComplianceExample() {
  try {
    // This would typically fetch from your database
    const mockRecords = [
      { timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) }, // 5 days ago
      { timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), anonymized: true }, // 10 days ago
      { timestamp: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000) } // 100 days ago
    ]

    const { auditPrivacyCompliance } = await import('./privacy-utils')
    const report = auditPrivacyCompliance(mockRecords)

    console.log('Privacy Compliance Audit:', {
      totalRecords: report.totalRecords,
      anonymizedRecords: report.anonymizedRecords,
      recordsToDelete: report.deletedRecords,
      compliant: report.retentionCompliance,
      oldestRecord: report.oldestRecord?.toISOString(),
      newestRecord: report.newestRecord?.toISOString()
    })

    return report

  } catch (error) {
    console.error('Error in privacy audit:', error)
    throw new Error('Failed to audit privacy compliance')
  }
}

/**
 * Example: Integration with Next.js API route
 */
export async function handleAnalyticsRequest(request: NextRequest) {
  try {
    const { profileId, consent } = await request.json()

    if (!profileId) {
      return Response.json(
        { error: 'Profile ID is required' },
        { status: 400 }
      )
    }

    // Default consent (you would get this from user preferences)
    const defaultConsent: ConsentConfig = {
      analytics: true,
      geolocation: true,
      fingerprinting: true,
      advertising: false
    }

    const userConsent = consent || defaultConsent

    // Track with consent
    const result = await trackWithConsentExample(
      request,
      profileId,
      userConsent
    )

    return Response.json(result)

  } catch (error) {
    console.error('Error in analytics request:', error)
    return Response.json(
      { error: 'Failed to process analytics' },
      { status: 500 }
    )
  }
}

// Export the visitor monitor instance for use across the application
export const globalVisitorMonitor = new VisitorMonitor()