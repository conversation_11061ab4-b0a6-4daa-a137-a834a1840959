"use client"

// Performance monitoring utilities
export class PerformanceOptimizer {
  private static metrics: Map<string, number> = new Map()

  /**
   * Mark the start of a performance measurement
   */
  static markStart(name: string): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${name}-start`)
      this.metrics.set(`${name}-start`, performance.now())
    }
  }

  /**
   * Mark the end of a performance measurement and calculate duration
   */
  static markEnd(name: string): number | null {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const endTime = performance.now()
      performance.mark(`${name}-end`)
      
      const startTime = this.metrics.get(`${name}-start`)
      if (startTime) {
        const duration = endTime - startTime
        performance.measure(name, `${name}-start`, `${name}-end`)
        
        // Log slow operations in development
        if (process.env.NODE_ENV === 'development' && duration > 100) {
          console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`)
        }
        
        return duration
      }
    }
    return null
  }

  /**
   * Measure the performance of an async function
   */
  static async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.markStart(name)
    try {
      const result = await fn()
      return result
    } finally {
      this.markEnd(name)
    }
  }

  /**
   * Measure the performance of a synchronous function
   */
  static measure<T>(name: string, fn: () => T): T {
    this.markStart(name)
    try {
      const result = fn()
      return result
    } finally {
      this.markEnd(name)
    }
  }

  /**
   * Get Core Web Vitals
   */
  static getCoreWebVitals(): Promise<{
    lcp?: number
    fid?: number
    cls?: number
    fcp?: number
    ttfb?: number
  }> {
    return new Promise((resolve) => {
      if (typeof window === 'undefined' || !('performance' in window)) {
        resolve({})
        return
      }

      const vitals: any = {}

      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry
      if (fcpEntry) {
        vitals.fcp = fcpEntry.startTime
      }

      // Time to First Byte
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        vitals.ttfb = navigationEntry.responseStart - navigationEntry.requestStart
      }

      // Use web-vitals library if available
      if ('webVitals' in window) {
        // This would require the web-vitals library to be installed
        resolve(vitals)
      } else {
        resolve(vitals)
      }
    })
  }

  /**
   * Preload critical resources
   */
  static preloadResource(href: string, as: string, type?: string): void {
    if (typeof document === 'undefined') return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    
    document.head.appendChild(link)
  }

  /**
   * Lazy load images with intersection observer
   */
  static lazyLoadImages(selector: string = 'img[data-src]'): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return

    const images = document.querySelectorAll(selector)
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
            observer.unobserve(img)
          }
        }
      })
    })

    images.forEach(img => imageObserver.observe(img))
  }

  /**
   * Debounce function calls
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  }

  /**
   * Throttle function calls
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }
}