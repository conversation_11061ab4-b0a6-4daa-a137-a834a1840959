/**
 * Comprehensive logging utility for analytics functionality
 * Provides structured logging with different levels and contexts
 */

import { AnalyticsError, isAnalyticsError } from '../errors/analytics-errors'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogContext {
  userId?: string
  profileId?: string
  operation?: string
  duration?: number
  metadata?: Record<string, unknown>
  // Additional context fields for comprehensive logging
  [key: string]: unknown
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: LogContext
  error?: {
    name: string
    message: string
    code?: string
    stack?: string
    details?: unknown
  }
}

class AnalyticsLogger {
  private static instance: AnalyticsLogger
  private logLevel: LogLevel = LogLevel.INFO

  private constructor() {
    // Set log level based on environment
    const envLogLevel = process.env.ANALYTICS_LOG_LEVEL?.toLowerCase()
    switch (envLogLevel) {
      case 'debug':
        this.logLevel = LogLevel.DEBUG
        break
      case 'info':
        this.logLevel = LogLevel.INFO
        break
      case 'warn':
        this.logLevel = LogLevel.WARN
        break
      case 'error':
        this.logLevel = LogLevel.ERROR
        break
      default:
        this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO
    }
  }

  static getInstance(): AnalyticsLogger {
    if (!AnalyticsLogger.instance) {
      AnalyticsLogger.instance = new AnalyticsLogger()
    }
    return AnalyticsLogger.instance
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  private formatLogEntry(level: LogLevel, message: string, context?: LogContext, error?: Error | AnalyticsError): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context
    }

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      }

      if (isAnalyticsError(error)) {
        entry.error.code = error.code
        entry.error.details = error.details
      }
    }

    return entry
  }

  private writeLog(entry: LogEntry): void {
    const logMessage = this.formatLogMessage(entry)
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage)
        break
      case LogLevel.INFO:
        console.info(logMessage)
        break
      case LogLevel.WARN:
        console.warn(logMessage)
        break
      case LogLevel.ERROR:
        console.error(logMessage)
        break
    }
  }

  private formatLogMessage(entry: LogEntry): string {
    const levelName = LogLevel[entry.level]
    let message = `[${entry.timestamp}] [ANALYTICS] [${levelName}] ${entry.message}`

    if (entry.context) {
      const contextStr = Object.entries(entry.context)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
        .join(' ')
      
      if (contextStr) {
        message += ` | Context: ${contextStr}`
      }
    }

    if (entry.error) {
      message += ` | Error: ${entry.error.name}: ${entry.error.message}`
      if (entry.error.code) {
        message += ` (Code: ${entry.error.code})`
      }
      if (entry.error.details) {
        message += ` | Details: ${JSON.stringify(entry.error.details)}`
      }
    }

    return message
  }

  debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const entry = this.formatLogEntry(LogLevel.DEBUG, message, context)
      this.writeLog(entry)
    }
  }

  info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const entry = this.formatLogEntry(LogLevel.INFO, message, context)
      this.writeLog(entry)
    }
  }

  warn(message: string, context?: LogContext, error?: Error | AnalyticsError): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const entry = this.formatLogEntry(LogLevel.WARN, message, context, error)
      this.writeLog(entry)
    }
  }

  error(message: string, context?: LogContext, error?: Error | AnalyticsError): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const entry = this.formatLogEntry(LogLevel.ERROR, message, context, error)
      this.writeLog(entry)
    }
  }

  /**
   * Log the start of an operation
   */
  operationStart(operation: string, context?: LogContext): void {
    this.info(`Starting operation: ${operation}`, {
      ...context,
      operation,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Log the completion of an operation
   */
  operationComplete(operation: string, duration: number, context?: LogContext): void {
    this.info(`Completed operation: ${operation}`, {
      ...context,
      operation,
      duration
    })
  }

  /**
   * Log an operation failure
   */
  operationFailed(operation: string, error: Error | AnalyticsError, context?: LogContext): void {
    this.error(`Failed operation: ${operation}`, {
      ...context,
      operation
    }, error)
  }

  /**
   * Log data validation issues
   */
  validationWarning(field: string, value: unknown, expectedType: string, context?: LogContext): void {
    this.warn(`Data validation warning for field: ${field}`, {
      ...context,
      field,
      providedValue: value,
      expectedType
    })
  }

  /**
   * Log performance metrics
   */
  performance(operation: string, duration: number, context?: LogContext): void {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.INFO
    const message = `Performance: ${operation} took ${duration}ms`
    
    if (level === LogLevel.WARN) {
      this.warn(message, { ...context, operation, duration })
    } else {
      this.info(message, { ...context, operation, duration })
    }
  }

  /**
   * Log database query information
   */
  queryInfo(query: string, duration: number, resultCount?: number, context?: LogContext): void {
    this.debug(`Database query executed`, {
      ...context,
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      duration,
      resultCount
    })
  }

  /**
   * Log API request information
   */
  apiRequest(method: string, path: string, userId?: string, context?: LogContext): void {
    this.info(`API request: ${method} ${path}`, {
      ...context,
      method,
      path,
      userId
    })
  }

  /**
   * Log API response information
   */
  apiResponse(method: string, path: string, statusCode: number, duration: number, context?: LogContext): void {
    const level = statusCode >= 400 ? LogLevel.ERROR : LogLevel.INFO
    const message = `API response: ${method} ${path} - ${statusCode}`
    
    if (level === LogLevel.ERROR) {
      this.error(message, { ...context, method, path, statusCode, duration })
    } else {
      this.info(message, { ...context, method, path, statusCode, duration })
    }
  }
}

// Export singleton instance
export const analyticsLogger = AnalyticsLogger.getInstance()

/**
 * Decorator for timing operations
 */
export function logOperation(operationName: string, context?: LogContext) {
  return function (target: unknown, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: unknown[]) {
      const startTime = Date.now()
      analyticsLogger.operationStart(operationName, context)

      try {
        const result = await method.apply(this, args)
        const duration = Date.now() - startTime
        analyticsLogger.operationComplete(operationName, duration, context)
        return result
      } catch (error) {
        const duration = Date.now() - startTime
        analyticsLogger.operationFailed(operationName, error as Error, {
          ...context,
          duration
        })
        throw error
      }
    }

    return descriptor
  }
}

/**
 * Utility function to create a performance timer
 */
export function createTimer(operation: string, context?: LogContext) {
  const startTime = Date.now()
  
  return {
    end: () => {
      const duration = Date.now() - startTime
      analyticsLogger.performance(operation, duration, context)
      return duration
    }
  }
}