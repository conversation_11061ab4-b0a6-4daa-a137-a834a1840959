import { UAParser } from 'ua-parser-js'
import { createHash } from 'crypto'

/**
 * Device information extracted from user agent
 */
export interface DeviceInfo {
  browser: {
    name: string
    version: string
  }
  os: {
    name: string
    version: string
  }
  device: {
    type: string
    vendor: string
    model: string
  }
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}

/**
 * Location information from Vercel geo headers
 */
export interface LocationInfo {
  country: string
  region: string
  city: string
  latitude: string
  longitude: string
  timezone: string
}

/**
 * Referrer information with sanitization
 */
export interface ReferrerInfo {
  domain: string
  source: string
  medium: string
  campaign: string | null
  isInternal: boolean
  isSocial: boolean
  isSearch: boolean
}

/**
 * Complete visitor context
 */
export interface VisitorContext {
  device: DeviceInfo
  location: LocationInfo | null
  referrer: ReferrerInfo | null
  timezone: string
  ipHash: string | null
  timestamp: Date
}

/**
 * Parse user agent string to extract device information
 */
export function parseUserAgent(userAgent: string): DeviceInfo {
  try {
    const parser = new UAParser(userAgent)
    const result = parser.getResult()

    const deviceType = result.device.type || 'desktop'
    const isMobile = deviceType === 'mobile'
    const isTablet = deviceType === 'tablet'
    const isDesktop = !isMobile && !isTablet

    return {
      browser: {
        name: result.browser.name || 'Unknown',
        version: result.browser.version || 'Unknown'
      },
      os: {
        name: result.os.name || 'Unknown',
        version: result.os.version || 'Unknown'
      },
      device: {
        type: deviceType,
        vendor: result.device.vendor || 'Unknown',
        model: result.device.model || 'Unknown'
      },
      isMobile,
      isTablet,
      isDesktop
    }
  } catch (error) {
    // Return safe defaults on parsing error
    return {
      browser: { name: 'Unknown', version: 'Unknown' },
      os: { name: 'Unknown', version: 'Unknown' },
      device: { type: 'desktop', vendor: 'Unknown', model: 'Unknown' },
      isMobile: false,
      isTablet: false,
      isDesktop: true
    }
  }
}

/**
 * Extract location information from Vercel geo headers
 */
export function parseLocationFromHeaders(headers: Headers): LocationInfo | null {
  try {
    const country = headers.get('x-vercel-ip-country') || ''
    const region = headers.get('x-vercel-ip-country-region') || ''
    const city = headers.get('x-vercel-ip-city') || ''
    const latitude = headers.get('x-vercel-ip-latitude') || ''
    const longitude = headers.get('x-vercel-ip-longitude') || ''
    const timezone = headers.get('x-vercel-ip-timezone') || ''

    // Return null if no location data is available
    if (!country && !region && !city) {
      return null
    }

    return {
      country: sanitizeLocationValue(country),
      region: sanitizeLocationValue(region),
      city: sanitizeLocationValue(city),
      latitude: sanitizeCoordinate(latitude),
      longitude: sanitizeCoordinate(longitude),
      timezone: sanitizeTimezone(timezone)
    }
  } catch (error) {
    return null
  }
}

/**
 * Parse and sanitize referrer information
 */
export function parseReferrer(referrer: string, currentDomain: string): ReferrerInfo | null {
  if (!referrer || referrer === '') {
    return null
  }

  try {
    const url = new URL(referrer)
    const domain = url.hostname.toLowerCase()
    const isInternal = domain === currentDomain.toLowerCase()
    
    // Extract UTM parameters
    const campaign = url.searchParams.get('utm_campaign')
    const source = url.searchParams.get('utm_source') || extractSourceFromDomain(domain)
    const medium = url.searchParams.get('utm_medium') || extractMediumFromDomain(domain)

    return {
      domain: sanitizeDomain(domain),
      source: sanitizeUtmValue(source),
      medium: sanitizeUtmValue(medium),
      campaign: campaign ? sanitizeUtmValue(campaign) : null,
      isInternal,
      isSocial: isSocialDomain(domain),
      isSearch: isSearchDomain(domain)
    }
  } catch (error) {
    // If URL parsing fails, try to extract domain from string
    const domainMatch = referrer.match(/https?:\/\/([^\/]+)/)
    if (domainMatch) {
      const domain = domainMatch[1].toLowerCase()
      return {
        domain: sanitizeDomain(domain),
        source: extractSourceFromDomain(domain),
        medium: 'referral',
        campaign: null,
        isInternal: domain === currentDomain.toLowerCase(),
        isSocial: isSocialDomain(domain),
        isSearch: isSearchDomain(domain)
      }
    }
    
    return null
  }
}

/**
 * Detect timezone from various sources
 */
export function detectTimezone(
  locationTimezone?: string,
  acceptLanguage?: string
): string {
  // Priority order: location timezone > browser timezone > UTC fallback
  
  if (locationTimezone && isValidTimezone(locationTimezone)) {
    return locationTimezone
  }

  // Try to extract timezone from Accept-Language header
  if (acceptLanguage) {
    const timezoneFromLang = extractTimezoneFromLanguage(acceptLanguage)
    if (timezoneFromLang && isValidTimezone(timezoneFromLang)) {
      return timezoneFromLang
    }
  }

  // Fallback to UTC
  return 'UTC'
}

/**
 * Create privacy-safe IP hash
 */
export function hashIP(ip: string, salt?: string): string {
  if (!ip) return ''
  
  try {
    // Use a salt for additional security
    const saltValue = salt || process.env.IP_HASH_SALT || 'default-salt'
    const hash = createHash('sha256')
    hash.update(ip + saltValue)
    return hash.digest('hex')
  } catch (error) {
    return ''
  }
}

/**
 * Extract IP address from request headers (Vercel/Next.js)
 */
export function extractIPAddress(headers: Headers): string | null {
  // Check various headers in order of preference
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-vercel-forwarded-for',
    'cf-connecting-ip', // Cloudflare
    'x-client-ip'
  ]

  for (const header of ipHeaders) {
    const value = headers.get(header)
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim()
      if (isValidIP(ip)) {
        return ip
      }
    }
  }

  return null
}

/**
 * Create complete visitor context from request
 */
export function createVisitorContext(
  userAgent: string,
  headers: Headers,
  referrer?: string,
  currentDomain: string = 'localhost'
): VisitorContext {
  const ip = extractIPAddress(headers)
  const location = parseLocationFromHeaders(headers)
  const device = parseUserAgent(userAgent)
  const referrerInfo = referrer ? parseReferrer(referrer, currentDomain) : null
  const timezone = detectTimezone(
    location?.timezone,
    headers.get('accept-language') || undefined
  )

  return {
    device,
    location,
    referrer: referrerInfo,
    timezone,
    ipHash: ip ? hashIP(ip) : null,
    timestamp: new Date()
  }
}

// Helper functions

function sanitizeLocationValue(value: string): string {
  if (!value) return ''
  // Remove any potentially harmful characters and limit length
  return value.replace(/[<>\"'&]/g, '').substring(0, 100)
}

function sanitizeCoordinate(coord: string): string {
  if (!coord) return ''
  // Validate coordinate format
  const num = parseFloat(coord)
  if (isNaN(num) || num < -180 || num > 180) return ''
  return coord
}

function sanitizeTimezone(timezone: string): string {
  if (!timezone) return ''
  // Basic timezone validation
  if (!/^[A-Za-z_\/]+$/.test(timezone)) return ''
  return timezone.substring(0, 50)
}

function sanitizeDomain(domain: string): string {
  if (!domain) return ''
  // Remove protocol and path, keep only domain
  return domain.replace(/^https?:\/\//, '').split('/')[0].substring(0, 100)
}

function sanitizeUtmValue(value: string): string {
  if (!value) return ''
  // Remove potentially harmful characters and limit length
  return value.replace(/[<>\"'&]/g, '').substring(0, 100)
}

function extractSourceFromDomain(domain: string): string {
  // Map common domains to sources
  const sourceMap: Record<string, string> = {
    'google.com': 'google',
    'google.co.uk': 'google',
    'google.ca': 'google',
    'bing.com': 'bing',
    'yahoo.com': 'yahoo',
    'duckduckgo.com': 'duckduckgo',
    'facebook.com': 'facebook',
    'twitter.com': 'twitter',
    'x.com': 'twitter',
    'linkedin.com': 'linkedin',
    'instagram.com': 'instagram',
    'tiktok.com': 'tiktok',
    'youtube.com': 'youtube',
    'reddit.com': 'reddit',
    'pinterest.com': 'pinterest'
  }

  // Check for exact matches first
  if (sourceMap[domain]) {
    return sourceMap[domain]
  }

  // Check for subdomain matches
  for (const [key, value] of Object.entries(sourceMap)) {
    if (domain.endsWith(key)) {
      return value
    }
  }

  // Return domain as source if no match found
  return domain
}

function extractMediumFromDomain(domain: string): string {
  if (isSearchDomain(domain)) return 'search'
  if (isSocialDomain(domain)) return 'social'
  return 'referral'
}

function isSocialDomain(domain: string): boolean {
  const socialDomains = [
    'facebook.com', 'twitter.com', 'x.com', 'linkedin.com',
    'instagram.com', 'tiktok.com', 'youtube.com', 'reddit.com',
    'pinterest.com', 'snapchat.com', 'discord.com', 'telegram.org'
  ]
  
  return socialDomains.some(social => 
    domain === social || domain.endsWith('.' + social)
  )
}

function isSearchDomain(domain: string): boolean {
  const searchDomains = [
    'google.com', 'google.co.uk', 'google.ca', 'google.de',
    'bing.com', 'yahoo.com', 'duckduckgo.com', 'baidu.com',
    'yandex.com', 'ask.com'
  ]
  
  return searchDomains.some(search => 
    domain === search || domain.endsWith('.' + search)
  )
}

function isValidTimezone(timezone: string): boolean {
  try {
    // Try to create a date with the timezone to validate it
    new Intl.DateTimeFormat('en', { timeZone: timezone })
    return true
  } catch {
    return false
  }
}

function extractTimezoneFromLanguage(acceptLanguage: string): string | null {
  // This is a simplified approach - in practice, you might want a more
  // comprehensive mapping of locales to timezones
  const localeTimezoneMap: Record<string, string> = {
    'en-US': 'America/New_York',
    'en-GB': 'Europe/London',
    'en-CA': 'America/Toronto',
    'en-AU': 'Australia/Sydney',
    'de-DE': 'Europe/Berlin',
    'fr-FR': 'Europe/Paris',
    'es-ES': 'Europe/Madrid',
    'it-IT': 'Europe/Rome',
    'ja-JP': 'Asia/Tokyo',
    'ko-KR': 'Asia/Seoul',
    'zh-CN': 'Asia/Shanghai',
    'pt-BR': 'America/Sao_Paulo'
  }

  // Parse the Accept-Language header
  const languages = acceptLanguage.split(',').map(lang => {
    const [locale] = lang.trim().split(';')
    return locale.trim()
  })

  // Find the first matching timezone
  for (const locale of languages) {
    if (localeTimezoneMap[locale]) {
      return localeTimezoneMap[locale]
    }
  }

  return null
}

function isValidIP(ip: string): boolean {
  // Basic IP validation (IPv4 and IPv6)
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  
  if (ipv4Regex.test(ip)) {
    // Validate IPv4 octets
    const octets = ip.split('.')
    return octets.every(octet => {
      const num = parseInt(octet, 10)
      return num >= 0 && num <= 255
    })
  }
  
  return ipv6Regex.test(ip)
}