import { PrismaClient, Prisma } from '@prisma/client'

// Custom error classes for better error handling
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode: number = 500
  ) {
    super(message)
    this.name = 'DatabaseError'
  }
}

export class NotFoundError extends DatabaseError {
  constructor(resource: string, identifier?: string) {
    const message = identifier 
      ? `${resource} with identifier '${identifier}' not found`
      : `${resource} not found`
    super(message, 'NOT_FOUND', 404)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends DatabaseError {
  constructor(message: string, field?: string) {
    super(message, 'CONFLICT', 409)
    this.name = 'ConflictError'
  }
}

export class ValidationError extends DatabaseError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400)
    this.name = 'ValidationError'
  }
}

// Database connection utilities with optimized connection pooling
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const db = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db

// Enhanced database utility functions with retry logic
export async function handleDatabaseError(error: unknown): Promise<never> {
  console.error('Database error:', error)

  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        // Unique constraint violation
        const field = error.meta?.target as string[] | undefined
        const fieldName = field?.[0] || 'field'
        throw new ConflictError(`A record with this ${fieldName} already exists`, fieldName)
      
      case 'P2025':
        // Record not found
        throw new NotFoundError('Record')
      
      case 'P2003':
        // Foreign key constraint violation
        throw new ValidationError('Invalid reference to related record')
      
      case 'P2014':
        // Required relation violation
        throw new ValidationError('Required relation is missing')
      
      case 'P2024':
        // Connection timeout
        throw new DatabaseError('Database connection timeout', 'CONNECTION_TIMEOUT', 503)
      
      case 'P1001':
        // Can't reach database server
        throw new DatabaseError('Cannot reach database server', 'CONNECTION_ERROR', 503)
      
      case 'P1002':
        // Database server timeout
        throw new DatabaseError('Database server timeout', 'SERVER_TIMEOUT', 503)
      
      case 'P1008':
        // Operations timed out
        throw new DatabaseError('Database operations timed out', 'OPERATION_TIMEOUT', 503)
      
      case 'P1017':
        // Server has closed the connection
        throw new DatabaseError('Database connection closed', 'CONNECTION_CLOSED', 503)
      
      default:
        throw new DatabaseError(`Database operation failed: ${error.message}`, error.code)
    }
  }

  if (error instanceof Prisma.PrismaClientValidationError) {
    throw new ValidationError('Invalid data provided to database operation')
  }

  if (error instanceof Prisma.PrismaClientInitializationError) {
    throw new DatabaseError('Failed to initialize database connection', 'CONNECTION_ERROR', 503)
  }

  if (error instanceof Prisma.PrismaClientRustPanicError) {
    throw new DatabaseError('Database engine panic occurred', 'ENGINE_PANIC', 500)
  }

  // Re-throw custom errors
  if (error instanceof DatabaseError) {
    throw error
  }

  // Unknown error
  throw new DatabaseError('An unexpected database error occurred')
}

// Enhanced database operation with retry logic
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  operationName?: string
): Promise<T> {
  let lastError: unknown
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      
      // Don't retry on validation errors or business logic errors
      if (error instanceof ValidationError || 
          error instanceof ConflictError || 
          error instanceof NotFoundError) {
        throw error
      }
      
      // Check if error is retryable
      if (!isRetryableError(error) || attempt === maxRetries) {
        break
      }
      
      // Calculate delay with exponential backoff and jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000
      
      console.warn(`Database operation ${operationName || 'unknown'} failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`, error)
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  // All retries exhausted, throw the last error
  throw lastError
}

// Check if database error is retryable
function isRetryableError(error: unknown): boolean {
  if (error instanceof DatabaseError) {
    return error.code === 'CONNECTION_TIMEOUT' ||
           error.code === 'CONNECTION_ERROR' ||
           error.code === 'SERVER_TIMEOUT' ||
           error.code === 'OPERATION_TIMEOUT' ||
           error.code === 'CONNECTION_CLOSED'
  }
  
  if (error instanceof Error) {
    const message = error.message.toLowerCase()
    return message.includes('connection') ||
           message.includes('timeout') ||
           message.includes('network') ||
           message.includes('econnreset') ||
           message.includes('enotfound') ||
           message.includes('etimedout')
  }
  
  return false
}

// Enhanced connection health check with detailed diagnostics
export async function checkDatabaseConnection(): Promise<{
  isHealthy: boolean
  latency?: number
  error?: string
}> {
  const startTime = Date.now()
  
  try {
    await db.$queryRaw`SELECT 1`
    const latency = Date.now() - startTime
    
    return {
      isHealthy: true,
      latency
    }
  } catch (error) {
    const latency = Date.now() - startTime
    console.error('Database connection check failed:', error)
    
    return {
      isHealthy: false,
      latency,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// Connection pool monitoring
export async function getConnectionPoolStatus(): Promise<{
  activeConnections: number
  idleConnections: number
  totalConnections: number
}> {
  try {
    // This is a simplified version - actual implementation would depend on database type
    const result = await db.$queryRaw<Array<{ count: number }>>`
      SELECT COUNT(*) as count FROM pg_stat_activity WHERE state = 'active'
    `
    
    return {
      activeConnections: result[0]?.count || 0,
      idleConnections: 0, // Would need additional query for idle connections
      totalConnections: result[0]?.count || 0
    }
  } catch (error) {
    console.error('Failed to get connection pool status:', error)
    return {
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0
    }
  }
}

// Optimized batch operations for analytics
export async function executeBatchQueries<T>(
  queries: Array<() => Promise<T>>,
  batchSize: number = 5
): Promise<T[]> {
  const results: T[] = []
  
  // Process queries in batches to avoid overwhelming the connection pool
  for (let i = 0; i < queries.length; i += batchSize) {
    const batch = queries.slice(i, i + batchSize)
    const batchResults = await Promise.all(
      batch.map(query => executeWithRetry(query, 2, 500, `batch-query-${i}`))
    )
    results.push(...batchResults)
  }
  
  return results
}

// Transaction wrapper with error handling
export async function withTransaction<T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  try {
    return await db.$transaction(operation)
  } catch (error) {
    return handleDatabaseError(error)
  }
}

// Graceful shutdown
export async function disconnectDatabase(): Promise<void> {
  try {
    await db.$disconnect()
  } catch (error) {
    console.error('Error disconnecting from database:', error)
  }
}

// Database seeding utilities
export async function seedDatabase(): Promise<void> {
  try {
    // This will be implemented when seeding is needed
    console.log('Database seeding not implemented yet')
  } catch (error) {
    console.error('Database seeding failed:', error)
    throw error
  }
}

export { db as prisma } // Backward compatibility