/**
 * Username validation and availability error constants
 * Provides consistent error messaging across the application
 */

export const USERNAME_ERROR_CODES = {
  // Format validation errors
  TOO_SHORT: 'TOO_SHORT',
  TOO_LONG: 'TOO_LONG',
  INVALID_CHARACTERS: 'INVALID_CHARACTERS',
  STARTS_WITH_SPECIAL: 'STARTS_WITH_SPECIAL',
  ENDS_WITH_SPECIAL: 'ENDS_WITH_SPECIAL',
  CONSECUTIVE_SPECIAL: 'CONSECUTIVE_SPECIAL',
  
  // Availability errors
  USERNAME_TAKEN: 'USERNAME_TAKEN',
  
  // Network and system errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

export type UsernameErrorCode = typeof USERNAME_ERROR_CODES[keyof typeof USERNAME_ERROR_CODES]

export const USERNAME_ERROR_MESSAGES: Record<UsernameErrorCode, string> = {
  // Format validation errors
  [USERNAME_ERROR_CODES.TOO_SHORT]: 'Username must be at least 3 characters long',
  [USERNAME_ERROR_CODES.TOO_LONG]: 'Username must be less than 30 characters long',
  [USERNAME_ERROR_CODES.INVALID_CHARACTERS]: 'Username can only contain letters, numbers, hyphens, and underscores',
  [USERNAME_ERROR_CODES.STARTS_WITH_SPECIAL]: 'Username cannot start with special characters',
  [USERNAME_ERROR_CODES.ENDS_WITH_SPECIAL]: 'Username cannot end with special characters',
  [USERNAME_ERROR_CODES.CONSECUTIVE_SPECIAL]: 'Username cannot contain consecutive special characters',
  
  // Availability errors
  [USERNAME_ERROR_CODES.USERNAME_TAKEN]: 'This username is already taken',
  
  // Network and system errors
  [USERNAME_ERROR_CODES.NETWORK_ERROR]: 'Network error. Please check your connection and try again.',
  [USERNAME_ERROR_CODES.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
  [USERNAME_ERROR_CODES.SERVER_ERROR]: 'Server error. Please try again later.',
  [USERNAME_ERROR_CODES.RATE_LIMITED]: 'Too many requests. Please wait a moment and try again.',
  [USERNAME_ERROR_CODES.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.'
}

export const USERNAME_SUCCESS_MESSAGES = {
  AVAILABLE: 'Username is available',
  CHECKING: 'Checking availability...',
  VALID_FORMAT: 'Username format is valid'
} as const

export const USERNAME_STATUS_MESSAGES = {
  IDLE: '',
  CHECKING: USERNAME_SUCCESS_MESSAGES.CHECKING,
  AVAILABLE: USERNAME_SUCCESS_MESSAGES.AVAILABLE,
  ERROR: 'Error occurred',
  CACHED: 'Username is available (cached)'
} as const

/**
 * Maps validation error messages to error codes for consistent handling
 */
export function getErrorCodeFromMessage(message: string): UsernameErrorCode {
  // Check for specific validation patterns
  if (message.includes('at least 3 characters')) {
    return USERNAME_ERROR_CODES.TOO_SHORT
  }
  if (message.includes('less than 30 characters')) {
    return USERNAME_ERROR_CODES.TOO_LONG
  }
  if (message.includes('only contain letters, numbers')) {
    return USERNAME_ERROR_CODES.INVALID_CHARACTERS
  }
  if (message.includes('cannot start with')) {
    return USERNAME_ERROR_CODES.STARTS_WITH_SPECIAL
  }
  if (message.includes('cannot end with')) {
    return USERNAME_ERROR_CODES.ENDS_WITH_SPECIAL
  }
  if (message.includes('consecutive special')) {
    return USERNAME_ERROR_CODES.CONSECUTIVE_SPECIAL
  }
  if (message.includes('already taken') || message.includes('not available')) {
    return USERNAME_ERROR_CODES.USERNAME_TAKEN
  }
  if (message.includes('timed out')) {
    return USERNAME_ERROR_CODES.TIMEOUT_ERROR
  }
  if (message.includes('network') || message.includes('Network')) {
    return USERNAME_ERROR_CODES.NETWORK_ERROR
  }
  if (message.includes('rate limit') || message.includes('too many')) {
    return USERNAME_ERROR_CODES.RATE_LIMITED
  }
  if (message.includes('server') || message.includes('Server')) {
    return USERNAME_ERROR_CODES.SERVER_ERROR
  }
  
  return USERNAME_ERROR_CODES.UNKNOWN_ERROR
}

/**
 * Gets user-friendly error message from error code
 */
export function getUserFriendlyMessage(errorCode: UsernameErrorCode): string {
  return USERNAME_ERROR_MESSAGES[errorCode] || USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.UNKNOWN_ERROR]
}

/**
 * Determines if an error is retryable
 */
export function isRetryableError(errorCode: UsernameErrorCode): boolean {
  return [
    USERNAME_ERROR_CODES.NETWORK_ERROR,
    USERNAME_ERROR_CODES.TIMEOUT_ERROR,
    USERNAME_ERROR_CODES.SERVER_ERROR,
    USERNAME_ERROR_CODES.UNKNOWN_ERROR
  ].includes(errorCode)
}

/**
 * Determines if an error should allow graceful degradation (form submission)
 */
export function allowsGracefulDegradation(errorCode: UsernameErrorCode): boolean {
  return [
    USERNAME_ERROR_CODES.NETWORK_ERROR,
    USERNAME_ERROR_CODES.TIMEOUT_ERROR,
    USERNAME_ERROR_CODES.SERVER_ERROR,
    USERNAME_ERROR_CODES.UNKNOWN_ERROR
  ].includes(errorCode)
}