# Username Error Handling System

This directory contains the comprehensive error handling system for username validation and availability checking.

## Overview

The error handling system provides consistent, user-friendly error messages and feedback across the application. It includes support for retry functionality, graceful degradation, and accessibility features.

## Components

### Error Constants (`username-errors.ts`)

Centralized error codes and messages for:
- **Format validation errors**: Too short, too long, invalid characters, etc.
- **Availability errors**: Username already taken
- **Network errors**: Timeout, network failure, server errors, rate limiting

### Error Display Components

#### `UsernameErrorDisplay`
Full-featured error display component with:
- Retry buttons for retryable errors
- Graceful degradation messages
- Progress tracking (retry count)
- Accessibility support

#### `InlineUsernameError`
Simplified inline error display for compact layouts.

### Success Display Components

#### `UsernameSuccessDisplay`
Success message display with different states:
- Available (with check icon)
- Checking (with loading animation)
- Cached (with lightning icon)

#### `UsernameSuccessIcon`
Icon-only success indicators for compact layouts.

## Features

### Error Classification
- **Retryable errors**: Network, timeout, server errors
- **Non-retryable errors**: Format validation, username taken
- **Graceful degradation**: Allows form submission for network issues

### User Experience
- **Consistent messaging**: All error messages use the same constants
- **Visual feedback**: Color-coded styling (red for errors, amber for warnings, green for success)
- **Retry functionality**: Automatic retry with exponential backoff
- **Progress indication**: Shows retry attempts and loading states

### Accessibility
- **ARIA attributes**: Proper roles, labels, and live regions
- **Screen reader support**: Descriptive error messages and status updates
- **Keyboard navigation**: Retry buttons are keyboard accessible

## Usage

```typescript
import { 
  USERNAME_ERROR_CODES, 
  getUserFriendlyMessage,
  isRetryableError,
  allowsGracefulDegradation 
} from '@/lib/constants/username-errors'

import { UsernameErrorDisplay } from '@/components/ui/username-error-display'
import { UsernameSuccessDisplay } from '@/components/ui/username-success-display'

// Check if error allows retry
const canRetry = isRetryableError(errorCode)

// Check if form submission should be allowed
const allowSubmission = allowsGracefulDegradation(errorCode)

// Display error with retry functionality
<UsernameErrorDisplay
  errorCode={errorCode}
  onRetry={handleRetry}
  showRetryButton={true}
  showGracefulDegradation={true}
/>
```

## Integration

The error handling system is integrated into:
- `UsernameInput` component
- `useUsernameAvailability` hook
- Signup and settings forms
- API error responses

## Testing

Comprehensive test coverage includes:
- Error code mapping
- Message consistency
- Retry functionality
- Accessibility compliance
- Component integration