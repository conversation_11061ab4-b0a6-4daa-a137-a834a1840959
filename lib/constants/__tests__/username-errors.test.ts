import {
  USERNAME_ERROR_CODES,
  USERNAME_ERROR_MESSAGES,
  USERNAME_SUCCESS_MESSAGES,
  getErrorCodeFromMessage,
  getUserFriendlyMessage,
  isRetryableError,
  allowsGracefulDegradation
} from '../username-errors'

describe('Username Error Constants', () => {
  describe('USERNAME_ERROR_CODES', () => {
    it('should have all required error codes', () => {
      expect(USERNAME_ERROR_CODES.TOO_SHORT).toBe('TOO_SHORT')
      expect(USERNAME_ERROR_CODES.TOO_LONG).toBe('TOO_LONG')
      expect(USERNAME_ERROR_CODES.INVALID_CHARACTERS).toBe('INVALID_CHARACTERS')
      expect(USERNAME_ERROR_CODES.STARTS_WITH_SPECIAL).toBe('STARTS_WITH_SPECIAL')
      expect(USERNAME_ERROR_CODES.ENDS_WITH_SPECIAL).toBe('ENDS_WITH_SPECIAL')
      expect(USERNAME_ERROR_CODES.CONSECUTIVE_SPECIAL).toBe('CONSECUTIVE_SPECIAL')
      expect(USERNAME_ERROR_CODES.USERNAME_TAKEN).toBe('USERNAME_TAKEN')
      expect(USERNAME_ERROR_CODES.NETWORK_ERROR).toBe('NETWORK_ERROR')
      expect(USERNAME_ERROR_CODES.TIMEOUT_ERROR).toBe('TIMEOUT_ERROR')
      expect(USERNAME_ERROR_CODES.SERVER_ERROR).toBe('SERVER_ERROR')
      expect(USERNAME_ERROR_CODES.RATE_LIMITED).toBe('RATE_LIMITED')
      expect(USERNAME_ERROR_CODES.UNKNOWN_ERROR).toBe('UNKNOWN_ERROR')
    })
  })

  describe('USERNAME_ERROR_MESSAGES', () => {
    it('should have messages for all error codes', () => {
      Object.values(USERNAME_ERROR_CODES).forEach(code => {
        expect(USERNAME_ERROR_MESSAGES[code]).toBeDefined()
        expect(typeof USERNAME_ERROR_MESSAGES[code]).toBe('string')
        expect(USERNAME_ERROR_MESSAGES[code].length).toBeGreaterThan(0)
      })
    })

    it('should have user-friendly messages', () => {
      expect(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.TOO_SHORT]).toContain('at least 3 characters')
      expect(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.TOO_LONG]).toContain('less than 30 characters')
      expect(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.NETWORK_ERROR]).toContain('Network error')
      expect(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.TIMEOUT_ERROR]).toContain('timed out')
    })
  })

  describe('getErrorCodeFromMessage', () => {
    it('should map validation error messages to correct codes', () => {
      expect(getErrorCodeFromMessage('Username must be at least 3 characters')).toBe(USERNAME_ERROR_CODES.TOO_SHORT)
      expect(getErrorCodeFromMessage('Username must be less than 30 characters')).toBe(USERNAME_ERROR_CODES.TOO_LONG)
      expect(getErrorCodeFromMessage('Username can only contain letters, numbers')).toBe(USERNAME_ERROR_CODES.INVALID_CHARACTERS)
      expect(getErrorCodeFromMessage('Username cannot start with')).toBe(USERNAME_ERROR_CODES.STARTS_WITH_SPECIAL)
      expect(getErrorCodeFromMessage('Username cannot end with')).toBe(USERNAME_ERROR_CODES.ENDS_WITH_SPECIAL)
      expect(getErrorCodeFromMessage('consecutive special')).toBe(USERNAME_ERROR_CODES.CONSECUTIVE_SPECIAL)
    })

    it('should map availability error messages to correct codes', () => {
      expect(getErrorCodeFromMessage('Username is already taken')).toBe(USERNAME_ERROR_CODES.USERNAME_TAKEN)
      expect(getErrorCodeFromMessage('Username not available')).toBe(USERNAME_ERROR_CODES.USERNAME_TAKEN)
    })

    it('should map network error messages to correct codes', () => {
      expect(getErrorCodeFromMessage('Request timed out')).toBe(USERNAME_ERROR_CODES.TIMEOUT_ERROR)
      expect(getErrorCodeFromMessage('Network error occurred')).toBe(USERNAME_ERROR_CODES.NETWORK_ERROR)
      expect(getErrorCodeFromMessage('rate limit exceeded')).toBe(USERNAME_ERROR_CODES.RATE_LIMITED)
      expect(getErrorCodeFromMessage('Server error occurred')).toBe(USERNAME_ERROR_CODES.SERVER_ERROR)
    })

    it('should return UNKNOWN_ERROR for unrecognized messages', () => {
      expect(getErrorCodeFromMessage('Some random error')).toBe(USERNAME_ERROR_CODES.UNKNOWN_ERROR)
      expect(getErrorCodeFromMessage('')).toBe(USERNAME_ERROR_CODES.UNKNOWN_ERROR)
    })
  })

  describe('getUserFriendlyMessage', () => {
    it('should return correct message for valid error codes', () => {
      expect(getUserFriendlyMessage(USERNAME_ERROR_CODES.TOO_SHORT)).toBe(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.TOO_SHORT])
      expect(getUserFriendlyMessage(USERNAME_ERROR_CODES.NETWORK_ERROR)).toBe(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.NETWORK_ERROR])
    })

    it('should return unknown error message for invalid codes', () => {
      expect(getUserFriendlyMessage('INVALID_CODE' as any)).toBe(USERNAME_ERROR_MESSAGES[USERNAME_ERROR_CODES.UNKNOWN_ERROR])
    })
  })

  describe('isRetryableError', () => {
    it('should return true for retryable errors', () => {
      expect(isRetryableError(USERNAME_ERROR_CODES.NETWORK_ERROR)).toBe(true)
      expect(isRetryableError(USERNAME_ERROR_CODES.TIMEOUT_ERROR)).toBe(true)
      expect(isRetryableError(USERNAME_ERROR_CODES.SERVER_ERROR)).toBe(true)
      expect(isRetryableError(USERNAME_ERROR_CODES.UNKNOWN_ERROR)).toBe(true)
    })

    it('should return false for non-retryable errors', () => {
      expect(isRetryableError(USERNAME_ERROR_CODES.TOO_SHORT)).toBe(false)
      expect(isRetryableError(USERNAME_ERROR_CODES.TOO_LONG)).toBe(false)
      expect(isRetryableError(USERNAME_ERROR_CODES.INVALID_CHARACTERS)).toBe(false)
      expect(isRetryableError(USERNAME_ERROR_CODES.USERNAME_TAKEN)).toBe(false)
      expect(isRetryableError(USERNAME_ERROR_CODES.RATE_LIMITED)).toBe(false)
    })
  })

  describe('allowsGracefulDegradation', () => {
    it('should return true for errors that allow form submission', () => {
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.NETWORK_ERROR)).toBe(true)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.TIMEOUT_ERROR)).toBe(true)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.SERVER_ERROR)).toBe(true)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.UNKNOWN_ERROR)).toBe(true)
    })

    it('should return false for errors that block form submission', () => {
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.TOO_SHORT)).toBe(false)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.TOO_LONG)).toBe(false)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.INVALID_CHARACTERS)).toBe(false)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.USERNAME_TAKEN)).toBe(false)
      expect(allowsGracefulDegradation(USERNAME_ERROR_CODES.RATE_LIMITED)).toBe(false)
    })
  })

  describe('SUCCESS_MESSAGES', () => {
    it('should have all required success messages', () => {
      expect(USERNAME_SUCCESS_MESSAGES.AVAILABLE).toBeDefined()
      expect(USERNAME_SUCCESS_MESSAGES.CHECKING).toBeDefined()
      expect(USERNAME_SUCCESS_MESSAGES.VALID_FORMAT).toBeDefined()
    })

    it('should have meaningful success messages', () => {
      expect(USERNAME_SUCCESS_MESSAGES.AVAILABLE).toContain('available')
      expect(USERNAME_SUCCESS_MESSAGES.CHECKING).toContain('Checking')
    })
  })
})