# Error Handling System

This directory contains a comprehensive error handling system for the LinksInBio application. The system provides structured error classes, logging, monitoring, and user-friendly error display components.

## Overview

The error handling system consists of several key components:

1. **Custom Error Classes** - Structured error types with user-friendly messages
2. **Error Logging** - Centralized logging with context and monitoring
3. **Error Boundaries** - React components that catch and handle errors
4. **Error Display Components** - User-friendly error UI components
5. **Error Hooks** - React hooks for error handling in components
6. **Server Action Integration** - Consistent error handling in Server Actions
7. **API Route Integration** - Structured error responses for API endpoints

## Files Structure

```
lib/errors/
├── app-errors.ts              # Core error classes and factory functions
├── __tests__/
│   └── error-handling.test.ts # Comprehensive test suite
└── README.md                  # This documentation

lib/utils/
└── error-logger.ts            # Logging and monitoring utilities

components/error-boundaries/
└── app-error-boundary.tsx     # React error boundary components

components/ui/
├── error-display.tsx          # Error display components
└── error-fallback.tsx         # Error fallback UI components

lib/hooks/
└── use-error-handler.ts       # React hooks for error handling

app/api/monitoring/errors/
└── route.ts                   # Error monitoring API endpoint
```

## Usage Examples

### 1. Using Error Classes in Server Actions

```typescript
import { ErrorFactory } from '@/lib/errors/app-errors'
import { handleServerActionError } from '@/lib/utils/error-logger'

export async function createLink(data: CreateLinkData) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    // Validation
    if (!data.title.trim()) {
      throw ErrorFactory.missingRequiredField('title')
    }

    if (!data.url.startsWith('http')) {
      throw ErrorFactory.invalidLinkUrl(data.url)
    }

    // Business logic...
    const link = await LinkRepository.create(profileId, data)
    return { success: true, data: link }

  } catch (error) {
    return handleServerActionError(error, 'create link', {
      userId: session?.user?.id,
      linkData: data
    })
  }
}
```

### 2. Using Error Boundaries

```tsx
import { AppErrorBoundary, PageErrorBoundary } from '@/components/error-boundaries/app-error-boundary'

// App-level error boundary
function App() {
  return (
    <AppErrorBoundary level="app" context="root">
      <MyApp />
    </AppErrorBoundary>
  )
}

// Page-level error boundary
function DashboardPage() {
  return (
    <PageErrorBoundary context="dashboard">
      <DashboardContent />
    </PageErrorBoundary>
  )
}

// Component-level error boundary with HOC
const SafeComponent = withErrorBoundary(MyComponent, 'my-component')
```

### 3. Using Error Display Components

```tsx
import { ErrorDisplay, ValidationErrorDisplay, ServerErrorDisplay } from '@/components/ui/error-display'

function MyForm() {
  const [error, setError] = useState(null)

  return (
    <div>
      {error && (
        <ValidationErrorDisplay
          error={error}
          onDismiss={() => setError(null)}
        />
      )}
      
      <ServerErrorDisplay
        error={serverError}
        showRetry
        onRetry={handleRetry}
      />
    </div>
  )
}
```

### 4. Using Error Hooks

```tsx
import { useErrorHandler, useFormSubmission } from '@/lib/hooks/use-error-handler'

function MyComponent() {
  // Basic error handling
  const { handleError, isError, errorMessage, clearError } = useErrorHandler({
    context: 'my-component'
  })

  // Form submission with error handling
  const { submit, isSubmitting, isSuccess, error } = useFormSubmission(
    async (data) => {
      return await createLink(data)
    }
  )

  const handleClick = async () => {
    try {
      await someAsyncOperation()
    } catch (error) {
      handleError(error, { operation: 'button-click' })
    }
  }

  return (
    <div>
      {isError && <div className="error">{errorMessage}</div>}
      <button onClick={handleClick}>Click me</button>
    </div>
  )
}
```

### 5. API Route Error Handling

```typescript
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'

export async function GET(request: Request) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      throw ErrorFactory.unauthorized()
    }

    const data = await fetchUserData(session.user.id)
    return Response.json({ data })

  } catch (error) {
    return handleApiError(error, 'get user data', withErrorContext(request))
  }
}
```

## Error Codes

The system uses predefined error codes for consistent error handling:

### Authentication & Authorization
- `UNAUTHORIZED` - User not logged in
- `FORBIDDEN` - User lacks permission
- `SESSION_EXPIRED` - Session has expired

### Profile Management
- `PROFILE_NOT_FOUND` - Profile doesn't exist
- `PROFILE_ALREADY_EXISTS` - Profile already exists
- `INVALID_PROFILE_DATA` - Invalid profile information

### Link Management
- `LINK_NOT_FOUND` - Link doesn't exist
- `INVALID_LINK_URL` - Invalid URL format
- `LINK_LIMIT_EXCEEDED` - Too many links

### Username Management
- `USERNAME_TAKEN` - Username already in use
- `INVALID_USERNAME` - Invalid username format
- `USERNAME_RESERVED` - Username is reserved

### File Upload
- `FILE_TOO_LARGE` - File exceeds size limit
- `INVALID_FILE_TYPE` - Unsupported file type
- `UPLOAD_FAILED` - Upload operation failed

### Database & System
- `DATABASE_ERROR` - Database operation failed
- `CONNECTION_ERROR` - Connection issue
- `INTERNAL_ERROR` - Internal server error
- `NETWORK_ERROR` - Network connectivity issue
- `TIMEOUT_ERROR` - Operation timed out

### Validation
- `VALIDATION_ERROR` - Input validation failed
- `MISSING_REQUIRED_FIELD` - Required field missing

### Rate Limiting
- `RATE_LIMIT_EXCEEDED` - Too many requests

## Error Logging and Monitoring

The system includes comprehensive logging and monitoring:

### Features
- **Structured Logging** - Consistent log format with context
- **Error Deduplication** - Groups similar errors by fingerprint
- **Log Levels** - Debug, Info, Warn, Error levels
- **Context Tracking** - User ID, session, URL, operation context
- **External Monitoring** - Integration with monitoring services
- **Development Tools** - Enhanced error details in development

### Configuration

Set environment variables for monitoring integration:

```env
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
MONITORING_API_KEY=your-api-key
NODE_ENV=production
```

### Accessing Logs

In development, you can access error logs via the API:

```bash
# Get recent error logs
GET /api/monitoring/errors

# Get logs by level
GET /api/monitoring/errors?level=error

# Get logs by fingerprint
GET /api/monitoring/errors?fingerprint=abc123

# Clear logs (development only)
DELETE /api/monitoring/errors
```

## Best Practices

### 1. Use Specific Error Types
```typescript
// Good
throw ErrorFactory.usernameTaken('john_doe')

// Avoid
throw new Error('Username taken')
```

### 2. Provide Context
```typescript
// Good
return handleServerActionError(error, 'create user profile', {
  userId: session.user.id,
  profileData: data,
  timestamp: new Date().toISOString()
})

// Avoid
return { success: false, error: 'Something went wrong' }
```

### 3. Use Error Boundaries
```tsx
// Good - Wrap components that might error
<ComponentErrorBoundary context="user-profile">
  <UserProfile />
</ComponentErrorBoundary>

// Avoid - Letting errors bubble up unhandled
<UserProfile />
```

### 4. Handle Async Operations
```typescript
// Good
const { execute, isLoading, error } = useAsyncOperation(fetchData)

// Avoid
const [data, setData] = useState(null)
useEffect(() => {
  fetchData().then(setData) // No error handling
}, [])
```

### 5. Provide User-Friendly Messages
```typescript
// Good
const error = ErrorFactory.validationError(
  'username',
  'Username must be 3-30 characters and contain only letters, numbers, and underscores',
  username
)

// Avoid
throw new Error('Invalid username format')
```

## Testing

The error handling system includes comprehensive tests:

```bash
# Run error handling tests
npm test -- lib/errors/__tests__/error-handling.test.ts

# Run all tests
npm test
```

## Integration with External Services

The system is designed to integrate with external monitoring services:

### Sentry Integration
```typescript
// In your app initialization
if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_SENTRY_DSN) {
  window.Sentry = Sentry
}
```

### Custom Monitoring Service
```typescript
// Set webhook URL in environment variables
MONITORING_WEBHOOK_URL=https://your-service.com/errors
MONITORING_API_KEY=your-api-key
```

## Performance Considerations

- **Log Rotation** - Logs are automatically rotated to prevent memory issues
- **Async Reporting** - External reporting doesn't block the main thread
- **Error Deduplication** - Similar errors are grouped to reduce noise
- **Conditional Logging** - Respects log levels to reduce overhead
- **Client-Side Optimization** - Minimal client-side error handling code

## Security Considerations

- **Sensitive Data** - Error details are sanitized in production
- **Stack Traces** - Only shown in development environment
- **User Context** - Personal information is not logged
- **Rate Limiting** - Error reporting is rate-limited to prevent abuse
- **Authentication** - Error logs require authentication to access