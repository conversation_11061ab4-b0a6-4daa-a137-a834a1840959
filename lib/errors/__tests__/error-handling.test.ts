import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { AppError, ErrorFactory, ErrorCodes, isAppError, toAppError, getErrorMessage, shouldReportError } from '../app-errors'
import { errorLogger, handleServerActionError, handleApiError } from '../../utils/error-logger'

// Mock Response for Node.js test environment
global.Response = class MockResponse {
  status: number
  body: string
  headers: Record<string, string>

  constructor(body: string, init?: { status?: number; headers?: Record<string, string> }) {
    this.body = body
    this.status = init?.status || 200
    this.headers = init?.headers || {}
  }

  async json() {
    return JSON.parse(this.body)
  }
} as any

describe('AppError', () => {
  it('should create an AppError with correct properties', () => {
    const error = new AppError('Test error', ErrorCodes.VALIDATION_ERROR, 400, { field: 'test' })
    
    expect(error.message).toBe('Test error')
    expect(error.code).toBe(ErrorCodes.VALIDATION_ERROR)
    expect(error.statusCode).toBe(400)
    expect(error.details).toEqual({ field: 'test' })
    expect(error.name).toBe('AppError')
  })

  it('should generate user-friendly messages', () => {
    const unauthorizedError = new AppError('Unauthorized', ErrorCodes.UNAUTHORIZED, 401)
    expect(unauthorizedError.getUserMessage()).toBe('You need to be logged in to perform this action.')

    const validationError = new AppError('Invalid input', ErrorCodes.VALIDATION_ERROR, 400)
    expect(validationError.getUserMessage()).toBe('Please check your input and try again.')

    const customError = new AppError('Custom error', 'CUSTOM_CODE', 500, undefined, 'Custom user message')
    expect(customError.getUserMessage()).toBe('Custom user message')
  })

  it('should determine if error should be logged', () => {
    const serverError = new AppError('Server error', ErrorCodes.INTERNAL_ERROR, 500)
    expect(serverError.shouldLog()).toBe(true)

    const validationError = new AppError('Validation error', ErrorCodes.VALIDATION_ERROR, 400)
    expect(validationError.shouldLog()).toBe(false)

    const userError = new AppError('Username taken', ErrorCodes.USERNAME_TAKEN, 409)
    expect(userError.shouldLog()).toBe(false)
  })

  it('should convert to JSON correctly', () => {
    const error = new AppError('Test error', ErrorCodes.DATABASE_ERROR, 500, { query: 'SELECT *' })
    const json = error.toJSON()

    expect(json.name).toBe('AppError')
    expect(json.message).toBe('Test error')
    expect(json.code).toBe(ErrorCodes.DATABASE_ERROR)
    expect(json.statusCode).toBe(500)
    expect(json.details).toEqual({ query: 'SELECT *' })
  })
})

describe('ErrorFactory', () => {
  it('should create unauthorized errors', () => {
    const error = ErrorFactory.unauthorized({ userId: '123' })
    
    expect(error.code).toBe(ErrorCodes.UNAUTHORIZED)
    expect(error.statusCode).toBe(401)
    expect(error.details).toEqual({ userId: '123' })
  })

  it('should create profile not found errors', () => {
    const error = ErrorFactory.profileNotFound('profile-123')
    
    expect(error.code).toBe(ErrorCodes.PROFILE_NOT_FOUND)
    expect(error.statusCode).toBe(404)
    expect(error.details).toEqual({ profileId: 'profile-123' })
  })

  it('should create link not found errors', () => {
    const error = ErrorFactory.linkNotFound('link-123')
    
    expect(error.code).toBe(ErrorCodes.LINK_NOT_FOUND)
    expect(error.statusCode).toBe(404)
    expect(error.details).toEqual({ linkId: 'link-123' })
  })

  it('should create validation errors', () => {
    const error = ErrorFactory.validationError('username', 'Username is required', 'invalid-username')
    
    expect(error.code).toBe(ErrorCodes.VALIDATION_ERROR)
    expect(error.statusCode).toBe(400)
    expect(error.details).toEqual({ field: 'username', value: 'invalid-username' })
    expect(error.userMessage).toBe('Username is required')
  })

  it('should create username taken errors', () => {
    const error = ErrorFactory.usernameTaken('testuser')
    
    expect(error.code).toBe(ErrorCodes.USERNAME_TAKEN)
    expect(error.statusCode).toBe(409)
    expect(error.details).toEqual({ username: 'testuser' })
  })

  it('should create file upload errors', () => {
    const error = ErrorFactory.fileTooLarge(5000000, 2000000)
    
    expect(error.code).toBe(ErrorCodes.FILE_TOO_LARGE)
    expect(error.statusCode).toBe(400)
    expect(error.details).toEqual({ size: 5000000, maxSize: 2000000 })
  })

  it('should create database errors', () => {
    const originalError = new Error('Connection failed')
    const error = ErrorFactory.databaseError('user creation', originalError)
    
    expect(error.code).toBe(ErrorCodes.DATABASE_ERROR)
    expect(error.statusCode).toBe(500)
    expect(error.details).toEqual({ operation: 'user creation', originalError: 'Connection failed' })
  })

  it('should create rate limit errors', () => {
    const error = ErrorFactory.rateLimitExceeded(100, 60000)
    
    expect(error.code).toBe(ErrorCodes.RATE_LIMIT_EXCEEDED)
    expect(error.statusCode).toBe(429)
    expect(error.details).toEqual({ limit: 100, windowMs: 60000 })
  })
})

describe('Error utility functions', () => {
  it('should identify AppError instances', () => {
    const appError = new AppError('Test', ErrorCodes.INTERNAL_ERROR, 500)
    const regularError = new Error('Test')
    const notAnError = 'string error'

    expect(isAppError(appError)).toBe(true)
    expect(isAppError(regularError)).toBe(false)
    expect(isAppError(notAnError)).toBe(false)
  })

  it('should convert errors to AppError', () => {
    const regularError = new Error('Regular error')
    const appError = toAppError(regularError, ErrorCodes.VALIDATION_ERROR)

    expect(isAppError(appError)).toBe(true)
    expect(appError.code).toBe(ErrorCodes.VALIDATION_ERROR)
    expect(appError.message).toBe('Regular error')

    const stringError = 'String error'
    const convertedError = toAppError(stringError)

    expect(isAppError(convertedError)).toBe(true)
    expect(convertedError.code).toBe(ErrorCodes.INTERNAL_ERROR)
    expect(convertedError.message).toBe('Unknown error occurred')
  })

  it('should extract error messages', () => {
    const appError = new AppError('App error', ErrorCodes.UNAUTHORIZED, 401)
    expect(getErrorMessage(appError)).toBe('You need to be logged in to perform this action.')

    const regularError = new Error('Regular error')
    expect(getErrorMessage(regularError)).toBe('Regular error')

    const stringError = 'String error'
    expect(getErrorMessage(stringError)).toBe('An unexpected error occurred')
  })

  it('should determine if errors should be reported', () => {
    const serverError = new AppError('Server error', ErrorCodes.INTERNAL_ERROR, 500)
    expect(shouldReportError(serverError)).toBe(true)

    const validationError = new AppError('Validation error', ErrorCodes.VALIDATION_ERROR, 400)
    expect(shouldReportError(validationError)).toBe(false)

    const regularError = new Error('Regular error')
    expect(shouldReportError(regularError)).toBe(true)

    const stringError = 'String error'
    expect(shouldReportError(stringError)).toBe(true)
  })
})

describe('Error logging', () => {
  beforeEach(() => {
    errorLogger.clearLogs()
    errorLogger.setLogLevel('debug') // Ensure all logs are captured in tests
    jest.clearAllMocks()
  })

  it('should log errors with context', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const error = new Error('Test error')
    const context = { userId: '123', operation: 'test' }

    errorLogger.error('Test message', context, error)

    const logs = errorLogger.getRecentLogs(1)
    expect(logs).toHaveLength(1)
    expect(logs[0].level).toBe('error')
    expect(logs[0].message).toBe('Test message')
    expect(logs[0].context).toEqual(context)
    expect(logs[0].error).toBe(error)

    consoleSpy.mockRestore()
  })

  it('should log warnings', () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()
    const context = { operation: 'test' }

    errorLogger.warn('Test warning', context)

    const logs = errorLogger.getRecentLogs(1)
    expect(logs).toHaveLength(1)
    expect(logs[0].level).toBe('warn')
    expect(logs[0].message).toBe('Test warning')

    consoleSpy.mockRestore()
  })

  it('should filter logs by level', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const warnSpy = jest.spyOn(console, 'warn').mockImplementation()
    const infoSpy = jest.spyOn(console, 'info').mockImplementation()

    errorLogger.error('Error message')
    errorLogger.warn('Warning message')
    errorLogger.info('Info message')

    const errorLogs = errorLogger.getLogsByLevel('error')
    const warnLogs = errorLogger.getLogsByLevel('warn')
    const infoLogs = errorLogger.getLogsByLevel('info')

    expect(errorLogs).toHaveLength(1)
    expect(warnLogs).toHaveLength(1)
    expect(infoLogs).toHaveLength(1)

    consoleSpy.mockRestore()
    warnSpy.mockRestore()
    infoSpy.mockRestore()
  })

  it('should respect log level settings', () => {
    const originalLevel = errorLogger.getLogLevel()
    errorLogger.setLogLevel('error')

    const consoleSpy = jest.spyOn(console, 'info').mockImplementation()

    errorLogger.info('This should not be logged')
    const logs = errorLogger.getRecentLogs()
    
    expect(logs.filter(log => log.message === 'This should not be logged')).toHaveLength(0)

    errorLogger.setLogLevel(originalLevel)
    consoleSpy.mockRestore()
  })
})

describe('Server Action error handling', () => {
  it('should handle server action errors correctly', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const appError = ErrorFactory.validationError('username', 'Username is required')
    
    const result = handleServerActionError(appError, 'create user', { userId: '123' })

    expect(result.success).toBe(false)
    expect(result.error).toBe('Username is required')
    expect(result.code).toBe(ErrorCodes.VALIDATION_ERROR)

    consoleSpy.mockRestore()
  })

  it('should handle regular errors in server actions', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const regularError = new Error('Database connection failed')
    
    const result = handleServerActionError(regularError, 'create user')

    expect(result.success).toBe(false)
    expect(result.error).toBe('An unexpected error occurred. Please try again later.')

    consoleSpy.mockRestore()
  })
})

describe('API error handling', () => {
  it('should handle API errors correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const appError = ErrorFactory.unauthorized()
    
    const response = handleApiError(appError, 'get user profile')

    expect(response.status).toBe(401)
    
    // Test response body
    const body = await response.json()
    expect(body.error.message).toBe('You need to be logged in to perform this action.')
    expect(body.error.code).toBe(ErrorCodes.UNAUTHORIZED)

    consoleSpy.mockRestore()
  })

  it('should handle regular errors in API routes', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    const regularError = new Error('Database error')
    
    const response = handleApiError(regularError, 'get user profile')

    expect(response.status).toBe(500)
    
    const body = await response.json()
    expect(body.error.message).toBe('An unexpected error occurred. Please try again later.')

    consoleSpy.mockRestore()
  })
})