/**
 * Custom error classes for analytics functionality
 * Provides specific error codes and structured error handling
 */

export class AnalyticsError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: unknown
  ) {
    super(message)
    this.name = 'AnalyticsError'
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AnalyticsError)
    }
  }

  /**
   * Convert error to JSON for API responses
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      stack: this.stack
    }
  }

  /**
   * Create a user-friendly error message
   */
  getUserMessage(): string {
    switch (this.code) {
      case AnalyticsErrorCodes.PROFILE_NOT_FOUND:
        return 'Profile not found. Please check if the profile exists.'
      case AnalyticsErrorCodes.INVALID_DATE_RANGE:
        return 'Invalid date range provided. Please check your date parameters.'
      case AnalyticsErrorCodes.DATA_PROCESSING_ERROR:
        return 'Error processing analytics data. Please try again later.'
      case AnalyticsErrorCodes.CHART_RENDERING_ERROR:
        return 'Error rendering chart. Please refresh the page.'
      case AnalyticsErrorCodes.DATABASE_CONNECTION_ERROR:
        return 'Database connection error. Please try again later.'
      case AnalyticsErrorCodes.INVALID_PROFILE_ID:
        return 'Invalid profile ID provided.'
      case AnalyticsErrorCodes.INVALID_PERIOD:
        return 'Invalid time period. Please select a period between 1 and 365 days.'
      case AnalyticsErrorCodes.DATA_VALIDATION_ERROR:
        return 'Data validation failed. Please check your input.'
      case AnalyticsErrorCodes.AGGREGATION_ERROR:
        return 'Error aggregating analytics data. Please try again.'
      case AnalyticsErrorCodes.UNAUTHORIZED:
        return 'You are not authorized to access this analytics data.'
      default:
        return 'An unexpected error occurred. Please try again later.'
    }
  }
}

/**
 * Predefined error codes for consistent error handling
 */
export const AnalyticsErrorCodes = {
  PROFILE_NOT_FOUND: 'PROFILE_NOT_FOUND',
  INVALID_DATE_RANGE: 'INVALID_DATE_RANGE',
  DATA_PROCESSING_ERROR: 'DATA_PROCESSING_ERROR',
  CHART_RENDERING_ERROR: 'CHART_RENDERING_ERROR',
  DATABASE_CONNECTION_ERROR: 'DATABASE_CONNECTION_ERROR',
  INVALID_PROFILE_ID: 'INVALID_PROFILE_ID',
  INVALID_PERIOD: 'INVALID_PERIOD',
  DATA_VALIDATION_ERROR: 'DATA_VALIDATION_ERROR',
  AGGREGATION_ERROR: 'AGGREGATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR'
} as const

export type AnalyticsErrorCode = typeof AnalyticsErrorCodes[keyof typeof AnalyticsErrorCodes]

/**
 * Factory functions for creating specific analytics errors
 */
export class AnalyticsErrorFactory {
  static profileNotFound(profileId: string): AnalyticsError {
    return new AnalyticsError(
      `Profile with ID ${profileId} not found`,
      AnalyticsErrorCodes.PROFILE_NOT_FOUND,
      404,
      { profileId }
    )
  }

  static invalidDateRange(startDate?: Date, endDate?: Date): AnalyticsError {
    return new AnalyticsError(
      'Invalid date range provided',
      AnalyticsErrorCodes.INVALID_DATE_RANGE,
      400,
      { startDate, endDate }
    )
  }

  static dataProcessingError(operation: string, originalError?: Error): AnalyticsError {
    return new AnalyticsError(
      `Error processing analytics data during ${operation}`,
      AnalyticsErrorCodes.DATA_PROCESSING_ERROR,
      500,
      { operation, originalError: originalError?.message }
    )
  }

  static chartRenderingError(chartType: string, originalError?: Error): AnalyticsError {
    return new AnalyticsError(
      `Error rendering ${chartType} chart`,
      AnalyticsErrorCodes.CHART_RENDERING_ERROR,
      500,
      { chartType, originalError: originalError?.message }
    )
  }

  static databaseConnectionError(originalError?: Error): AnalyticsError {
    return new AnalyticsError(
      'Database connection error',
      AnalyticsErrorCodes.DATABASE_CONNECTION_ERROR,
      500,
      { originalError: originalError?.message }
    )
  }

  static invalidProfileId(profileId: unknown): AnalyticsError {
    return new AnalyticsError(
      'Invalid profile ID provided',
      AnalyticsErrorCodes.INVALID_PROFILE_ID,
      400,
      { providedValue: profileId, expectedType: 'string' }
    )
  }

  static invalidPeriod(period: unknown): AnalyticsError {
    return new AnalyticsError(
      'Invalid period provided. Must be between 1 and 365 days',
      AnalyticsErrorCodes.INVALID_PERIOD,
      400,
      { providedValue: period, validRange: '1-365 days' }
    )
  }

  static dataValidationError(field: string, value: unknown, expectedType: string): AnalyticsError {
    return new AnalyticsError(
      `Data validation failed for field: ${field}`,
      AnalyticsErrorCodes.DATA_VALIDATION_ERROR,
      400,
      { field, providedValue: value, expectedType }
    )
  }

  static aggregationError(operation: string, originalError?: Error): AnalyticsError {
    return new AnalyticsError(
      `Error during data aggregation: ${operation}`,
      AnalyticsErrorCodes.AGGREGATION_ERROR,
      500,
      { operation, originalError: originalError?.message }
    )
  }

  static unauthorized(userId?: string): AnalyticsError {
    return new AnalyticsError(
      'Unauthorized access to analytics data',
      AnalyticsErrorCodes.UNAUTHORIZED,
      401,
      { userId }
    )
  }

  static networkError(originalError?: Error): AnalyticsError {
    return new AnalyticsError(
      'Network error occurred while fetching analytics data',
      AnalyticsErrorCodes.NETWORK_ERROR,
      503,
      { originalError: originalError?.message }
    )
  }

  static timeoutError(operation: string): AnalyticsError {
    return new AnalyticsError(
      `Operation timed out: ${operation}`,
      AnalyticsErrorCodes.TIMEOUT_ERROR,
      408,
      { operation }
    )
  }
}

/**
 * Utility function to check if an error is an AnalyticsError
 */
export function isAnalyticsError(error: unknown): error is AnalyticsError {
  return error instanceof AnalyticsError
}

/**
 * Utility function to convert any error to an AnalyticsError
 */
export function toAnalyticsError(error: unknown, defaultCode: AnalyticsErrorCode = AnalyticsErrorCodes.DATA_PROCESSING_ERROR): AnalyticsError {
  if (isAnalyticsError(error)) {
    return error
  }

  if (error instanceof Error) {
    return new AnalyticsError(
      error.message,
      defaultCode,
      500,
      { originalError: error.message, stack: error.stack }
    )
  }

  return new AnalyticsError(
    'Unknown error occurred',
    defaultCode,
    500,
    { originalError: String(error) }
  )
}