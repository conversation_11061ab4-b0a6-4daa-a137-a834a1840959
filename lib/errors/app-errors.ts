/**
 * Comprehensive error handling system for LinksInBio application
 * Provides structured error classes, codes, and user-friendly messages
 */

export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: unknown,
    public userMessage?: string
  ) {
    super(message)
    this.name = 'AppError'
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError)
    }
  }

  /**
   * Convert error to JSON for API responses
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      userMessage: this.userMessage,
      stack: process.env.NODE_ENV === 'development' ? this.stack : undefined
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    if (this.userMessage) {
      return this.userMessage
    }

    switch (this.code) {
      // Authentication errors
      case ErrorCodes.UNAUTHORIZED:
        return 'You need to be logged in to perform this action.'
      case ErrorCodes.FORBIDDEN:
        return 'You do not have permission to perform this action.'
      case ErrorCodes.SESSION_EXPIRED:
        return 'Your session has expired. Please log in again.'
      
      // Profile errors
      case ErrorCodes.PROFILE_NOT_FOUND:
        return 'Profile not found. Please check if the profile exists.'
      case ErrorCodes.PROFILE_ALREADY_EXISTS:
        return 'A profile with this information already exists.'
      case ErrorCodes.INVALID_PROFILE_DATA:
        return 'Invalid profile information provided.'
      
      // Link errors
      case ErrorCodes.LINK_NOT_FOUND:
        return 'Link not found. It may have been deleted or moved.'
      case ErrorCodes.INVALID_LINK_URL:
        return 'Please provide a valid URL for your link.'
      case ErrorCodes.LINK_LIMIT_EXCEEDED:
        return 'You have reached the maximum number of links allowed.'
      
      // Username errors
      case ErrorCodes.USERNAME_TAKEN:
        return 'This username is already taken. Please choose another one.'
      case ErrorCodes.INVALID_USERNAME:
        return 'Username must be 3-30 characters and contain only letters, numbers, and underscores.'
      case ErrorCodes.USERNAME_RESERVED:
        return 'This username is reserved. Please choose another one.'
      
      // File upload errors
      case ErrorCodes.FILE_TOO_LARGE:
        return 'File is too large. Please choose a smaller file.'
      case ErrorCodes.INVALID_FILE_TYPE:
        return 'Invalid file type. Please upload a supported image format.'
      case ErrorCodes.UPLOAD_FAILED:
        return 'File upload failed. Please try again.'
      
      // Database errors
      case ErrorCodes.DATABASE_ERROR:
        return 'A database error occurred. Please try again later.'
      case ErrorCodes.CONNECTION_ERROR:
        return 'Connection error. Please check your internet connection.'
      
      // Validation errors
      case ErrorCodes.VALIDATION_ERROR:
        return 'Please check your input and try again.'
      case ErrorCodes.MISSING_REQUIRED_FIELD:
        return 'Please fill in all required fields.'
      
      // Rate limiting
      case ErrorCodes.RATE_LIMIT_EXCEEDED:
        return 'Too many requests. Please wait a moment before trying again.'
      
      // Theme errors
      case ErrorCodes.INVALID_THEME_DATA:
        return 'Invalid theme configuration. Please check your settings.'
      case ErrorCodes.THEME_NOT_FOUND:
        return 'Theme not found. Please select a valid theme.'
      
      default:
        return 'An unexpected error occurred. Please try again later.'
    }
  }

  /**
   * Check if error should be logged (excludes user errors)
   */
  shouldLog(): boolean {
    const userErrorCodes = [
      ErrorCodes.VALIDATION_ERROR,
      ErrorCodes.MISSING_REQUIRED_FIELD,
      ErrorCodes.INVALID_USERNAME,
      ErrorCodes.USERNAME_TAKEN,
      ErrorCodes.INVALID_LINK_URL,
      ErrorCodes.FILE_TOO_LARGE,
      ErrorCodes.INVALID_FILE_TYPE
    ]
    
    return !userErrorCodes.includes(this.code as any)
  }
}

/**
 * Predefined error codes for consistent error handling
 */
export const ErrorCodes = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  
  // Profile Management
  PROFILE_NOT_FOUND: 'PROFILE_NOT_FOUND',
  PROFILE_ALREADY_EXISTS: 'PROFILE_ALREADY_EXISTS',
  INVALID_PROFILE_DATA: 'INVALID_PROFILE_DATA',
  
  // Link Management
  LINK_NOT_FOUND: 'LINK_NOT_FOUND',
  INVALID_LINK_URL: 'INVALID_LINK_URL',
  LINK_LIMIT_EXCEEDED: 'LINK_LIMIT_EXCEEDED',
  
  // Username Management
  USERNAME_TAKEN: 'USERNAME_TAKEN',
  INVALID_USERNAME: 'INVALID_USERNAME',
  USERNAME_RESERVED: 'USERNAME_RESERVED',
  
  // File Upload
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // Database
  DATABASE_ERROR: 'DATABASE_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Theme Management
  INVALID_THEME_DATA: 'INVALID_THEME_DATA',
  THEME_NOT_FOUND: 'THEME_NOT_FOUND',
  
  // Analytics
  ANALYTICS_ERROR: 'ANALYTICS_ERROR',
  
  // Generic
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR'
} as const

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes]

/**
 * Factory functions for creating specific application errors
 */
export class ErrorFactory {
  // Authentication errors
  static unauthorized(details?: unknown): AppError {
    return new AppError(
      'Unauthorized access',
      ErrorCodes.UNAUTHORIZED,
      401,
      details
    )
  }

  static forbidden(details?: unknown): AppError {
    return new AppError(
      'Forbidden access',
      ErrorCodes.FORBIDDEN,
      403,
      details
    )
  }

  static sessionExpired(): AppError {
    return new AppError(
      'Session expired',
      ErrorCodes.SESSION_EXPIRED,
      401
    )
  }

  // Profile errors
  static profileNotFound(profileId?: string): AppError {
    return new AppError(
      'Profile not found',
      ErrorCodes.PROFILE_NOT_FOUND,
      404,
      { profileId }
    )
  }

  static invalidProfileData(field: string, value: unknown): AppError {
    return new AppError(
      `Invalid profile data: ${field}`,
      ErrorCodes.INVALID_PROFILE_DATA,
      400,
      { field, value }
    )
  }

  // Link errors
  static linkNotFound(linkId: string): AppError {
    return new AppError(
      'Link not found',
      ErrorCodes.LINK_NOT_FOUND,
      404,
      { linkId }
    )
  }

  static invalidLinkUrl(url: string): AppError {
    return new AppError(
      'Invalid link URL',
      ErrorCodes.INVALID_LINK_URL,
      400,
      { url }
    )
  }

  static linkLimitExceeded(currentCount: number, maxCount: number): AppError {
    return new AppError(
      'Link limit exceeded',
      ErrorCodes.LINK_LIMIT_EXCEEDED,
      400,
      { currentCount, maxCount }
    )
  }

  // Username errors
  static usernameTaken(username: string): AppError {
    return new AppError(
      'Username is already taken',
      ErrorCodes.USERNAME_TAKEN,
      409,
      { username }
    )
  }

  static invalidUsername(username: string, reason?: string): AppError {
    return new AppError(
      'Invalid username format',
      ErrorCodes.INVALID_USERNAME,
      400,
      { username, reason }
    )
  }

  static usernameReserved(username: string): AppError {
    return new AppError(
      'Username is reserved',
      ErrorCodes.USERNAME_RESERVED,
      400,
      { username }
    )
  }

  // File upload errors
  static fileTooLarge(size: number, maxSize: number): AppError {
    return new AppError(
      'File too large',
      ErrorCodes.FILE_TOO_LARGE,
      400,
      { size, maxSize }
    )
  }

  static invalidFileType(type: string, allowedTypes: string[]): AppError {
    return new AppError(
      'Invalid file type',
      ErrorCodes.INVALID_FILE_TYPE,
      400,
      { type, allowedTypes }
    )
  }

  static uploadFailed(originalError?: Error): AppError {
    return new AppError(
      'File upload failed',
      ErrorCodes.UPLOAD_FAILED,
      500,
      { originalError: originalError?.message }
    )
  }

  // Database errors
  static databaseError(operation: string, originalError?: Error): AppError {
    return new AppError(
      `Database error during ${operation}`,
      ErrorCodes.DATABASE_ERROR,
      500,
      { operation, originalError: originalError?.message }
    )
  }

  static connectionError(originalError?: Error): AppError {
    return new AppError(
      'Database connection error',
      ErrorCodes.CONNECTION_ERROR,
      503,
      { originalError: originalError?.message }
    )
  }

  // Validation errors
  static validationError(field: string, message: string, value?: unknown): AppError {
    return new AppError(
      `Validation error: ${message}`,
      ErrorCodes.VALIDATION_ERROR,
      400,
      { field, value },
      message
    )
  }

  static missingRequiredField(field: string): AppError {
    return new AppError(
      `Missing required field: ${field}`,
      ErrorCodes.MISSING_REQUIRED_FIELD,
      400,
      { field },
      `${field} is required`
    )
  }

  // Rate limiting
  static rateLimitExceeded(limit: number, windowMs: number): AppError {
    return new AppError(
      'Rate limit exceeded',
      ErrorCodes.RATE_LIMIT_EXCEEDED,
      429,
      { limit, windowMs }
    )
  }

  // Theme errors
  static invalidThemeData(field: string, value: unknown): AppError {
    return new AppError(
      `Invalid theme data: ${field}`,
      ErrorCodes.INVALID_THEME_DATA,
      400,
      { field, value }
    )
  }

  static themeNotFound(themeId: string): AppError {
    return new AppError(
      'Theme not found',
      ErrorCodes.THEME_NOT_FOUND,
      404,
      { themeId }
    )
  }

  // Generic errors
  static internalError(message: string, originalError?: Error): AppError {
    return new AppError(
      message,
      ErrorCodes.INTERNAL_ERROR,
      500,
      { originalError: originalError?.message }
    )
  }

  static networkError(originalError?: Error): AppError {
    return new AppError(
      'Network error',
      ErrorCodes.NETWORK_ERROR,
      503,
      { originalError: originalError?.message }
    )
  }

  static timeoutError(operation: string, timeoutMs: number): AppError {
    return new AppError(
      `Operation timed out: ${operation}`,
      ErrorCodes.TIMEOUT_ERROR,
      408,
      { operation, timeoutMs }
    )
  }
}

/**
 * Utility functions for error handling
 */
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError
}

export function toAppError(error: unknown, defaultCode: ErrorCode = ErrorCodes.INTERNAL_ERROR): AppError {
  if (isAppError(error)) {
    return error
  }

  if (error instanceof Error) {
    return new AppError(
      error.message,
      defaultCode,
      500,
      { originalError: error.message, stack: error.stack }
    )
  }

  return new AppError(
    'Unknown error occurred',
    defaultCode,
    500,
    { originalError: String(error) }
  )
}

/**
 * Extract user-friendly error message from any error
 */
export function getErrorMessage(error: unknown): string {
  if (isAppError(error)) {
    return error.getUserMessage()
  }

  if (error instanceof Error) {
    return error.message
  }

  return 'An unexpected error occurred'
}

/**
 * Check if error should be reported to monitoring services
 */
export function shouldReportError(error: unknown): boolean {
  if (isAppError(error)) {
    return error.shouldLog()
  }

  // Report all non-AppError errors
  return true
}