{"name": "links-in-bio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "eslint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:performance": "node scripts/performance-test.js"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.2", "@prisma/client": "^6.16.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@uploadthing/react": "^7.3.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jest-environment-jsdom": "^30.1.2", "lucide-react": "^0.544.0", "next": "15.5.3", "next-auth": "^5.0.0-beta.29", "prisma": "^6.16.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "recharts": "^3.2.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "ua-parser-js": "^2.0.5", "uploadthing": "^7.7.4", "zod": "^4.1.8", "zustand": "^5.0.8"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.5.3", "@playwright/test": "^1.55.0", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ua-parser-js": "^0.7.39", "axe-playwright": "^2.2.2", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}