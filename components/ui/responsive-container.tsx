'use client'

import { useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  mobileBreakpoint?: number
}

export function ResponsiveContainer({ 
  children, 
  className,
  mobileBreakpoint = 768 
}: ResponsiveContainerProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    const checkMobile = () => {
      setIsMobile(window.innerWidth < mobileBreakpoint)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [mobileBreakpoint])

  if (!isClient) {
    // Server-side render with mobile-first approach
    return (
      <div className={cn("w-full", className)}>
        {children}
      </div>
    )
  }

  return (
    <div 
      className={cn(
        "w-full transition-all duration-200",
        isMobile && "px-4 py-2",
        className
      )}
      data-mobile={isMobile}
    >
      {children}
    </div>
  )
}

// Hook for responsive behavior
export function useResponsive(breakpoint: number = 768) {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isDesktop, setIsDesktop] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth
      setIsMobile(width < breakpoint)
      setIsTablet(width >= breakpoint && width < 1024)
      setIsDesktop(width >= 1024)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [breakpoint])

  return { isMobile, isTablet, isDesktop }
}