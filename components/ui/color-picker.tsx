"use client"

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

interface ColorPickerProps {
  value: string
  onChange: (color: string) => void
  label?: string
  className?: string
}

export function ColorPicker({ value, onChange, label, className }: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [inputValue, setInputValue] = useState(value)
  const colorInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setInputValue(value)
  }, [value])

  const handleColorChange = (newColor: string) => {
    setInputValue(newColor)
    onChange(newColor)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    
    // Validate hex color format
    if (/^#[0-9A-F]{6}$/i.test(newValue)) {
      onChange(newValue)
    }
  }

  const handleInputBlur = () => {
    // If invalid format, revert to current value
    if (!/^#[0-9A-F]{6}$/i.test(inputValue)) {
      setInputValue(value)
    }
  }

  const presetColors = [
    '#000000', '#ffffff', '#ef4444', '#f97316', '#eab308',
    '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
  ]

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label>{label}</Label>}
      
      <div className="flex items-center space-x-2">
        <Button
          type="button"
          variant="outline"
          className="w-12 h-10 p-0 border-2"
          style={{ backgroundColor: value }}
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="sr-only">Pick color</span>
        </Button>
        
        <Input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          placeholder="#000000"
          className="font-mono text-sm"
        />
        
        <input
          ref={colorInputRef}
          type="color"
          value={value}
          onChange={(e) => handleColorChange(e.target.value)}
          className="sr-only"
        />
        
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => colorInputRef.current?.click()}
        >
          Pick
        </Button>
      </div>

      {isOpen && (
        <div className="p-3 border rounded-lg bg-background shadow-lg">
          <div className="grid grid-cols-5 gap-2 mb-3">
            {presetColors.map((color) => (
              <button
                key={color}
                type="button"
                className={cn(
                  "w-8 h-8 rounded border-2 transition-all",
                  value === color ? "border-primary scale-110" : "border-border hover:scale-105"
                )}
                style={{ backgroundColor: color }}
                onClick={() => {
                  handleColorChange(color)
                  setIsOpen(false)
                }}
              />
            ))}
          </div>
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => setIsOpen(false)}
          >
            Close
          </Button>
        </div>
      )}
    </div>
  )
}