"use client"

import Image from 'next/image'
import { useState, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Skeleton } from './skeleton'
import { useIntersectionObserver, useImagePreloader } from '@/lib/hooks/use-performance'

interface OptimizedImageProps {
  src: string | null | undefined
  alt: string
  width: number
  height: number
  className?: string
  fallback?: React.ReactNode
  priority?: boolean
  sizes?: string
  quality?: number
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  fallback,
  priority = false,
  sizes,
  quality = 85,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  
  // Use intersection observer for lazy loading (unless priority is true)
  const { hasIntersected } = useIntersectionObserver(containerRef, {
    threshold: 0.1,
    rootMargin: '50px',
  })
  
  // Preload the image if it's in viewport or priority
  const shouldLoad = priority || hasIntersected
  const { isLoaded: preloaded, hasError: preloadError } = useImagePreloader(shouldLoad ? src : null)

  if (!src || hasError || preloadError) {
    return fallback || <Skeleton className={cn("rounded-full", className)} style={{ width, height }} />
  }

  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-hidden", className)} 
      style={{ width, height }}
    >
      {(isLoading || !preloaded) && (
        <Skeleton 
          className="absolute inset-0 rounded-full" 
          style={{ width, height }} 
        />
      )}
      {shouldLoad && (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={cn(
            "object-cover transition-opacity duration-300",
            (isLoading || !preloaded) ? "opacity-0" : "opacity-100",
            className
          )}
          priority={priority}
          quality={quality}
          sizes={sizes || `${width}px`}
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setHasError(true)
            setIsLoading(false)
          }}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
        />
      )}
    </div>
  )
}