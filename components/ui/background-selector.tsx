"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { ColorPicker } from '@/components/ui/color-picker'
import { Badge } from '@/components/ui/badge'
import { Image, Palette, Zap, Upload } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BackgroundSelectorProps {
  type: 'color' | 'gradient' | 'image'
  value: string
  onTypeChange: (type: 'color' | 'gradient' | 'image') => void
  onValueChange: (value: string) => void
  label?: string
  className?: string
}

const gradientPresets = [
  { name: 'Ocean', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
  { name: 'Sunset', value: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' },
  { name: 'Forest', value: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' },
  { name: 'Purple', value: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' },
  { name: 'Fire', value: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)' },
  { name: 'Sky', value: 'linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%)' },
]

export function BackgroundSelector({ 
  type, 
  value, 
  onTypeChange, 
  onValueChange, 
  label, 
  className 
}: BackgroundSelectorProps) {
  const [customGradient, setCustomGradient] = useState(
    type === 'gradient' && !gradientPresets.some(p => p.value === value) ? value : ''
  )

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        onValueChange(result)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {label && <Label>{label}</Label>}
      
      {/* Background Type Selector */}
      <div className="flex space-x-2">
        <Button
          type="button"
          variant={type === 'color' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onTypeChange('color')}
          className="flex items-center space-x-1"
        >
          <Palette className="h-4 w-4" />
          <span>Color</span>
        </Button>
        <Button
          type="button"
          variant={type === 'gradient' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onTypeChange('gradient')}
          className="flex items-center space-x-1"
        >
          <Zap className="h-4 w-4" />
          <span>Gradient</span>
        </Button>
        <Button
          type="button"
          variant={type === 'image' ? 'default' : 'outline'}
          size="sm"
          onClick={() => onTypeChange('image')}
          className="flex items-center space-x-1"
        >
          <Image className="h-4 w-4" />
          <span>Image</span>
        </Button>
      </div>

      {/* Color Picker */}
      {type === 'color' && (
        <ColorPicker
          value={value}
          onChange={onValueChange}
          label="Background Color"
        />
      )}

      {/* Gradient Selector */}
      {type === 'gradient' && (
        <div className="space-y-3">
          <Label>Gradient Presets</Label>
          <div className="grid grid-cols-2 gap-2">
            {gradientPresets.map((preset) => (
              <div
                key={preset.name}
                className={cn(
                  "relative h-16 rounded-lg cursor-pointer border-2 transition-all",
                  value === preset.value ? "border-primary scale-105" : "border-border hover:border-primary/50"
                )}
                style={{ background: preset.value }}
                onClick={() => onValueChange(preset.value)}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <Badge variant="secondary" className="text-xs">
                    {preset.name}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
          
          <div className="space-y-2">
            <Label>Custom Gradient (CSS)</Label>
            <Input
              placeholder="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
              value={customGradient}
              onChange={(e) => setCustomGradient(e.target.value)}
              onBlur={() => {
                if (customGradient.trim()) {
                  onValueChange(customGradient.trim())
                }
              }}
            />
            {customGradient && (
              <div
                className="h-12 rounded border"
                style={{ background: customGradient }}
              />
            )}
          </div>
        </div>
      )}

      {/* Image Upload */}
      {type === 'image' && (
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
              id="background-upload"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById('background-upload')?.click()}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>Upload Image</span>
            </Button>
          </div>
          
          {value && value.startsWith('data:image') && (
            <div className="space-y-2">
              <Label>Preview</Label>
              <div
                className="h-32 rounded border bg-cover bg-center"
                style={{ backgroundImage: `url(${value})` }}
              />
            </div>
          )}
          
          {value && !value.startsWith('data:image') && (
            <div className="space-y-2">
              <Label>Image URL</Label>
              <Input
                value={value}
                onChange={(e) => onValueChange(e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          )}
        </div>
      )}
    </div>
  )
}