"use client"

import * as React from "react"
import { Check, Clock, Zap } from "lucide-react"
import { cn } from "@/lib/utils"
import { USERNAME_SUCCESS_MESSAGES } from "@/lib/constants/username-errors"

interface UsernameSuccessDisplayProps {
  type: 'available' | 'checking' | 'cached'
  message?: string
  showIcon?: boolean
  className?: string
}

const SUCCESS_ICONS = {
  available: Check,
  checking: Clock,
  cached: Zap
} as const

const SUCCESS_COLORS = {
  available: "text-green-600",
  checking: "text-blue-600", 
  cached: "text-green-600"
} as const

const DEFAULT_MESSAGES = {
  available: USERNAME_SUCCESS_MESSAGES.AVAILABLE,
  checking: USERNAME_SUCCESS_MESSAGES.CHECKING,
  cached: `${USERNAME_SUCCESS_MESSAGES.AVAILABLE} (cached)`
} as const

export function UsernameSuccessDisplay({
  type,
  message,
  showIcon = true,
  className
}: UsernameSuccessDisplayProps) {
  const IconComponent = SUCCESS_ICONS[type]
  const colorClass = SUCCESS_COLORS[type]
  const displayMessage = message || DEFAULT_MESSAGES[type]

  return (
    <p 
      className={cn(
        "text-sm flex items-center gap-1",
        colorClass,
        className
      )}
      role="status"
      aria-live="polite"
    >
      {showIcon && (
        <IconComponent 
          className={cn(
            "h-3 w-3 flex-shrink-0",
            type === 'checking' && "animate-pulse"
          )} 
        />
      )}
      {displayMessage}
    </p>
  )
}

/**
 * Compact success indicator for use in input fields
 */
export function UsernameSuccessIcon({
  type,
  className
}: Pick<UsernameSuccessDisplayProps, 'type' | 'className'>) {
  const IconComponent = SUCCESS_ICONS[type]
  const colorClass = SUCCESS_COLORS[type]

  return (
    <IconComponent 
      className={cn(
        "h-4 w-4",
        colorClass,
        type === 'checking' && "animate-pulse",
        className
      )}
      aria-label={DEFAULT_MESSAGES[type]}
    />
  )
}