'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Wifi, 
  Server,
  Shield,
  FileX
} from 'lucide-react'
import { isAppError, ErrorCodes } from '@/lib/errors/app-errors'
import { cn } from '@/lib/utils'

export interface ErrorFallbackProps {
  error: unknown
  resetError?: () => void
  className?: string
  showHomeButton?: boolean
  showReportButton?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'card' | 'page' | 'inline'
}

export function ErrorFallback({
  error,
  resetError,
  className,
  showHomeButton = false,
  showReportButton = true,
  size = 'md',
  variant = 'card'
}: ErrorFallbackProps) {
  const appError = isAppError(error) ? error : null
  const errorCode = appError?.code
  const errorMessage = appError?.getUserMessage() || (error instanceof Error ? error.message : 'An unexpected error occurred')

  // Get appropriate icon and styling based on error type
  const getErrorInfo = () => {
    if (!appError) {
      return {
        icon: AlertTriangle,
        title: 'Unexpected Error',
        color: 'red',
        description: 'Something went wrong. Please try again.'
      }
    }

    switch (appError.code) {
      case ErrorCodes.UNAUTHORIZED:
      case ErrorCodes.FORBIDDEN:
      case ErrorCodes.SESSION_EXPIRED:
        return {
          icon: Shield,
          title: 'Access Denied',
          color: 'blue',
          description: 'You need to be logged in to access this feature.'
        }

      case ErrorCodes.PROFILE_NOT_FOUND:
      case ErrorCodes.LINK_NOT_FOUND:
      case ErrorCodes.THEME_NOT_FOUND:
        return {
          icon: FileX,
          title: 'Not Found',
          color: 'gray',
          description: 'The requested item could not be found.'
        }

      case ErrorCodes.NETWORK_ERROR:
      case ErrorCodes.CONNECTION_ERROR:
        return {
          icon: Wifi,
          title: 'Connection Error',
          color: 'orange',
          description: 'Please check your internet connection and try again.'
        }

      case ErrorCodes.DATABASE_ERROR:
      case ErrorCodes.INTERNAL_ERROR:
        return {
          icon: Server,
          title: 'Server Error',
          color: 'red',
          description: 'Our servers are experiencing issues. Please try again later.'
        }

      case ErrorCodes.VALIDATION_ERROR:
      case ErrorCodes.MISSING_REQUIRED_FIELD:
      case ErrorCodes.INVALID_USERNAME:
      case ErrorCodes.INVALID_LINK_URL:
        return {
          icon: AlertTriangle,
          title: 'Invalid Input',
          color: 'yellow',
          description: 'Please check your input and try again.'
        }

      case ErrorCodes.RATE_LIMIT_EXCEEDED:
        return {
          icon: AlertTriangle,
          title: 'Too Many Requests',
          color: 'orange',
          description: 'Please wait a moment before trying again.'
        }

      default:
        return {
          icon: AlertTriangle,
          title: 'Error',
          color: 'red',
          description: 'An error occurred while processing your request.'
        }
    }
  }

  const { icon: Icon, title, color, description } = getErrorInfo()

  const handleReport = () => {
    const errorDetails = {
      message: errorMessage,
      code: errorCode,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
      stack: error instanceof Error ? error.stack : undefined
    }

    if (navigator.clipboard) {
      navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
        .then(() => {
          alert('Error details copied to clipboard. Please share this with support.')
        })
        .catch(() => {
          console.log('Error details:', errorDetails)
          alert('Error details logged to console.')
        })
    } else {
      console.log('Error details:', errorDetails)
      alert('Error details logged to console.')
    }
  }

  const handleGoHome = () => {
    window.location.href = '/'
  }

  if (variant === 'inline') {
    return (
      <Alert className={cn(
        'border-2',
        color === 'red' && 'border-red-200 bg-red-50',
        color === 'yellow' && 'border-yellow-200 bg-yellow-50',
        color === 'blue' && 'border-blue-200 bg-blue-50',
        color === 'orange' && 'border-orange-200 bg-orange-50',
        color === 'gray' && 'border-gray-200 bg-gray-50',
        className
      )}>
        <Icon className="h-4 w-4" />
        <AlertDescription className={cn(
          color === 'red' && 'text-red-700',
          color === 'yellow' && 'text-yellow-700',
          color === 'blue' && 'text-blue-700',
          color === 'orange' && 'text-orange-700',
          color === 'gray' && 'text-gray-700'
        )}>
          {errorMessage}
        </AlertDescription>
      </Alert>
    )
  }

  if (variant === 'page') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="w-full max-w-md text-center">
          <div className={cn(
            'mx-auto mb-6 h-20 w-20 rounded-full flex items-center justify-center',
            color === 'red' && 'bg-red-100',
            color === 'yellow' && 'bg-yellow-100',
            color === 'blue' && 'bg-blue-100',
            color === 'orange' && 'bg-orange-100',
            color === 'gray' && 'bg-gray-100'
          )}>
            <Icon className={cn(
              'h-10 w-10',
              color === 'red' && 'text-red-600',
              color === 'yellow' && 'text-yellow-600',
              color === 'blue' && 'text-blue-600',
              color === 'orange' && 'text-orange-600',
              color === 'gray' && 'text-gray-600'
            )} />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
          <p className="text-gray-600 mb-2">{description}</p>
          <p className="text-sm text-gray-500 mb-6">{errorMessage}</p>

          {errorCode && process.env.NODE_ENV === 'development' && (
            <p className="text-xs font-mono text-gray-400 mb-6">
              Error Code: {errorCode}
            </p>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {resetError && (
              <Button onClick={resetError} className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
              </Button>
            )}

            <Button
              onClick={handleGoHome}
              variant={resetError ? "outline" : "default"}
              className="flex items-center space-x-2"
            >
              <Home className="h-4 w-4" />
              <span>Go Home</span>
            </Button>

            {showReportButton && (
              <Button
                onClick={handleReport}
                variant="outline"
                className={cn(
                  'flex items-center space-x-2',
                  color === 'red' && 'text-red-600 border-red-200 hover:bg-red-50',
                  color === 'yellow' && 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
                  color === 'blue' && 'text-blue-600 border-blue-200 hover:bg-blue-50',
                  color === 'orange' && 'text-orange-600 border-orange-200 hover:bg-orange-50',
                  color === 'gray' && 'text-gray-600 border-gray-200 hover:bg-gray-50'
                )}
              >
                <Bug className="h-4 w-4" />
                <span>Report</span>
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <Card className={cn(
      'border-2',
      color === 'red' && 'border-red-200 bg-red-50',
      color === 'yellow' && 'border-yellow-200 bg-yellow-50',
      color === 'blue' && 'border-blue-200 bg-blue-50',
      color === 'orange' && 'border-orange-200 bg-orange-50',
      color === 'gray' && 'border-gray-200 bg-gray-50',
      size === 'sm' && 'max-w-sm',
      size === 'md' && 'max-w-md',
      size === 'lg' && 'max-w-lg',
      className
    )}>
      <CardHeader className="text-center">
        <div className={cn(
          'mx-auto mb-4 h-12 w-12 rounded-full flex items-center justify-center',
          color === 'red' && 'bg-red-100',
          color === 'yellow' && 'bg-yellow-100',
          color === 'blue' && 'bg-blue-100',
          color === 'orange' && 'bg-orange-100',
          color === 'gray' && 'bg-gray-100'
        )}>
          <Icon className={cn(
            'h-6 w-6',
            color === 'red' && 'text-red-600',
            color === 'yellow' && 'text-yellow-600',
            color === 'blue' && 'text-blue-600',
            color === 'orange' && 'text-orange-600',
            color === 'gray' && 'text-gray-600'
          )} />
        </div>
        <CardTitle className={cn(
          size === 'sm' && 'text-lg',
          size === 'md' && 'text-xl',
          size === 'lg' && 'text-2xl'
        )}>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="text-center space-y-4">
        <div>
          <p className="text-gray-600 mb-2">{description}</p>
          <p className={cn(
            'text-sm',
            color === 'red' && 'text-red-600',
            color === 'yellow' && 'text-yellow-600',
            color === 'blue' && 'text-blue-600',
            color === 'orange' && 'text-orange-600',
            color === 'gray' && 'text-gray-600'
          )}>
            {errorMessage}
          </p>

          {errorCode && process.env.NODE_ENV === 'development' && (
            <p className={cn(
              'text-xs font-mono mt-2',
              color === 'red' && 'text-red-400',
              color === 'yellow' && 'text-yellow-400',
              color === 'blue' && 'text-blue-400',
              color === 'orange' && 'text-orange-400',
              color === 'gray' && 'text-gray-400'
            )}>
              Error Code: {errorCode}
            </p>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-2">
          {resetError && (
            <Button
              onClick={resetError}
              size={size}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Try Again</span>
            </Button>
          )}

          {showHomeButton && (
            <Button
              onClick={handleGoHome}
              variant={resetError ? "outline" : "default"}
              size={size}
              className="flex items-center space-x-2"
            >
              <Home className="h-4 w-4" />
              <span>Go Home</span>
            </Button>
          )}

          {showReportButton && (
            <Button
              onClick={handleReport}
              variant="outline"
              size={size}
              className={cn(
                'flex items-center space-x-2',
                color === 'red' && 'text-red-600 border-red-200 hover:bg-red-50',
                color === 'yellow' && 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
                color === 'blue' && 'text-blue-600 border-blue-200 hover:bg-blue-50',
                color === 'orange' && 'text-orange-600 border-orange-200 hover:bg-orange-50',
                color === 'gray' && 'text-gray-600 border-gray-200 hover:bg-gray-50'
              )}
            >
              <Bug className="h-4 w-4" />
              <span>Report</span>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}