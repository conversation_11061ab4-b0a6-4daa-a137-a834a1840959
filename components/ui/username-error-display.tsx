"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw, Wifi, Clock, Server, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { 
  USERNAME_ERROR_CODES, 
  UsernameErrorCode, 
  getUserFriendlyMessage, 
  isRetryableError,
  allowsGracefulDegradation 
} from "@/lib/constants/username-errors"

interface UsernameErrorDisplayProps {
  errorCode?: UsernameErrorCode
  errorMessage?: string
  onRetry?: () => void
  retryCount?: number
  maxRetries?: number
  isRetrying?: boolean
  showRetryButton?: boolean
  showGracefulDegradation?: boolean
  className?: string
}

const ERROR_ICONS = {
  [USERNAME_ERROR_CODES.NETWORK_ERROR]: Wifi,
  [USERNAME_ERROR_CODES.TIMEOUT_ERROR]: Clock,
  [USERNAME_ERROR_CODES.SERVER_ERROR]: Server,
  [USERNAME_ERROR_CODES.RATE_LIMITED]: AlertTriangle,
  [USERNAME_ERROR_CODES.UNKNOWN_ERROR]: AlertCircle,
} as const

export function UsernameErrorDisplay({
  errorCode,
  errorMessage,
  onRetry,
  retryCount = 0,
  maxRetries = 3,
  isRetrying = false,
  showRetryButton = true,
  showGracefulDegradation = false,
  className
}: UsernameErrorDisplayProps) {
  if (!errorCode && !errorMessage) {
    return null
  }

  const actualErrorCode = errorCode || USERNAME_ERROR_CODES.UNKNOWN_ERROR
  const displayMessage = errorMessage || getUserFriendlyMessage(actualErrorCode)
  const canRetry = isRetryableError(actualErrorCode) && retryCount < maxRetries
  const allowsDegradation = allowsGracefulDegradation(actualErrorCode)
  
  // Get appropriate icon for the error type
  const IconComponent = ERROR_ICONS[actualErrorCode as keyof typeof ERROR_ICONS] || AlertCircle

  // Determine alert variant based on error severity
  const getAlertVariant = () => {
    if (allowsDegradation) {
      return "default" // Less severe for errors that allow form submission
    }
    return "destructive" // More severe for blocking errors
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Alert variant={getAlertVariant()}>
        <IconComponent className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{displayMessage}</span>
          
          {/* Retry button for retryable errors */}
          {canRetry && showRetryButton && onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              disabled={isRetrying}
              className="ml-2 h-7 px-2 text-xs"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-1 h-3 w-3" />
                  Retry {retryCount > 0 ? `(${retryCount}/${maxRetries})` : ''}
                </>
              )}
            </Button>
          )}
        </AlertDescription>
      </Alert>

      {/* Graceful degradation message */}
      {allowsDegradation && showGracefulDegradation && (
        <p className="text-sm text-muted-foreground">
          <AlertTriangle className="inline h-3 w-3 mr-1" />
          You can still submit the form, but username availability couldn't be verified.
        </p>
      )}

      {/* Max retries reached message */}
      {!canRetry && retryCount >= maxRetries && isRetryableError(actualErrorCode) && (
        <p className="text-sm text-muted-foreground">
          Maximum retry attempts reached. Please try again later or contact support if the problem persists.
        </p>
      )}
    </div>
  )
}

/**
 * Simplified error display for inline usage
 */
export function InlineUsernameError({
  errorCode,
  errorMessage,
  className
}: Pick<UsernameErrorDisplayProps, 'errorCode' | 'errorMessage' | 'className'>) {
  if (!errorCode && !errorMessage) {
    return null
  }

  const actualErrorCode = errorCode || USERNAME_ERROR_CODES.UNKNOWN_ERROR
  const displayMessage = errorMessage || getUserFriendlyMessage(actualErrorCode)
  const allowsDegradation = allowsGracefulDegradation(actualErrorCode)

  return (
    <p 
      className={cn(
        "text-sm flex items-center gap-1",
        allowsDegradation ? "text-amber-600" : "text-destructive",
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <AlertCircle className="h-3 w-3 flex-shrink-0" />
      {displayMessage}
    </p>
  )
}