'use client'

import React from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  AlertTriangle, 
  XCircle, 
  AlertCircle, 
  Info, 
  RefreshCw, 
  Bug,
  X
} from 'lucide-react'
import { isAppError, getErrorMessage, ErrorCodes } from '@/lib/errors/app-errors'
import { cn } from '@/lib/utils'

export interface ErrorDisplayProps {
  error: unknown
  title?: string
  showRetry?: boolean
  showReport?: boolean
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  variant?: 'alert' | 'card' | 'inline'
  size?: 'sm' | 'md' | 'lg'
}

export function ErrorDisplay({
  error,
  title,
  showRetry = false,
  showReport = false,
  onRetry,
  onDismiss,
  className,
  variant = 'alert',
  size = 'md'
}: ErrorDisplayProps) {
  const errorMessage = getErrorMessage(error)
  const appError = isAppError(error) ? error : null
  const errorCode = appError?.code

  // Determine error severity and icon
  const getSeverityInfo = () => {
    if (!appError) {
      return { severity: 'error', icon: XCircle, color: 'red' }
    }

    switch (appError.code) {
      case ErrorCodes.VALIDATION_ERROR:
      case ErrorCodes.MISSING_REQUIRED_FIELD:
      case ErrorCodes.INVALID_USERNAME:
      case ErrorCodes.INVALID_LINK_URL:
        return { severity: 'warning', icon: AlertCircle, color: 'yellow' }
      
      case ErrorCodes.UNAUTHORIZED:
      case ErrorCodes.FORBIDDEN:
        return { severity: 'info', icon: Info, color: 'blue' }
      
      default:
        return { severity: 'error', icon: XCircle, color: 'red' }
    }
  }

  const { severity, icon: Icon, color } = getSeverityInfo()

  const handleReport = () => {
    const errorDetails = {
      message: errorMessage,
      code: errorCode,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined
    }

    if (navigator.clipboard) {
      navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
        .then(() => {
          alert('Error details copied to clipboard. Please share this with support.')
        })
        .catch(() => {
          console.log('Error details:', errorDetails)
          alert('Error details logged to console.')
        })
    } else {
      console.log('Error details:', errorDetails)
      alert('Error details logged to console.')
    }
  }

  if (variant === 'inline') {
    return (
      <div className={cn(
        'flex items-center space-x-2 text-sm',
        color === 'red' && 'text-red-600',
        color === 'yellow' && 'text-yellow-600',
        color === 'blue' && 'text-blue-600',
        className
      )}>
        <Icon className="h-4 w-4 flex-shrink-0" />
        <span>{errorMessage}</span>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="ml-auto text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    )
  }

  if (variant === 'card') {
    return (
      <Card className={cn(
        'border-2',
        color === 'red' && 'border-red-200 bg-red-50',
        color === 'yellow' && 'border-yellow-200 bg-yellow-50',
        color === 'blue' && 'border-blue-200 bg-blue-50',
        className
      )}>
        <CardHeader className={cn(
          'pb-3',
          size === 'sm' && 'pb-2'
        )}>
          <CardTitle className={cn(
            'flex items-center space-x-2',
            color === 'red' && 'text-red-700',
            color === 'yellow' && 'text-yellow-700',
            color === 'blue' && 'text-blue-700',
            size === 'sm' && 'text-base',
            size === 'lg' && 'text-xl'
          )}>
            <Icon className={cn(
              'flex-shrink-0',
              size === 'sm' && 'h-4 w-4',
              size === 'md' && 'h-5 w-5',
              size === 'lg' && 'h-6 w-6'
            )} />
            <span>{title || 'Error'}</span>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="ml-auto text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className={cn(
          'space-y-4',
          size === 'sm' && 'space-y-2'
        )}>
          <p className={cn(
            'text-sm',
            color === 'red' && 'text-red-600',
            color === 'yellow' && 'text-yellow-600',
            color === 'blue' && 'text-blue-600'
          )}>
            {errorMessage}
          </p>

          {errorCode && process.env.NODE_ENV === 'development' && (
            <p className={cn(
              'text-xs font-mono',
              color === 'red' && 'text-red-400',
              color === 'yellow' && 'text-yellow-400',
              color === 'blue' && 'text-blue-400'
            )}>
              Error Code: {errorCode}
            </p>
          )}

          {(showRetry || showReport) && (
            <div className="flex flex-col sm:flex-row gap-2">
              {showRetry && onRetry && (
                <Button
                  onClick={onRetry}
                  variant="outline"
                  size={size}
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Try Again</span>
                </Button>
              )}

              {showReport && (
                <Button
                  onClick={handleReport}
                  variant="outline"
                  size={size}
                  className={cn(
                    'flex items-center space-x-2',
                    color === 'red' && 'text-red-600 border-red-200 hover:bg-red-50',
                    color === 'yellow' && 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
                    color === 'blue' && 'text-blue-600 border-blue-200 hover:bg-blue-50'
                  )}
                >
                  <Bug className="h-4 w-4" />
                  <span>Report Error</span>
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // Default alert variant
  return (
    <Alert className={cn(
      color === 'red' && 'border-red-200 bg-red-50',
      color === 'yellow' && 'border-yellow-200 bg-yellow-50',
      color === 'blue' && 'border-blue-200 bg-blue-50',
      className
    )}>
      <Icon className="h-4 w-4" />
      <div className="flex-1">
        {title && (
          <AlertTitle className={cn(
            color === 'red' && 'text-red-700',
            color === 'yellow' && 'text-yellow-700',
            color === 'blue' && 'text-blue-700'
          )}>
            {title}
          </AlertTitle>
        )}
        <AlertDescription className={cn(
          color === 'red' && 'text-red-600',
          color === 'yellow' && 'text-yellow-600',
          color === 'blue' && 'text-blue-600'
        )}>
          {errorMessage}
        </AlertDescription>

        {errorCode && process.env.NODE_ENV === 'development' && (
          <AlertDescription className={cn(
            'text-xs font-mono mt-1',
            color === 'red' && 'text-red-400',
            color === 'yellow' && 'text-yellow-400',
            color === 'blue' && 'text-blue-400'
          )}>
            Error Code: {errorCode}
          </AlertDescription>
        )}

        {(showRetry || showReport) && (
          <div className="flex flex-col sm:flex-row gap-2 mt-3">
            {showRetry && onRetry && (
              <Button
                onClick={onRetry}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
              </Button>
            )}

            {showReport && (
              <Button
                onClick={handleReport}
                variant="outline"
                size="sm"
                className={cn(
                  'flex items-center space-x-2',
                  color === 'red' && 'text-red-600 border-red-200 hover:bg-red-50',
                  color === 'yellow' && 'text-yellow-600 border-yellow-200 hover:bg-yellow-50',
                  color === 'blue' && 'text-blue-600 border-blue-200 hover:bg-blue-50'
                )}
              >
                <Bug className="h-4 w-4" />
                <span>Report Error</span>
              </Button>
            )}
          </div>
        )}
      </div>

      {onDismiss && (
        <button
          onClick={onDismiss}
          className="ml-2 text-gray-400 hover:text-gray-600"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </Alert>
  )
}

// Specialized error display components
export function ValidationErrorDisplay({ 
  error, 
  onDismiss,
  className 
}: { 
  error: unknown
  onDismiss?: () => void
  className?: string 
}) {
  return (
    <ErrorDisplay
      error={error}
      title="Validation Error"
      variant="inline"
      onDismiss={onDismiss}
      className={className}
    />
  )
}

export function ServerErrorDisplay({ 
  error, 
  onRetry,
  className 
}: { 
  error: unknown
  onRetry?: () => void
  className?: string 
}) {
  return (
    <ErrorDisplay
      error={error}
      title="Server Error"
      variant="card"
      showRetry={!!onRetry}
      showReport={true}
      onRetry={onRetry}
      className={className}
    />
  )
}

export function NetworkErrorDisplay({ 
  error, 
  onRetry,
  className 
}: { 
  error: unknown
  onRetry?: () => void
  className?: string 
}) {
  return (
    <ErrorDisplay
      error={error}
      title="Connection Error"
      variant="alert"
      showRetry={!!onRetry}
      onRetry={onRetry}
      className={className}
    />
  )
}