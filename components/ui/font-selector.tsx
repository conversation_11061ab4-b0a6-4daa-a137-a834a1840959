"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Check, Type } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FontSelectorProps {
  value: string
  onChange: (font: string) => void
  label?: string
  className?: string
}

const googleFonts = [
  { name: 'Inter', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: '<PERSON><PERSON>', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Open Sans', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: '<PERSON><PERSON><PERSON>', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: '<PERSON><PERSON>', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: '<PERSON><PERSON><PERSON>', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Source Sans Pro', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: '<PERSON><PERSON><PERSON>', category: 'Sans Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Playfair Display', category: 'Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Merriweather', category: 'Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Lora', category: 'Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'PT Serif', category: 'Serif', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Fira Code', category: 'Monospace', preview: 'The quick brown fox jumps over the lazy dog' },
  { name: 'JetBrains Mono', category: 'Monospace', preview: 'The quick brown fox jumps over the lazy dog' },
]

export function FontSelector({ value, onChange, label, className }: FontSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('All')
  
  const categories = ['All', 'Sans Serif', 'Serif', 'Monospace']
  
  const filteredFonts = selectedCategory === 'All' 
    ? googleFonts 
    : googleFonts.filter(font => font.category === selectedCategory)

  const loadGoogleFont = (fontName: string) => {
    const fontUrl = `https://fonts.googleapis.com/css2?family=${fontName.replace(/\s+/g, '+')}:wght@400;500;600;700&display=swap`
    
    // Check if font is already loaded
    const existingLink = document.querySelector(`link[href="${fontUrl}"]`)
    if (existingLink) return

    // Load the font
    const link = document.createElement('link')
    link.href = fontUrl
    link.rel = 'stylesheet'
    document.head.appendChild(link)
  }

  const handleFontSelect = (fontName: string) => {
    loadGoogleFont(fontName)
    onChange(fontName)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {label && (
        <div className="flex items-center space-x-2">
          <Type className="h-4 w-4" />
          <Label>{label}</Label>
        </div>
      )}
      
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <Button
            key={category}
            type="button"
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Font List */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredFonts.map((font) => (
          <div
            key={font.name}
            className={cn(
              "p-4 border rounded-lg cursor-pointer transition-all hover:border-primary",
              value === font.name && "border-primary bg-primary/5"
            )}
            onClick={() => handleFontSelect(font.name)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="font-medium">{font.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {font.category}
                </Badge>
              </div>
              {value === font.name && (
                <Check className="h-4 w-4 text-primary" />
              )}
            </div>
            <p 
              className="text-sm text-muted-foreground"
              style={{ fontFamily: font.name }}
            >
              {font.preview}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}