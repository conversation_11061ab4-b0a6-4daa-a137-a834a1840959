import { render, screen, fireEvent } from '@testing-library/react'
import { UsernameErrorDisplay, InlineUsernameError } from '../username-error-display'
import { USERNAME_ERROR_CODES } from '@/lib/constants/username-errors'

describe('UsernameErrorDisplay', () => {
  describe('Basic rendering', () => {
    it('should render nothing when no error is provided', () => {
      const { container } = render(<UsernameErrorDisplay />)
      expect(container.firstChild).toBeNull()
    })

    it('should render error message when errorMessage is provided', () => {
      render(<UsernameErrorDisplay errorMessage="Test error message" />)
      expect(screen.getByText('Test error message')).toBeInTheDocument()
    })

    it('should render error message from error code', () => {
      render(<UsernameErrorDisplay errorCode={USERNAME_ERROR_CODES.TOO_SHORT} />)
      expect(screen.getByText('Username must be at least 3 characters long')).toBeInTheDocument()
    })

    it('should prioritize errorMessage over errorCode', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.TOO_SHORT}
          errorMessage="Custom error message"
        />
      )
      expect(screen.getByText('Custom error message')).toBeInTheDocument()
      expect(screen.queryByText('Username must be at least 3 characters long')).not.toBeInTheDocument()
    })
  })

  describe('Retry functionality', () => {
    it('should show retry button for retryable errors', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          onRetry={onRetry}
          showRetryButton={true}
        />
      )
      
      const retryButton = screen.getByRole('button', { name: /retry/i })
      expect(retryButton).toBeInTheDocument()
      expect(retryButton).not.toBeDisabled()
    })

    it('should not show retry button for non-retryable errors', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.TOO_SHORT}
          onRetry={onRetry}
          showRetryButton={true}
        />
      )
      
      expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument()
    })

    it('should call onRetry when retry button is clicked', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          onRetry={onRetry}
          showRetryButton={true}
        />
      )
      
      const retryButton = screen.getByRole('button', { name: /retry/i })
      fireEvent.click(retryButton)
      expect(onRetry).toHaveBeenCalledTimes(1)
    })

    it('should disable retry button when isRetrying is true', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          onRetry={onRetry}
          isRetrying={true}
          showRetryButton={true}
        />
      )
      
      const retryButton = screen.getByRole('button', { name: /retrying/i })
      expect(retryButton).toBeDisabled()
    })

    it('should show retry count in button text', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          onRetry={onRetry}
          retryCount={2}
          maxRetries={3}
          showRetryButton={true}
        />
      )
      
      expect(screen.getByText(/retry \(2\/3\)/i)).toBeInTheDocument()
    })

    it('should not show retry button when max retries reached', () => {
      const onRetry = jest.fn()
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          onRetry={onRetry}
          retryCount={3}
          maxRetries={3}
          showRetryButton={true}
        />
      )
      
      expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument()
      expect(screen.getByText(/maximum retry attempts reached/i)).toBeInTheDocument()
    })
  })

  describe('Graceful degradation', () => {
    it('should show graceful degradation message when enabled', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
          showGracefulDegradation={true}
        />
      )
      
      expect(screen.getByText(/you can still submit the form/i)).toBeInTheDocument()
    })

    it('should not show graceful degradation message for non-degradable errors', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.TOO_SHORT}
          showGracefulDegradation={true}
        />
      )
      
      expect(screen.queryByText(/you can still submit the form/i)).not.toBeInTheDocument()
    })

    it('should use default alert variant for degradable errors', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
        />
      )
      
      // Check that it doesn't have the destructive styling
      const alert = screen.getByRole('alert')
      expect(alert).not.toHaveClass('text-destructive')
    })

    it('should use destructive alert variant for non-degradable errors', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.TOO_SHORT}
        />
      )
      
      const alert = screen.getByRole('alert')
      // Check for destructive styling (text-destructive class should be present)
      expect(alert).toHaveClass('text-destructive')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
        />
      )
      
      expect(screen.getByRole('alert')).toBeInTheDocument()
    })

    it('should have proper icon labels', () => {
      render(
        <UsernameErrorDisplay 
          errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR}
        />
      )
      
      // The icon should be present (we can't easily test aria-label on icons in this setup)
      expect(screen.getByRole('alert')).toBeInTheDocument()
    })
  })
})

describe('InlineUsernameError', () => {
  it('should render nothing when no error is provided', () => {
    const { container } = render(<InlineUsernameError />)
    expect(container.firstChild).toBeNull()
  })

  it('should render inline error message', () => {
    render(<InlineUsernameError errorMessage="Test error" />)
    expect(screen.getByText('Test error')).toBeInTheDocument()
  })

  it('should use amber color for degradable errors', () => {
    render(<InlineUsernameError errorCode={USERNAME_ERROR_CODES.NETWORK_ERROR} />)
    const errorElement = screen.getByRole('alert')
    expect(errorElement).toHaveClass('text-amber-600')
  })

  it('should use destructive color for non-degradable errors', () => {
    render(<InlineUsernameError errorCode={USERNAME_ERROR_CODES.TOO_SHORT} />)
    const errorElement = screen.getByRole('alert')
    expect(errorElement).toHaveClass('text-destructive')
  })

  it('should have proper accessibility attributes', () => {
    render(<InlineUsernameError errorMessage="Test error" />)
    const errorElement = screen.getByRole('alert')
    expect(errorElement).toHaveAttribute('aria-live', 'polite')
  })
})