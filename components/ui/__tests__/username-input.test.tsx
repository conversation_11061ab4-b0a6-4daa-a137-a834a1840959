import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { UsernameInput } from '../username-input'

// Mock the username availability hook
const mockCheckAvailability = jest.fn()
const mockReset = jest.fn()

jest.mock('@/lib/hooks/use-username-availability', () => ({
  useUsernameAvailability: jest.fn(() => ({
    status: { checking: false },
    checkAvailability: mockCheckAvailability,
    reset: mockReset
  }))
}))

// Mock the validation schema
jest.mock('@/lib/validations', () => ({
  usernameSchema: {
    safeParse: jest.fn()
  }
}))

const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
const { usernameSchema } = require('@/lib/validations')

describe('UsernameInput', () => {
  const defaultProps = {
    value: '',
    onChange: jest.fn(),
    currentUsername: '',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Default to successful validation
    usernameSchema.safeParse.mockReturnValue({ success: true })
  })

  describe('Basic Rendering', () => {
    it('renders input field correctly', () => {
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      expect(input).toBeInTheDocument()
      expect(input).toHaveAttribute('type', 'text')
      expect(input).toHaveAttribute('autoComplete', 'username')
      expect(input).toHaveAttribute('spellCheck', 'false')
    })

    it('applies custom className', () => {
      const { container } = render(
        <UsernameInput {...defaultProps} className="custom-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('passes through input props', () => {
      render(
        <UsernameInput 
          {...defaultProps} 
          placeholder="Enter username"
          id="username-field"
        />
      )
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('placeholder', 'Enter username')
      expect(input).toHaveAttribute('id', 'username-field')
    })
  })

  describe('Value and Change Handling', () => {
    it('displays the provided value', () => {
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveValue('testuser')
    })

    it('calls onChange when input value changes', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(<UsernameInput {...defaultProps} onChange={onChange} />)
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'test')
      
      expect(onChange).toHaveBeenCalledTimes(4) // Once for each character
      // Check that the last call was with 't' (since userEvent types one character at a time)
      expect(onChange).toHaveBeenNthCalledWith(4, 't')
    })

    it('resets validation state when value changes', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} value="test" />)
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'user')
      
      expect(mockReset).toHaveBeenCalled()
    })
  })

  describe('Validation', () => {
    it('shows validation error for invalid format', async () => {
      // Create a controlled component wrapper for testing
      function TestWrapper() {
        const [value, setValue] = React.useState('')
        return <UsernameInput value={value} onChange={setValue} />
      }
      
      // Mock validation to always fail
      usernameSchema.safeParse.mockReturnValue({
        success: false,
        error: { errors: [{ message: 'Username must be at least 3 characters' }] }
      })
      
      render(<TestWrapper />)
      
      const input = screen.getByRole('textbox')
      
      // Type to trigger validation
      fireEvent.change(input, { target: { value: 'ab' } })
      
      await waitFor(() => {
        expect(screen.getByText('Username must be at least 3 characters')).toBeInTheDocument()
      })
      
      expect(screen.getByLabelText('Validation error')).toBeInTheDocument()
      expect(input).toHaveAttribute('aria-invalid', 'true')
    })

    it('does not check availability for invalid format', async () => {
      const user = userEvent.setup()
      usernameSchema.safeParse.mockReturnValue({
        success: false,
        error: { errors: [{ message: 'Invalid format' }] }
      })
      
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'ab')
      
      expect(mockCheckAvailability).not.toHaveBeenCalled()
    })

    it('checks availability for valid format', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      await user.type(input, 'validuser')
      
      // Should be called for each character that passes validation
      expect(mockCheckAvailability).toHaveBeenCalled()
      // Check that it was called with the last character
      expect(mockCheckAvailability).toHaveBeenLastCalledWith('r')
    })

    it('does not validate empty values', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} value="test" />)
      
      const input = screen.getByRole('textbox')
      await user.clear(input)
      
      expect(mockCheckAvailability).not.toHaveBeenCalled()
      expect(screen.queryByRole('status')).not.toBeInTheDocument()
    })
  })

  describe('Availability Status', () => {
    it('shows loading state during availability check', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: true },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      expect(screen.getByLabelText('Loading')).toBeInTheDocument()
      expect(screen.getByText('Checking availability...')).toBeInTheDocument()
    })

    it('shows success state when username is available', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: true },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      expect(screen.getByLabelText('Username available')).toBeInTheDocument()
      expect(screen.getByText('Username available')).toBeInTheDocument()
      expect(screen.getByText('Username available')).toHaveClass('text-green-600')
    })

    it('shows error state when username is unavailable', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: false, error: 'Username not available' },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      expect(screen.getByLabelText('Username unavailable')).toBeInTheDocument()
      expect(screen.getByText('Username not available')).toBeInTheDocument()
      expect(screen.getByText('Username not available')).toHaveClass('text-destructive')
    })

    it('shows error state for network errors', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, error: 'Network error occurred' },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      expect(screen.getByLabelText('Username unavailable')).toBeInTheDocument()
      expect(screen.getByText('Network error occurred')).toBeInTheDocument()
    })
  })

  describe('Requirements Display', () => {
    it('shows requirements when focused by default', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      expect(screen.getByRole('region', { name: 'Username requirements' })).toBeInTheDocument()
      expect(screen.getByText('Username requirements:')).toBeInTheDocument()
      expect(screen.getByText('3-30 characters long')).toBeInTheDocument()
      expect(screen.getByText('Only letters, numbers, hyphens, and underscores')).toBeInTheDocument()
      expect(screen.getByText('Cannot start or end with special characters')).toBeInTheDocument()
      expect(screen.getByText('No consecutive special characters')).toBeInTheDocument()
    })

    it('hides requirements when not focused', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      expect(screen.getByRole('region', { name: 'Username requirements' })).toBeInTheDocument()
      
      await user.tab() // Focus away
      
      expect(screen.queryByRole('region', { name: 'Username requirements' })).not.toBeInTheDocument()
    })

    it('does not show requirements when showRequirements is false', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} showRequirements={false} />)
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      expect(screen.queryByRole('region', { name: 'Username requirements' })).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-describedby', 'username-status')
      // aria-invalid should be false when there's no value or error
      expect(input).toHaveAttribute('aria-invalid', 'false')
    })

    it('updates ARIA attributes when focused with requirements', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} />)
      
      const input = screen.getByRole('textbox')
      await user.click(input)
      
      expect(input).toHaveAttribute('aria-describedby', 'username-requirements username-status')
    })

    it('sets aria-invalid to true for validation errors', async () => {
      // Create a controlled component wrapper for testing
      function TestWrapper() {
        const [value, setValue] = React.useState('')
        return <UsernameInput value={value} onChange={setValue} />
      }
      
      // Mock validation to always fail
      usernameSchema.safeParse.mockReturnValue({
        success: false,
        error: { errors: [{ message: 'Invalid format' }] }
      })
      
      render(<TestWrapper />)
      
      const input = screen.getByRole('textbox')
      
      // Type to trigger validation
      fireEvent.change(input, { target: { value: 'ab' } })
      
      await waitFor(() => {
        expect(input).toHaveAttribute('aria-invalid', 'true')
      })
    })

    it('sets aria-invalid to true for availability errors', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: false, error: 'Username not available' },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveAttribute('aria-invalid', 'true')
    })

    it('has proper status message with aria-live', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: true },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const statusMessage = screen.getByText('Checking availability...')
      expect(statusMessage).toHaveAttribute('aria-live', 'polite')
      expect(statusMessage).toHaveAttribute('id', 'username-status')
      expect(statusMessage).toHaveAttribute('role', 'status')
    })
  })

  describe('Disabled State', () => {
    it('disables input when disabled prop is true', () => {
      render(<UsernameInput {...defaultProps} disabled={true} />)
      
      const input = screen.getByRole('textbox')
      expect(input).toBeDisabled()
    })

    it('does not check availability when disabled', async () => {
      const user = userEvent.setup()
      
      render(<UsernameInput {...defaultProps} disabled={true} />)
      
      const input = screen.getByRole('textbox')
      // This should not trigger any events since input is disabled
      await user.type(input, 'test')
      
      expect(mockCheckAvailability).not.toHaveBeenCalled()
    })
  })

  describe('External Status Prop', () => {
    it('uses external status when provided', () => {
      const externalStatus = { checking: false, available: true }
      
      render(<UsernameInput {...defaultProps} value="testuser" status={externalStatus} />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-green-600')
      expect(screen.getByText('Username available')).toBeInTheDocument()
    })

    it('falls back to internal hook when no status provided', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: true },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-green-600')
      expect(screen.getByText('Username available')).toBeInTheDocument()
    })
  })

  describe('Input Styling', () => {
    it('applies error styling for validation errors', async () => {
      // Create a controlled component wrapper for testing
      function TestWrapper() {
        const [value, setValue] = React.useState('')
        return <UsernameInput value={value} onChange={setValue} />
      }
      
      // Mock validation to always fail
      usernameSchema.safeParse.mockReturnValue({
        success: false,
        error: { errors: [{ message: 'Invalid format' }] }
      })
      
      render(<TestWrapper />)
      
      const input = screen.getByRole('textbox')
      
      // Type to trigger validation
      fireEvent.change(input, { target: { value: 'ab' } })
      
      await waitFor(() => {
        expect(input.className).toContain('border-destructive')
      })
    })

    it('applies success styling when username is available', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: true },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-green-600')
    })

    it('applies error styling when username is unavailable', () => {
      useUsernameAvailability.mockReturnValue({
        status: { checking: false, available: false, error: 'Username not available' },
        checkAvailability: mockCheckAvailability,
        reset: mockReset
      })
      
      render(<UsernameInput {...defaultProps} value="testuser" />)
      
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-destructive')
    })
  })
})