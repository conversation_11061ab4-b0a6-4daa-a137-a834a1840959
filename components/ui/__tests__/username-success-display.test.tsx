import { render, screen } from '@testing-library/react'
import { UsernameSuccessDisplay, UsernameSuccessIcon } from '../username-success-display'

describe('UsernameSuccessDisplay', () => {
  describe('Available status', () => {
    it('should render available message with check icon', () => {
      render(<UsernameSuccessDisplay type="available" />)
      expect(screen.getByText('Username is available')).toBeInTheDocument()
      expect(screen.getByRole('status')).toHaveClass('text-green-600')
    })

    it('should render custom available message', () => {
      render(<UsernameSuccessDisplay type="available" message="Custom available message" />)
      expect(screen.getByText('Custom available message')).toBeInTheDocument()
    })
  })

  describe('Checking status', () => {
    it('should render checking message with clock icon', () => {
      render(<UsernameSuccessDisplay type="checking" />)
      expect(screen.getByText('Checking availability...')).toBeInTheDocument()
      expect(screen.getByRole('status')).toHaveClass('text-blue-600')
    })

    it('should have pulsing animation for checking status', () => {
      render(<UsernameSuccessDisplay type="checking" />)
      const icon = screen.getByRole('status').querySelector('svg')
      expect(icon).toHaveClass('animate-pulse')
    })
  })

  describe('Cached status', () => {
    it('should render cached message with zap icon', () => {
      render(<UsernameSuccessDisplay type="cached" />)
      expect(screen.getByText('Username is available (cached)')).toBeInTheDocument()
      expect(screen.getByRole('status')).toHaveClass('text-green-600')
    })
  })

  describe('Icon display', () => {
    it('should show icon by default', () => {
      render(<UsernameSuccessDisplay type="available" />)
      const statusElement = screen.getByRole('status')
      expect(statusElement.querySelector('svg')).toBeInTheDocument()
    })

    it('should hide icon when showIcon is false', () => {
      render(<UsernameSuccessDisplay type="available" showIcon={false} />)
      const statusElement = screen.getByRole('status')
      expect(statusElement.querySelector('svg')).not.toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<UsernameSuccessDisplay type="available" />)
      const statusElement = screen.getByRole('status')
      expect(statusElement).toHaveAttribute('aria-live', 'polite')
    })

    it('should apply custom className', () => {
      render(<UsernameSuccessDisplay type="available" className="custom-class" />)
      expect(screen.getByRole('status')).toHaveClass('custom-class')
    })
  })
})

describe('UsernameSuccessIcon', () => {
  describe('Icon rendering', () => {
    it('should render check icon for available status', () => {
      render(<UsernameSuccessIcon type="available" />)
      const icon = screen.getByLabelText('Username is available')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-green-600')
    })

    it('should render clock icon for checking status', () => {
      render(<UsernameSuccessIcon type="checking" />)
      const icon = screen.getByLabelText('Checking availability...')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-blue-600')
      expect(icon).toHaveClass('animate-pulse')
    })

    it('should render zap icon for cached status', () => {
      render(<UsernameSuccessIcon type="cached" />)
      const icon = screen.getByLabelText('Username is available (cached)')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-green-600')
    })
  })

  describe('Styling', () => {
    it('should apply default size classes', () => {
      render(<UsernameSuccessIcon type="available" />)
      const icon = screen.getByLabelText('Username is available')
      expect(icon).toHaveClass('h-4', 'w-4')
    })

    it('should apply custom className', () => {
      render(<UsernameSuccessIcon type="available" className="custom-icon-class" />)
      const icon = screen.getByLabelText('Username is available')
      expect(icon).toHaveClass('custom-icon-class')
    })
  })

  describe('Accessibility', () => {
    it('should have proper aria-label for each status type', () => {
      const { rerender } = render(<UsernameSuccessIcon type="available" />)
      expect(screen.getByLabelText('Username is available')).toBeInTheDocument()

      rerender(<UsernameSuccessIcon type="checking" />)
      expect(screen.getByLabelText('Checking availability...')).toBeInTheDocument()

      rerender(<UsernameSuccessIcon type="cached" />)
      expect(screen.getByLabelText('Username is available (cached)')).toBeInTheDocument()
    })
  })
})