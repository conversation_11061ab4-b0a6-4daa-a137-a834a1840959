"use client"

import React from 'react'
import { UsernameInput } from '../username-input'

// This is a demo component to test the UsernameInput in a realistic scenario
export function UsernameInputDemo() {
  const [username, setUsername] = React.useState('')
  const [status, setStatus] = React.useState<{ checking: boolean; available?: boolean; error?: string }>({ checking: false })

  return (
    <div className="max-w-md mx-auto p-6 space-y-4">
      <h2 className="text-xl font-semibold">Username Input Demo</h2>
      
      <div className="space-y-2">
        <label htmlFor="username-demo" className="text-sm font-medium">
          Choose a username
        </label>
        <UsernameInput
          id="username-demo"
          value={username}
          onChange={setUsername}
          onStatusChange={setStatus}
          placeholder="Enter your username"
        />
      </div>

      <div className="text-sm text-muted-foreground">
        <p>Current value: "{username}"</p>
        <p>Status: {JSON.stringify(status, null, 2)}</p>
      </div>

      <div className="space-y-2">
        <h3 className="font-medium">Test Cases:</h3>
        <ul className="text-sm space-y-1">
          <li>• Try typing "ab" (too short)</li>
          <li>• Try typing "user@name" (invalid characters)</li>
          <li>• Try typing "_username" (starts with special char)</li>
          <li>• Try typing "user__name" (consecutive special chars)</li>
          <li>• Try typing "validusername" (should check availability)</li>
        </ul>
      </div>
    </div>
  )
}