import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { UsernameInput } from '../username-input'
import { USERNAME_ERROR_CODES } from '@/lib/constants/username-errors'

// Mock the username availability hook
const mockCheckAvailability = jest.fn()
const mockReset = jest.fn()
const mockRetry = jest.fn()

jest.mock('@/lib/hooks/use-username-availability', () => ({
  useUsernameAvailability: () => ({
    status: {
      checking: false,
      available: undefined,
      error: undefined,
      errorCode: undefined,
      cached: false,
      retryCount: 0
    },
    checkAvailability: mockCheckAvailability,
    reset: mockReset,
    retry: mockRetry,
    canRetry: false,
    maxRetries: 3
  })
}))

// Mock fetch for API calls
global.fetch = jest.fn()

describe('UsernameInput Enhanced Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Error Display', () => {
    it('should show validation errors with proper styling', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(
        <UsernameInput 
          value="" 
          onChange={onChange}
          showRetryButton={true}
          showGracefulDegradation={true}
        />
      )
      
      const input = screen.getByRole('textbox')
      
      // Type invalid username (too short)
      await user.type(input, 'ab')
      
      await waitFor(() => {
        expect(screen.getByText(/must be at least 3 characters/i)).toBeInTheDocument()
      })
      
      // Input should have error styling
      expect(input).toHaveClass('border-destructive')
    })

    it('should show network error with retry button', () => {
      const onChange = jest.fn()
      
      // Mock hook to return network error
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: undefined,
          error: 'Network error. Please check your connection and try again.',
          errorCode: USERNAME_ERROR_CODES.NETWORK_ERROR,
          cached: false,
          retryCount: 1
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: true,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
          showRetryButton={true}
          showGracefulDegradation={true}
        />
      )
      
      // Should show error message
      expect(screen.getByText(/network error/i)).toBeInTheDocument()
      
      // Should show retry button
      expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
      
      // Should show graceful degradation message
      expect(screen.getByText(/you can still submit the form/i)).toBeInTheDocument()
      
      // Input should have warning styling (amber) for degradable errors
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-amber-500')
    })

    it('should handle retry functionality', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      // Mock hook to return retryable error
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: undefined,
          error: 'Request timed out. Please try again.',
          errorCode: USERNAME_ERROR_CODES.TIMEOUT_ERROR,
          cached: false,
          retryCount: 1
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: true,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
          showRetryButton={true}
        />
      )
      
      const retryButton = screen.getByRole('button', { name: /retry/i })
      await user.click(retryButton)
      
      expect(mockRetry).toHaveBeenCalledTimes(1)
    })

    it('should show max retries reached message', () => {
      const onChange = jest.fn()
      
      // Mock hook to return max retries reached
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: undefined,
          error: 'Network error. Please check your connection and try again.',
          errorCode: USERNAME_ERROR_CODES.NETWORK_ERROR,
          cached: false,
          retryCount: 3
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
          showRetryButton={true}
        />
      )
      
      // Should not show retry button
      expect(screen.queryByRole('button', { name: /retry/i })).not.toBeInTheDocument()
      
      // Should show max retries message
      expect(screen.getByText(/maximum retry attempts reached/i)).toBeInTheDocument()
    })
  })

  describe('Success Display', () => {
    it('should show available status with success styling', () => {
      const onChange = jest.fn()
      
      // Mock hook to return available status
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: true,
          error: undefined,
          errorCode: undefined,
          cached: false,
          retryCount: 0
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
        />
      )
      
      // Should show success message
      expect(screen.getByText(/username is available/i)).toBeInTheDocument()
      
      // Input should have success styling
      const input = screen.getByRole('textbox')
      expect(input).toHaveClass('border-green-600')
    })

    it('should show cached status with appropriate icon', () => {
      const onChange = jest.fn()
      
      // Mock hook to return cached status
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: true,
          error: undefined,
          errorCode: undefined,
          cached: true,
          retryCount: 0
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
        />
      )
      
      // Should show cached message
      expect(screen.getByText(/username is available \(cached\)/i)).toBeInTheDocument()
    })

    it('should show checking status with loading animation', () => {
      const onChange = jest.fn()
      
      // Mock hook to return checking status
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: true,
          available: undefined,
          error: undefined,
          errorCode: undefined,
          cached: false,
          retryCount: 0
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
        />
      )
      
      // Should show checking message
      expect(screen.getByText(/checking availability/i)).toBeInTheDocument()
      
      // Should have loading icon with animation
      const statusElement = screen.getByRole('status')
      const icon = statusElement.querySelector('svg')
      expect(icon).toHaveClass('animate-pulse')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for error states', () => {
      const onChange = jest.fn()
      
      // Mock hook to return error status
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: false,
          error: 'This username is already taken',
          errorCode: USERNAME_ERROR_CODES.USERNAME_TAKEN,
          cached: false,
          retryCount: 0
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
        />
      )
      
      const input = screen.getByRole('textbox')
      
      // Should have aria-invalid set to true for errors
      expect(input).toHaveAttribute('aria-invalid', 'true')
      
      // Should have aria-describedby pointing to status
      expect(input).toHaveAttribute('aria-describedby', 'username-status')
      
      // Status should have proper role and aria-live
      const statusElement = screen.getByRole('alert')
      expect(statusElement).toHaveAttribute('aria-live', 'polite')
    })

    it('should have proper ARIA attributes for success states', () => {
      const onChange = jest.fn()
      
      // Mock hook to return success status
      const mockHook = require('@/lib/hooks/use-username-availability')
      mockHook.useUsernameAvailability = jest.fn(() => ({
        status: {
          checking: false,
          available: true,
          error: undefined,
          errorCode: undefined,
          cached: false,
          retryCount: 0
        },
        checkAvailability: mockCheckAvailability,
        reset: mockReset,
        retry: mockRetry,
        canRetry: false,
        maxRetries: 3
      }))
      
      render(
        <UsernameInput 
          value="testuser" 
          onChange={onChange}
        />
      )
      
      const input = screen.getByRole('textbox')
      
      // Should have aria-invalid set to false for success
      expect(input).toHaveAttribute('aria-invalid', 'false')
      
      // Status should have proper role and aria-live
      const statusElement = screen.getByRole('status')
      expect(statusElement).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('Requirements Display', () => {
    it('should show requirements when focused', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(
        <UsernameInput 
          value="" 
          onChange={onChange}
          showRequirements={true}
        />
      )
      
      const input = screen.getByRole('textbox')
      
      // Requirements should not be visible initially
      expect(screen.queryByText(/username requirements/i)).not.toBeInTheDocument()
      
      // Focus the input
      await user.click(input)
      
      // Requirements should now be visible
      expect(screen.getByText(/username requirements/i)).toBeInTheDocument()
      expect(screen.getByText(/3-30 characters long/i)).toBeInTheDocument()
      expect(screen.getByText(/only letters, numbers, hyphens, and underscores/i)).toBeInTheDocument()
    })

    it('should hide requirements when not focused', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(
        <UsernameInput 
          value="" 
          onChange={onChange}
          showRequirements={true}
        />
      )
      
      const input = screen.getByRole('textbox')
      
      // Focus and then blur
      await user.click(input)
      expect(screen.getByText(/username requirements/i)).toBeInTheDocument()
      
      await user.tab() // This will blur the input
      
      // Requirements should be hidden
      expect(screen.queryByText(/username requirements/i)).not.toBeInTheDocument()
    })
  })
})