"use client"

import { useEffect } from 'react'
import { syncStoresWithServer, subscribeToStoreChanges } from '@/lib/stores/sync-utils'
import type { User, Profile, Link, ProfileTheme } from '@/lib/types'

interface StoreProviderProps {
  children: React.ReactNode
  initialData?: {
    user?: User | null
    profile?: Profile | null
    links?: Link[]
    theme?: {
      theme: ProfileTheme
      backgroundType: 'color' | 'gradient' | 'image'
      backgroundValue: string
    }
  }
}

export function StoreProvider({ children, initialData }: StoreProviderProps) {
  useEffect(() => {
    // Sync stores with initial server data
    if (initialData) {
      syncStoresWithServer(initialData)
    }

    // Subscribe to store changes in development
    subscribeToStoreChanges()
  }, [initialData])

  return <>{children}</>
}