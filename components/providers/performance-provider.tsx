"use client"

import { useEffect } from 'react'
import { PerformanceOptimizer } from '@/lib/utils/performance-optimizer'

export function PerformanceProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Initialize performance monitoring
    PerformanceOptimizer.markStart('app-initialization')
    
    // Preload critical resources
    PerformanceOptimizer.preloadResource('/fonts/inter.woff2', 'font', 'font/woff2')
    
    // Set up lazy loading for images
    PerformanceOptimizer.lazyLoadImages()
    
    // Monitor Core Web Vitals in production
    if (process.env.NODE_ENV === 'production') {
      PerformanceOptimizer.getCoreWebVitals().then((vitals) => {
        // Log vitals to analytics service
        if (vitals.lcp && vitals.lcp > 2500) {
          console.warn('Poor LCP detected:', vitals.lcp)
        }
        if (vitals.fid && vitals.fid > 100) {
          console.warn('Poor FID detected:', vitals.fid)
        }
        if (vitals.cls && vitals.cls > 0.1) {
          console.warn('Poor CLS detected:', vitals.cls)
        }
      })
    }
    
    return () => {
      PerformanceOptimizer.markEnd('app-initialization')
    }
  }, [])

  return <>{children}</>
}