"use client"

import { useEffect } from 'react'
import { trackProfileViewClient } from '@/lib/actions/analytics'

interface ProfileViewTrackerProps {
  profileId: string
}

export function ProfileViewTracker({ profileId }: ProfileViewTrackerProps) {
  useEffect(() => {
    // Track the profile view when the component mounts
    const trackView = async () => {
      try {
        await trackProfileViewClient(profileId)
      } catch (error) {
        console.error('Failed to track profile view:', error)
      }
    }

    // Use a small delay to ensure the page has loaded
    const timeoutId = setTimeout(trackView, 1000)

    return () => clearTimeout(timeoutId)
  }, [profileId])

  // This component doesn't render anything visible
  return null
}