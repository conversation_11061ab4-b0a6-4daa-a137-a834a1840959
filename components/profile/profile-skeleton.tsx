import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'

export function ProfileSkeleton() {
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8 max-w-md">
        <div className="text-center space-y-6">
          {/* Profile Header Skeleton */}
          <div className="space-y-4">
            {/* Profile Image Skeleton */}
            <Skeleton className="w-24 h-24 mx-auto rounded-full" />

            {/* Name Skeleton */}
            <Skeleton className="h-8 w-48 mx-auto" />

            {/* Bio Skeleton */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-64 mx-auto" />
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
          </div>

          {/* Links Skeleton */}
          <div className="space-y-3 w-full">
            {Array.from({ length: 5 }).map((_, i) => (
              <Card key={i} className="p-4">
                <div className="flex items-center space-x-3">
                  <Skeleton className="w-6 h-6 rounded" />
                  <Skeleton className="h-5 flex-1" />
                </div>
              </Card>
            ))}
          </div>

          {/* Footer Skeleton */}
          <div className="pt-8 space-y-2">
            <Skeleton className="h-5 w-24 mx-auto" />
            <Skeleton className="h-4 w-32 mx-auto" />
          </div>
        </div>
      </div>
    </div>
  )
}

export function ProfileLinkSkeleton() {
  return (
    <Card className="p-4 animate-pulse">
      <div className="flex items-center space-x-3">
        <Skeleton className="w-6 h-6 rounded" />
        <Skeleton className="h-5 flex-1" />
      </div>
    </Card>
  )
}