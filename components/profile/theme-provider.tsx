"use client"

import { useEffect } from 'react'
import { cn } from '@/lib/utils'
import type { ProfileTheme } from '@/lib/types'

interface ThemeProviderProps {
  theme: ProfileTheme
  backgroundType: string
  backgroundValue: string
  children: React.ReactNode
  className?: string
}

export function ThemeProvider({ 
  theme, 
  backgroundType, 
  backgroundValue, 
  children, 
  className 
}: ThemeProviderProps) {
  useEffect(() => {
    // Apply the font family to the document
    if (theme.fontFamily) {
      document.documentElement.style.setProperty('--profile-font-family', theme.fontFamily)
    }

    // Cleanup function to reset font when component unmounts
    return () => {
      document.documentElement.style.removeProperty('--profile-font-family')
    }
  }, [theme.fontFamily])

  const getBackgroundStyle = (): React.CSSProperties => {
    switch (backgroundType) {
      case 'color':
        return { backgroundColor: backgroundValue }
      case 'gradient':
        return { background: backgroundValue }
      case 'image':
        return { 
          backgroundImage: `url(${backgroundValue})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }
      default:
        return { backgroundColor: theme.backgroundColor }
    }
  }

  return (
    <div 
      className={cn("min-h-screen w-full relative", className)}
      style={{
        ...getBackgroundStyle(),
        fontFamily: theme.fontFamily,
        color: theme.textColor
      }}
    >
      {/* Overlay for better text readability on images */}
      {backgroundType === 'image' && (
        <div className="absolute inset-0 bg-black/20 pointer-events-none" />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}