"use client"

import { ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LinkIcon } from './link-icon'
import { ScreenReaderOnly } from '@/components/ui/skip-links'
import { trackLinkClickClient } from '@/lib/actions/analytics'
import type { Link } from '@/lib/types'
import type { ProfileTheme } from '@/lib/types'

interface ProfileLinkCardProps {
  link: Link
  theme: ProfileTheme
  className?: string
  index?: number
  total?: number
}

export function ProfileLinkCard({ link, theme, className, index, total }: ProfileLinkCardProps) {
  const handleClick = async () => {
    // Track the click
    try {
      await trackLinkClickClient(link.id)
    } catch (error) {
      console.error('Failed to track link click:', error)
    }
    
    // Open the link
    window.open(link.url, '_blank', 'noopener,noreferrer')
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleClick()
    }
  }

  return (
    <button
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      className={cn(
        "w-full flex items-center justify-between p-3 sm:p-4 rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 group min-h-[44px] touch-manipulation active:scale-95",
        // Enhanced mobile responsiveness
        "text-left break-words hyphens-auto",
        // Better focus states
        "focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-opacity-75",
        // High contrast mode support
        "@media (prefers-contrast: high) { border: 2px solid currentColor }",
        className
      )}
      style={{ 
        backgroundColor: theme.primaryColor,
        color: theme.backgroundColor,
        '--tw-ring-color': theme.primaryColor,
        '--tw-ring-opacity': '0.5'
      }}
      aria-label={`Visit ${link.title}. Opens in new tab.${index && total ? ` Link ${index} of ${total}.` : ''}`}
      aria-describedby={`link-description-${link.id}`}
    >
      <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
        {/* Link Icon */}
        <LinkIcon 
          icon={link.icon} 
          title={link.title}
          className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0"
          style={{ color: theme.backgroundColor }}
          aria-hidden="true"
        />
        
        {/* Link Title */}
        <span 
          className="font-medium text-left truncate text-sm sm:text-base"
          style={{ color: theme.backgroundColor }}
          id={`link-description-${link.id}`}
        >
          {link.title}
        </span>
      </div>

      {/* External Link Icon */}
      <ExternalLink 
        className="h-4 w-4 opacity-70 group-hover:opacity-100 transition-opacity flex-shrink-0 ml-2" 
        style={{ color: theme.backgroundColor }}
        aria-hidden="true"
      />
      <ScreenReaderOnly>Opens in new tab</ScreenReaderOnly>
    </button>
  )
}