"use client"

import { 
  ExternalLink, 
  Instagram, 
  Twitter, 
  Github, 
  Linkedin, 
  Youtube, 
  Facebook,
  Globe,
  Mail,
  Phone,
  MapPin,
  Music,
  Camera,
  Video,
  ShoppingBag,
  BookOpen,
  Gamepad2,
  Heart,
  Star,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface LinkIconProps {
  icon?: string | null
  title: string
  className?: string
  style?: React.CSSProperties
}

// Map of icon names to Lucide React components
const iconMap = {
  // Social Media
  instagram: Instagram,
  twitter: Twitter,
  github: Github,
  linkedin: Linkedin,
  youtube: Youtube,
  facebook: Facebook,
  
  // General
  website: Globe,
  globe: Globe,
  external: ExternalLink,
  link: ExternalLink,
  
  // Contact
  email: Mail,
  mail: Mail,
  phone: Phone,
  location: MapPin,
  
  // Content
  music: Music,
  camera: Camera,
  video: Video,
  blog: BookOpen,
  book: BookOpen,
  
  // Business
  shop: ShoppingBag,
  store: ShoppingBag,
  
  // Entertainment
  gaming: Gamepad2,
  games: Gamepad2,
  
  // Misc
  heart: Heart,
  star: Star,
  zap: Zap,
}

export function LinkIcon({ icon, title, className, style }: LinkIconProps) {
  // Try to get icon from the map
  let IconComponent = ExternalLink // Default icon
  
  if (icon) {
    const normalizedIcon = icon.toLowerCase().trim()
    IconComponent = iconMap[normalizedIcon as keyof typeof iconMap] || ExternalLink
  } else {
    // Try to infer icon from title
    const normalizedTitle = title.toLowerCase()
    
    // Check for common social media platforms and services
    if (normalizedTitle.includes('instagram') || normalizedTitle.includes('insta')) {
      IconComponent = Instagram
    } else if (normalizedTitle.includes('twitter') || normalizedTitle.includes('x.com')) {
      IconComponent = Twitter
    } else if (normalizedTitle.includes('github')) {
      IconComponent = Github
    } else if (normalizedTitle.includes('linkedin')) {
      IconComponent = Linkedin
    } else if (normalizedTitle.includes('youtube')) {
      IconComponent = Youtube
    } else if (normalizedTitle.includes('facebook')) {
      IconComponent = Facebook
    } else if (normalizedTitle.includes('email') || normalizedTitle.includes('mail') || normalizedTitle.includes('@')) {
      IconComponent = Mail
    } else if (normalizedTitle.includes('phone') || normalizedTitle.includes('call')) {
      IconComponent = Phone
    } else if (normalizedTitle.includes('website') || normalizedTitle.includes('site')) {
      IconComponent = Globe
    } else if (normalizedTitle.includes('music') || normalizedTitle.includes('spotify') || normalizedTitle.includes('soundcloud')) {
      IconComponent = Music
    } else if (normalizedTitle.includes('shop') || normalizedTitle.includes('store') || normalizedTitle.includes('buy')) {
      IconComponent = ShoppingBag
    } else if (normalizedTitle.includes('blog') || normalizedTitle.includes('article')) {
      IconComponent = BookOpen
    } else if (normalizedTitle.includes('video') || normalizedTitle.includes('vimeo')) {
      IconComponent = Video
    } else if (normalizedTitle.includes('photo') || normalizedTitle.includes('camera') || normalizedTitle.includes('gallery')) {
      IconComponent = Camera
    } else if (normalizedTitle.includes('game') || normalizedTitle.includes('gaming')) {
      IconComponent = Gamepad2
    }
  }

  return (
    <IconComponent 
      className={cn("h-5 w-5", className)} 
      style={style}
      aria-hidden="true"
    />
  )
}