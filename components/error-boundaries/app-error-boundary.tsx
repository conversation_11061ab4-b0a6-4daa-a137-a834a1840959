'use client'

import React, { Component, ErrorIn<PERSON>, ReactNode } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>f<PERSON><PERSON><PERSON>, Bug, Home } from 'lucide-react'
import { errorLogger } from '@/lib/utils/error-logger'
import { ErrorFactory } from '@/lib/errors/app-errors'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  level?: 'app' | 'page' | 'component'
  context?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export class AppErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `app-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    return {
      hasError: true,
      error,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = this.state.errorId || `app-error-${Date.now()}`
    const level = this.props.level || 'component'
    const context = this.props.context || 'unknown'

    // Create structured error for logging
    const appError = ErrorFactory.internalError(
      `${level} error in ${context}: ${error.message}`,
      error
    )

    // Log the error with context
    errorLogger.error(`Error boundary caught ${level} error`, {
      errorId,
      context,
      level,
      componentStack: errorInfo.componentStack,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined
    }, appError)

    // Update state with error info
    this.setState({
      errorInfo,
      errorId
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report to external monitoring
    if (typeof window !== 'undefined') {
      // Report to monitoring service
      this.reportToMonitoring(appError, errorId, errorInfo)
    }
  }

  private reportToMonitoring(error: Error, errorId: string, errorInfo: ErrorInfo) {
    // Report to external monitoring service
    if ((window as any).reportError) {
      (window as any).reportError({
        error,
        errorId,
        context: {
          level: this.props.level,
          context: this.props.context,
          componentStack: errorInfo.componentStack
        }
      })
    }

    // Send to monitoring endpoint
    fetch('/api/monitoring/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        level: this.props.level,
        context: this.props.context,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })
    }).catch(err => {
      console.error('Failed to report error:', err)
    })
  }

  handleRetry = () => {
    errorLogger.info('User initiated error boundary retry', {
      errorId: this.state.errorId,
      context: this.props.context,
      level: this.props.level
    })

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    })
  }

  handleReportError = () => {
    if (this.state.error && this.state.errorId) {
      errorLogger.info('User reported error', {
        errorId: this.state.errorId,
        context: this.props.context
      })

      const errorDetails = {
        errorId: this.state.errorId,
        context: this.props.context,
        level: this.props.level,
        error: this.state.error.message,
        stack: this.state.error.stack,
        componentStack: this.state.errorInfo?.componentStack,
        url: typeof window !== 'undefined' ? window.location.href : undefined,
        timestamp: new Date().toISOString()
      }

      if (navigator.clipboard) {
        navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
          .then(() => {
            alert('Error details copied to clipboard. Please share this with support.')
          })
          .catch(() => {
            console.log('Error details:', errorDetails)
            alert('Error details logged to console. Please check the browser console.')
          })
      } else {
        console.log('Error details:', errorDetails)
        alert('Error details logged to console. Please check the browser console.')
      }
    }
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Different UI based on error level
      const level = this.props.level || 'component'
      
      if (level === 'app') {
        return (
          <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
            <Card className="w-full max-w-md border-red-200 bg-red-50">
              <CardHeader className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
                <CardTitle className="text-xl text-red-700">Application Error</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-red-600 mb-2">
                    Something went wrong with the application.
                  </p>
                  <p className="text-xs text-red-500">
                    {this.state.error?.message || 'An unexpected error occurred.'}
                  </p>
                  {this.state.errorId && (
                    <p className="text-xs text-red-400 mt-2 font-mono">
                      Error ID: {this.state.errorId}
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-2">
                  <Button
                    onClick={this.handleGoHome}
                    className="w-full"
                  >
                    <Home className="h-4 w-4 mr-2" />
                    Go to Home
                  </Button>
                  
                  <div className="flex gap-2">
                    <Button
                      onClick={this.handleRetry}
                      variant="outline"
                      size="sm"
                      className="flex-1"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Again
                    </Button>

                    <Button
                      onClick={this.handleReportError}
                      variant="outline"
                      size="sm"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Bug className="h-4 w-4 mr-2" />
                      Report
                    </Button>
                  </div>
                </div>

                {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                  <details className="mt-4">
                    <summary className="text-xs text-red-500 cursor-pointer hover:text-red-700">
                      Developer Details (Development Only)
                    </summary>
                    <div className="mt-2 p-3 bg-red-100 rounded text-xs font-mono text-red-700 overflow-auto max-h-40">
                      <div className="mb-2">
                        <strong>Error:</strong> {this.state.error?.message}
                      </div>
                      <div className="mb-2">
                        <strong>Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs">
                          {this.state.error?.stack}
                        </pre>
                      </div>
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    </div>
                  </details>
                )}
              </CardContent>
            </Card>
          </div>
        )
      }

      // Page or component level error
      return (
        <Card className="border-red-200 bg-red-50 m-4">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              <span>
                {level === 'page' ? 'Page Error' : 'Component Error'}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-red-600">
              <p className="font-medium mb-2">
                {level === 'page' 
                  ? 'Unable to load this page' 
                  : `Error in ${this.props.context || 'component'}`
                }
              </p>
              <p className="text-red-500">
                {this.state.error?.message || 'An unexpected error occurred.'}
              </p>
              {this.state.errorId && (
                <p className="text-xs text-red-400 mt-2 font-mono">
                  Error ID: {this.state.errorId}
                </p>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
              </Button>

              <Button
                onClick={this.handleReportError}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2 text-red-600 border-red-200 hover:bg-red-50"
              >
                <Bug className="h-4 w-4" />
                <span>Report Error</span>
              </Button>

              {level === 'page' && (
                <Button
                  onClick={this.handleGoHome}
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Home className="h-4 w-4" />
                  <span>Go Home</span>
                </Button>
              )}
            </div>

            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details className="mt-4">
                <summary className="text-xs text-red-500 cursor-pointer hover:text-red-700">
                  Developer Details (Development Only)
                </summary>
                <div className="mt-2 p-3 bg-red-100 rounded text-xs font-mono text-red-700 overflow-auto max-h-40">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error?.message}
                  </div>
                  <div className="mb-2">
                    <strong>Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">
                      {this.state.error?.stack}
                    </pre>
                  </div>
                  <div>
                    <strong>Component Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </div>
                </div>
              </details>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Higher-order component for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  context: string,
  level: 'app' | 'page' | 'component' = 'component'
) {
  const WithErrorBoundary = (props: P) => {
    return (
      <AppErrorBoundary context={context} level={level}>
        <WrappedComponent {...props} />
      </AppErrorBoundary>
    )
  }

  WithErrorBoundary.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`
  
  return WithErrorBoundary
}

// Specialized error boundaries for different contexts
export const PageErrorBoundary = ({ children, context }: { children: ReactNode; context?: string }) => (
  <AppErrorBoundary level="page" context={context}>
    {children}
  </AppErrorBoundary>
)

export const ComponentErrorBoundary = ({ children, context }: { children: ReactNode; context?: string }) => (
  <AppErrorBoundary level="component" context={context}>
    {children}
  </AppErrorBoundary>
)