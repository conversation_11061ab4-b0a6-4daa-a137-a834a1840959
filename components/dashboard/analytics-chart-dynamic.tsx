"use client"

import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Dynamically import the analytics chart to reduce initial bundle size
const AnalyticsChart = dynamic(
  () => import('./analytics-chart').then(mod => ({ default: mod.AnalyticsChart })),
  {
    loading: () => (
      <Card>
        <CardHeader>
          <CardTitle>Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    ),
    ssr: false, // Disable SSR for charts to avoid hydration issues
  }
)

export { AnalyticsChart as DynamicAnalyticsChart }