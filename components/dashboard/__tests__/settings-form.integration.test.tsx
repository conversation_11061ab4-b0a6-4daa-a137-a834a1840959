import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { useRouter } from 'next/navigation'
import { signOut } from 'next-auth/react'
import { SettingsForm } from '../settings-form'
import { updateUsername, exportUserData, deleteAccount } from '@/lib/actions/account'

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  signOut: jest.fn(),
}))

jest.mock('@/lib/actions/account', () => ({
  updateUsername: jest.fn(),
  exportUserData: jest.fn(),
  deleteAccount: jest.fn(),
}))

jest.mock('@/lib/hooks/use-username-availability', () => ({
  useUsernameAvailability: jest.fn(),
}))

// Mock the validation schema to prevent errors in tests
jest.mock('@/lib/validations', () => ({
  usernameSchema: {
    safeParse: jest.fn(() => ({ success: true })),
  },
}))

const mockRouter = {
  refresh: jest.fn(),
  push: jest.fn(),
}

const mockUser = {
  id: '1',
  email: '<EMAIL>',
  username: 'testuser',
  displayName: 'Test User',
  bio: 'Test bio',
  profileImage: null,
}

const mockUsernameHook = {
  status: {
    checking: false,
    available: undefined,
    error: undefined,
    cached: false,
  },
  checkAvailability: jest.fn(),
  reset: jest.fn(),
  retry: jest.fn(),
}

const mockUpdateUsername = updateUsername as jest.MockedFunction<typeof updateUsername>
const mockExportUserData = exportUserData as jest.MockedFunction<typeof exportUserData>
const mockDeleteAccount = deleteAccount as jest.MockedFunction<typeof deleteAccount>
const mockSignOut = signOut as jest.MockedFunction<typeof signOut>

describe('SettingsForm Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue(mockUsernameHook)
    
    mockUpdateUsername.mockResolvedValue({ success: true })
    mockExportUserData.mockResolvedValue({ success: true, data: {} })
    mockDeleteAccount.mockResolvedValue({ success: true })
    mockSignOut.mockResolvedValue(undefined)
  })

  describe('Username Update Flow', () => {
    it('should render settings form with username input', () => {
      render(<SettingsForm user={mockUser} />)

      // Should show username input
      expect(screen.getByDisplayValue('testuser')).toBeInTheDocument()
      
      // Should show update button
      expect(screen.getByRole('button', { name: /update/i })).toBeInTheDocument()
      
      // Should show current profile URL
      expect(screen.getByText('/testuser')).toBeInTheDocument()
    })

    it('should prevent form submission when username is unavailable', async () => {
      // Mock unavailable username
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: false,
          error: undefined,
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      const updateButton = screen.getByRole('button', { name: /update/i })

      // Update button should be disabled when username is unavailable
      expect(updateButton).toBeDisabled()
    })

    it('should allow form submission with warning for network errors', async () => {
      const user = userEvent.setup()
      
      // Mock network error - available should be false but error should contain 'network'
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: false,
          error: 'Network error occurred',
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')
      const updateButton = screen.getByRole('button', { name: /update/i })

      // Change username to trigger the network error scenario
      await user.clear(usernameInput)
      await user.type(usernameInput, 'newusername')

      // Update button should be enabled for network errors
      expect(updateButton).not.toBeDisabled()

      // Click update button
      await user.click(updateButton)

      // Should call updateUsername with warning
      await waitFor(() => {
        expect(mockUpdateUsername).toHaveBeenCalledWith('newusername')
      })

      // Should show success message after update
      await waitFor(() => {
        expect(screen.getByText(/username updated successfully/i)).toBeInTheDocument()
      })
    })

    it('should show retry button for network errors', async () => {
      const user = userEvent.setup()
      
      // Mock network error - available should be false but error should contain 'network'
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: false,
          error: 'Network error occurred',
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')

      // Change username to trigger the network error scenario
      await user.clear(usernameInput)
      await user.type(usernameInput, 'newusername')

      // Should show retry button
      const retryButton = screen.getByRole('button', { name: /retry/i })
      expect(retryButton).toBeInTheDocument()

      // Click retry button
      await user.click(retryButton)

      // Should call checkAvailability again
      expect(mockUsernameHook.checkAvailability).toHaveBeenCalledWith('newusername')
    })

    it('should successfully update username when available', async () => {
      const user = userEvent.setup()
      
      // Mock available username
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: true,
          error: undefined,
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')
      const updateButton = screen.getByRole('button', { name: /update/i })

      // Type available username
      await user.clear(usernameInput)
      await user.type(usernameInput, 'availableuser')

      // Update button should be enabled
      expect(updateButton).not.toBeDisabled()

      // Click update button
      await user.click(updateButton)

      // Should call updateUsername
      await waitFor(() => {
        expect(mockUpdateUsername).toHaveBeenCalledWith('availableuser')
      })

      // Should show success message
      await waitFor(() => {
        expect(screen.getByText(/username updated successfully/i)).toBeInTheDocument()
      })

      // Should refresh router
      expect(mockRouter.refresh).toHaveBeenCalled()
    })

    it('should handle username update errors', async () => {
      const user = userEvent.setup()
      
      // Mock available username
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: true,
          error: undefined,
          cached: false,
        },
      })

      // Mock failed updateUsername
      mockUpdateUsername.mockResolvedValue({
        success: false,
        error: 'Username already taken',
      })

      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')
      const updateButton = screen.getByRole('button', { name: /update/i })

      // Type available username
      await user.clear(usernameInput)
      await user.type(usernameInput, 'availableuser')

      // Click update button
      await user.click(updateButton)

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/username already taken/i)).toBeInTheDocument()
      })
    })

    it('should show current and new profile URLs', async () => {
      const user = userEvent.setup()
      
      // Mock available username
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: false,
          available: true,
          error: undefined,
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')

      // Should show current profile URL
      expect(screen.getByText('/testuser')).toBeInTheDocument()

      // Type new username
      await user.clear(usernameInput)
      await user.type(usernameInput, 'newusername')

      // Should show new profile URL
      await waitFor(() => {
        expect(screen.getByText('/newusername')).toBeInTheDocument()
      })
    })
  })

  describe('Username Input Component Integration', () => {
    it('should show username requirements when focused', async () => {
      const user = userEvent.setup()
      render(<SettingsForm user={mockUser} />)

      const usernameInput = screen.getByDisplayValue('testuser')

      // Focus on input
      await user.click(usernameInput)

      // Should show requirements
      expect(screen.getByText(/username requirements/i)).toBeInTheDocument()
      expect(screen.getByText(/3-30 characters long/i)).toBeInTheDocument()
      expect(screen.getByText(/only letters, numbers, hyphens, and underscores/i)).toBeInTheDocument()
    })

    it('should show checking state during availability check', async () => {
      // Mock checking state
      const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
      useUsernameAvailability.mockReturnValue({
        ...mockUsernameHook,
        status: {
          checking: true,
          available: undefined,
          error: undefined,
          cached: false,
        },
      })

      render(<SettingsForm user={mockUser} />)

      // Should show checking state
      expect(screen.getByText(/checking availability/i)).toBeInTheDocument()
      
      // Update button should be disabled during checking
      const updateButton = screen.getByRole('button', { name: /update/i })
      expect(updateButton).toBeDisabled()
    })
  })
})