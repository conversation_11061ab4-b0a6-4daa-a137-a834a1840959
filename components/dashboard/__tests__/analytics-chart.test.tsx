import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { AnalyticsChart } from '../analytics-chart'
import type { ChartData, LinkPerformanceData, ReferrerData } from '../analytics-chart'

// Mock Recharts components
jest.mock('recharts', () => ({
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey, stroke }: any) => (
    <div data-testid="line" data-key={dataKey} data-stroke={stroke} />
  ),
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  BarChart: ({ children, data }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey, fill }: any) => (
    <div data-testid="bar" data-key={dataKey} data-fill={fill} />
  ),
  PieChart: ({ children }: any) => (
    <div data-testid="pie-chart">{children}</div>
  ),
  Pie: ({ data }: any) => (
    <div data-testid="pie" data-chart-data={JSON.stringify(data)} />
  ),
  Cell: ({ fill }: any) => <div data-testid="cell" data-fill={fill} />
}))

// Mock the analytics logger
jest.mock('@/lib/utils/analytics-logger', () => ({
  analyticsLogger: {
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn()
  }
}))

// Mock validation functions
jest.mock('@/lib/validations/analytics', () => ({
  isValidChartData: jest.fn((data) => Array.isArray(data)),
  isValidLinkPerformanceData: jest.fn((data) => Array.isArray(data)),
  isValidReferrerData: jest.fn((data) => Array.isArray(data)),
  sanitizeChartData: jest.fn((data) => data),
  sanitizeLinkPerformanceData: jest.fn((data) => data),
  sanitizeReferrerData: jest.fn((data) => data)
}))

// Mock error boundary
jest.mock('../analytics-error-boundary', () => ({
  AnalyticsErrorBoundary: ({ children }: any) => <div data-testid="error-boundary">{children}</div>,
  useAnalyticsErrorHandler: () => ({
    handleError: jest.fn()
  })
}))

describe('AnalyticsChart Component Tests', () => {
  const mockViewsData: ChartData[] = [
    { date: '2024-01-01', count: 10 },
    { date: '2024-01-02', count: 15 },
    { date: '2024-01-03', count: 8 },
    { date: '2024-01-04', count: 22 },
    { date: '2024-01-05', count: 18 }
  ]

  const mockClicksData: ChartData[] = [
    { date: '2024-01-01', count: 5 },
    { date: '2024-01-02', count: 8 },
    { date: '2024-01-03', count: 3 },
    { date: '2024-01-04', count: 12 },
    { date: '2024-01-05', count: 9 }
  ]

  const mockLinkPerformance: LinkPerformanceData[] = [
    {
      id: 'link-1',
      title: 'My Website',
      url: 'https://example.com',
      totalClicks: 45,
      periodClicks: 25,
      isVisible: true
    },
    {
      id: 'link-2',
      title: 'GitHub Profile',
      url: 'https://github.com/user',
      totalClicks: 32,
      periodClicks: 18,
      isVisible: true
    },
    {
      id: 'link-3',
      title: 'Hidden Link',
      url: 'https://hidden.com',
      totalClicks: 10,
      periodClicks: 5,
      isVisible: false
    }
  ]

  const mockTopReferrers: ReferrerData[] = [
    { referrer: 'https://google.com', count: 25 },
    { referrer: 'https://twitter.com', count: 15 },
    { referrer: 'Direct', count: 12 },
    { referrer: 'https://linkedin.com', count: 8 }
  ]

  const defaultProps = {
    viewsData: mockViewsData,
    clicksData: mockClicksData,
    linkPerformance: mockLinkPerformance,
    topReferrers: mockTopReferrers,
    period: '30'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Loading States', () => {
    it('should render loading skeleton when isLoading is true', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          isLoading={true}
        />
      )

      // Should show skeleton components
      expect(screen.getAllByTestId(/skeleton/i)).toHaveLength(expect.any(Number))
      
      // Should not show actual charts
      expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument()
      expect(screen.queryByTestId('bar-chart')).not.toBeInTheDocument()
      expect(screen.queryByTestId('pie-chart')).not.toBeInTheDocument()
    })

    it('should render different skeleton components for different sections', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          isLoading={true}
        />
      )

      // Should have multiple skeleton sections
      const skeletons = screen.getAllByTestId(/skeleton/i)
      expect(skeletons.length).toBeGreaterThan(5)
    })
  })

  describe('Error States', () => {
    it('should render error message when error prop is provided', () => {
      const errorMessage = 'Failed to load analytics data'
      
      render(
        <AnalyticsChart
          {...defaultProps}
          error={errorMessage}
        />
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByText(/unable to load chart/i)).toBeInTheDocument()
      
      // Should not show charts when there's an error
      expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument()
    })

    it('should render retry button when onRetry is provided', () => {
      const mockRetry = jest.fn()
      
      render(
        <AnalyticsChart
          {...defaultProps}
          error="Network error"
          onRetry={mockRetry}
        />
      )

      const retryButton = screen.getByRole('button', { name: /try again/i })
      expect(retryButton).toBeInTheDocument()
      
      fireEvent.click(retryButton)
      expect(mockRetry).toHaveBeenCalledTimes(1)
    })
  })

  describe('Data Rendering', () => {
    it('should render all chart sections with valid data', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should render profile views chart
      expect(screen.getByText('Profile Views Over Time')).toBeInTheDocument()
      expect(screen.getByTestId('line-chart')).toBeInTheDocument()

      // Should render link clicks chart
      expect(screen.getByText('Link Clicks Over Time')).toBeInTheDocument()
      expect(screen.getAllByTestId('line-chart')).toHaveLength(2)

      // Should render link performance section
      expect(screen.getByText('Top Performing Links')).toBeInTheDocument()
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument()

      // Should render traffic sources section
      expect(screen.getByText('Traffic Sources')).toBeInTheDocument()
      expect(screen.getByTestId('pie-chart')).toBeInTheDocument()
    })

    it('should pass correct data to chart components', () => {
      render(<AnalyticsChart {...defaultProps} />)

      const lineCharts = screen.getAllByTestId('line-chart')
      
      // Check views chart data
      const viewsChart = lineCharts[0]
      const viewsData = JSON.parse(viewsChart.getAttribute('data-chart-data') || '[]')
      expect(viewsData).toEqual(mockViewsData)

      // Check clicks chart data
      const clicksChart = lineCharts[1]
      const clicksData = JSON.parse(clicksChart.getAttribute('data-chart-data') || '[]')
      expect(clicksData).toEqual(mockClicksData)

      // Check bar chart data
      const barChart = screen.getByTestId('bar-chart')
      const barData = JSON.parse(barChart.getAttribute('data-chart-data') || '[]')
      expect(barData).toEqual(mockLinkPerformance.slice(0, 5))
    })

    it('should render link performance list with correct data', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should show link titles
      expect(screen.getByText('My Website')).toBeInTheDocument()
      expect(screen.getByText('GitHub Profile')).toBeInTheDocument()
      expect(screen.getByText('Hidden Link')).toBeInTheDocument()

      // Should show click counts
      expect(screen.getByText('45 clicks')).toBeInTheDocument()
      expect(screen.getByText('32 clicks')).toBeInTheDocument()
      expect(screen.getByText('10 clicks')).toBeInTheDocument()

      // Should show hidden badge for invisible links
      expect(screen.getByText('Hidden')).toBeInTheDocument()
    })

    it('should render referrer list with correct data', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should show referrer sources
      expect(screen.getByText('google.com')).toBeInTheDocument()
      expect(screen.getByText('twitter.com')).toBeInTheDocument()
      expect(screen.getByText('Direct')).toBeInTheDocument()
      expect(screen.getByText('linkedin.com')).toBeInTheDocument()
    })

    it('should calculate and display trends correctly', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should show trend indicators
      const trendElements = screen.getAllByTestId(/trending|minus/i)
      expect(trendElements.length).toBeGreaterThan(0)

      // Should show trend percentages
      const percentageElements = screen.getAllByText(/vs last period/i)
      expect(percentageElements.length).toBe(2) // One for views, one for clicks
    })
  })

  describe('Empty Data States', () => {
    it('should render empty state for views when no data', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          viewsData={[]}
        />
      )

      expect(screen.getByText('No view data available')).toBeInTheDocument()
      expect(screen.getByText(/data will appear here once people start viewing/i)).toBeInTheDocument()
    })

    it('should render empty state for clicks when no data', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          clicksData={[]}
        />
      )

      expect(screen.getByText('No click data available')).toBeInTheDocument()
      expect(screen.getByText(/data will appear here once people start clicking/i)).toBeInTheDocument()
    })

    it('should render empty state for link performance when no links', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          linkPerformance={[]}
        />
      )

      expect(screen.getByText('No links to analyze yet')).toBeInTheDocument()
      expect(screen.getByText(/add some links to your profile/i)).toBeInTheDocument()
    })

    it('should render empty state for traffic sources when no referrers', () => {
      render(
        <AnalyticsChart
          {...defaultProps}
          topReferrers={[]}
        />
      )

      // Should still render the section but with empty chart
      expect(screen.getByText('Traffic Sources')).toBeInTheDocument()
    })
  })

  describe('Data Validation and Sanitization', () => {
    it('should handle invalid data gracefully', () => {
      render(
        <AnalyticsChart
          viewsData={null as any}
          clicksData={undefined as any}
          linkPerformance={'invalid' as any}
          topReferrers={123 as any}
          period="30"
        />
      )

      // Should not crash and should render empty states
      expect(screen.getByText('No view data available')).toBeInTheDocument()
      expect(screen.getByText('No click data available')).toBeInTheDocument()
      expect(screen.getByText('No links to analyze yet')).toBeInTheDocument()
    })

    it('should handle malformed chart data', () => {
      const malformedViewsData = [
        { date: '2024-01-01', count: 10 },
        { invalidStructure: true },
        { date: '2024-01-03', count: 'invalid' }
      ] as any

      render(
        <AnalyticsChart
          {...defaultProps}
          viewsData={malformedViewsData}
        />
      )

      // Should still render the chart (validation/sanitization should handle it)
      expect(screen.getByText('Profile Views Over Time')).toBeInTheDocument()
    })

    it('should handle missing required properties in link performance data', () => {
      const malformedLinkData = [
        { id: 'link-1', title: 'Valid Link', url: 'https://example.com', totalClicks: 10, periodClicks: 5, isVisible: true },
        { id: 'link-2' }, // Missing required properties
        { title: 'No ID', url: 'https://example.com', totalClicks: 5, periodClicks: 2, isVisible: true }
      ] as any

      render(
        <AnalyticsChart
          {...defaultProps}
          linkPerformance={malformedLinkData}
        />
      )

      // Should render what it can
      expect(screen.getByText('Valid Link')).toBeInTheDocument()
    })
  })

  describe('Responsive Design', () => {
    it('should render mobile-friendly elements', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should have responsive containers
      const responsiveContainers = screen.getAllByTestId('responsive-container')
      expect(responsiveContainers.length).toBeGreaterThan(0)

      // Should have mobile-friendly text sizes and layouts
      const cardTitles = screen.getAllByRole('heading')
      expect(cardTitles.length).toBeGreaterThan(0)
    })

    it('should handle long URLs and titles gracefully', () => {
      const longDataProps = {
        ...defaultProps,
        linkPerformance: [
          {
            id: 'link-1',
            title: 'This is a very long link title that should be truncated properly on mobile devices',
            url: 'https://very-long-domain-name-that-should-be-truncated.example.com/very/long/path/that/continues/for/a/while',
            totalClicks: 45,
            periodClicks: 25,
            isVisible: true
          }
        ]
      }

      render(<AnalyticsChart {...longDataProps} />)

      // Should render the long title (truncation handled by CSS)
      expect(screen.getByText(/This is a very long link title/)).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should have proper headings
      const headings = screen.getAllByRole('heading')
      expect(headings.length).toBeGreaterThan(0)

      // Should have proper button roles for interactive elements
      const buttons = screen.queryAllByRole('button')
      buttons.forEach(button => {
        expect(button).toBeInTheDocument()
      })
    })

    it('should provide meaningful text alternatives', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should have descriptive text for charts
      expect(screen.getByText('Profile Views Over Time')).toBeInTheDocument()
      expect(screen.getByText('Link Clicks Over Time')).toBeInTheDocument()
      expect(screen.getByText('Top Performing Links')).toBeInTheDocument()
      expect(screen.getByText('Traffic Sources')).toBeInTheDocument()
    })
  })

  describe('Performance', () => {
    it('should handle large datasets without crashing', () => {
      const largeViewsData = Array.from({ length: 365 }, (_, i) => ({
        date: `2024-${String(Math.floor(i / 30) + 1).padStart(2, '0')}-${String((i % 30) + 1).padStart(2, '0')}`,
        count: Math.floor(Math.random() * 100)
      }))

      const largeLinkData = Array.from({ length: 50 }, (_, i) => ({
        id: `link-${i}`,
        title: `Link ${i}`,
        url: `https://example${i}.com`,
        totalClicks: Math.floor(Math.random() * 1000),
        periodClicks: Math.floor(Math.random() * 100),
        isVisible: true
      }))

      expect(() => {
        render(
          <AnalyticsChart
            {...defaultProps}
            viewsData={largeViewsData}
            linkPerformance={largeLinkData}
          />
        )
      }).not.toThrow()

      // Should still render main sections
      expect(screen.getByText('Profile Views Over Time')).toBeInTheDocument()
      expect(screen.getByText('Top Performing Links')).toBeInTheDocument()
    })

    it('should limit displayed items for performance', () => {
      const manyLinks = Array.from({ length: 20 }, (_, i) => ({
        id: `link-${i}`,
        title: `Link ${i}`,
        url: `https://example${i}.com`,
        totalClicks: 100 - i,
        periodClicks: 50 - i,
        isVisible: true
      }))

      render(
        <AnalyticsChart
          {...defaultProps}
          linkPerformance={manyLinks}
        />
      )

      // Should only show top 5 links in the chart
      const barChart = screen.getByTestId('bar-chart')
      const barData = JSON.parse(barChart.getAttribute('data-chart-data') || '[]')
      expect(barData.length).toBeLessThanOrEqual(5)
    })
  })

  describe('Error Boundaries', () => {
    it('should wrap charts in error boundaries', () => {
      render(<AnalyticsChart {...defaultProps} />)

      // Should have error boundary wrappers
      const errorBoundaries = screen.getAllByTestId('error-boundary')
      expect(errorBoundaries.length).toBeGreaterThan(0)
    })

    it('should handle chart rendering failures gracefully', () => {
      // Mock a chart component that throws an error
      const originalError = console.error
      console.error = jest.fn()

      render(<AnalyticsChart {...defaultProps} />)

      // Should not crash the entire component
      expect(screen.getByText('Profile Views Over Time')).toBeInTheDocument()

      console.error = originalError
    })
  })
})