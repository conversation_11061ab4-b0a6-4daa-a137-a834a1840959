'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { ScreenReaderOnly } from '@/components/ui/skip-links'
import { trapFocus, handleArrowNavigation } from '@/lib/utils/accessibility'
import { 
  LayoutDashboard, 
  Link as LinkIcon, 
  Palette, 
  BarChart3,
  User,
  Settings,
  Menu,
  X
} from 'lucide-react'

const navigation = [
  {
    name: 'Overview',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Profile',
    href: '/dashboard/profile',
    icon: User,
  },
  {
    name: 'Links',
    href: '/dashboard/links',
    icon: LinkIcon,
  },
  {
    name: 'Customize',
    href: '/dashboard/customize',
    icon: Palette,
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
  },
]

export function DashboardNav() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const navRef = useRef<HTMLElement>(null)
  const menuButtonRef = useRef<HTMLButtonElement>(null)
  const [focusedIndex, setFocusedIndex] = useState(-1)
  const [isDesktop, setIsDesktop] = useState(false)

  // Handle desktop/mobile detection after hydration
  useEffect(() => {
    function handleResize() {
      setIsDesktop(window.innerWidth >= 1024)
    }

    // Set initial value
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle escape key to close mobile menu
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false)
        menuButtonRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isMobileMenuOpen])

  // Trap focus when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen && navRef.current) {
      const cleanup = trapFocus(navRef.current)
      return cleanup
    }
  }, [isMobileMenuOpen])

  // Handle keyboard navigation in menu
  function handleKeyDown(event: KeyboardEvent, index: number) {
    const navItems = navRef.current?.querySelectorAll('[role="menuitem"]') as NodeListOf<HTMLElement>
    if (!navItems) return

    const newIndex = handleArrowNavigation(event, Array.from(navItems), index, 'vertical')
    if (newIndex !== index) {
      setFocusedIndex(newIndex)
    }
  }

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          ref={menuButtonRef}
          variant="outline"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-white shadow-md min-h-[44px] min-w-[44px] touch-manipulation"
          aria-expanded={isMobileMenuOpen}
          aria-controls="mobile-navigation"
          aria-label={isMobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
          aria-haspopup="true"
        >
          {isMobileMenuOpen ? (
            <>
              <X className="h-4 w-4" aria-hidden="true" />
              <ScreenReaderOnly>Close menu</ScreenReaderOnly>
            </>
          ) : (
            <>
              <Menu className="h-4 w-4" aria-hidden="true" />
              <ScreenReaderOnly>Open menu</ScreenReaderOnly>
            </>
          )}
        </Button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Navigation sidebar */}
      <nav 
        ref={navRef}
        id="mobile-navigation"
        className={cn(
          "fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-200 ease-in-out lg:translate-x-0",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
        aria-label="Main navigation"
        role="navigation"
      >
        <div className="flex flex-col h-full">
          {/* Logo/Brand */}
          <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <Link 
              href="/dashboard" 
              className="text-xl font-bold text-gray-900 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm"
              aria-label="LinksInBio Dashboard Home"
            >
              LinksInBio
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
              aria-label="Close navigation menu"
            >
              <X className="h-4 w-4" />
              <ScreenReaderOnly>Close menu</ScreenReaderOnly>
            </Button>
          </div>

          {/* Navigation items */}
          <div className="flex-1 px-4 py-6 space-y-2" role="menu">
            {navigation.map((item, index) => {
              const isActive = pathname === item.href
              const Icon = item.icon

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  onKeyDown={(e) => handleKeyDown(e as any, index)}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 min-h-[44px] touch-manipulation",
                    // Enhanced focus states
                    "focus-visible:ring-2 focus-visible:ring-offset-2",
                    // High contrast support
                    "@media (prefers-contrast: high) { border: 1px solid currentColor }",
                    isActive
                      ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  )}
                  role="menuitem"
                  aria-current={isActive ? 'page' : undefined}
                  tabIndex={isMobileMenuOpen || isDesktop ? 0 : -1}
                >
                  <Icon className="mr-3 h-5 w-5" aria-hidden="true" />
                  {item.name}
                  {isActive && <ScreenReaderOnly>(current page)</ScreenReaderOnly>}
                </Link>
              )
            })}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              © 2024 LinksInBio
            </div>
          </div>
        </div>
      </nav>
    </>
  )
}