"use client"

import { useState, useTransition } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import type { User } from "@/lib/types"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { updateProfile } from "@/lib/actions/profile"
import { ProfileImageUpload } from "./profile-image-upload"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

const profileSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').max(50, 'Display name must be less than 50 characters'),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileEditorProps {
  user: User
}

export function ProfileEditor({ user }: ProfileEditorProps) {
  const [isPending, startTransition] = useTransition()
  const [profileImage, setProfileImage] = useState(user.profileImage)

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      displayName: user.displayName || '',
      bio: user.bio || '',
    },
  })

  const onSubmit = async (data: ProfileFormData) => {
    startTransition(async () => {
      const result = await updateProfile({
        ...data,
        bio: data.bio === '' ? undefined : data.bio,
        profileImage,
      })
      
      if (result.success) {
        toast.success("Profile updated successfully!")
      } else {
        toast.error(result.error || "Failed to update profile")
      }
    })
  }

  const handleImageChange = async (imageUrl: string | null) => {
    setProfileImage(imageUrl)
    
    // Auto-save the profile image change
    startTransition(async () => {
      const bioValue = form.getValues('bio')
      const result = await updateProfile({
        displayName: form.getValues('displayName'),
        bio: bioValue === '' ? undefined : bioValue,
        profileImage: imageUrl,
      })
      
      if (!result.success) {
        toast.error(result.error || "Failed to update profile image")
        // Revert the image change on error
        setProfileImage(user.profileImage)
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Profile Image Section */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Image</CardTitle>
          <CardDescription>
            Upload a profile image to personalize your page. Recommended size: 400x400px.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <ProfileImageUpload
            currentImage={profileImage}
            displayName={user.displayName}
            onImageChange={handleImageChange}
            disabled={isPending}
          />
        </CardContent>
      </Card>

      {/* Profile Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>
            Update your display name and bio to tell visitors about yourself.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="displayName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Display Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your display name"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      This is the name that will be displayed on your profile.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="bio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bio</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Tell visitors about yourself..."
                        className="min-h-[100px]"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      A short description about yourself. Maximum 500 characters.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update Profile'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}