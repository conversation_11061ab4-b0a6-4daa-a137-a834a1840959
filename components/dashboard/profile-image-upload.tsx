"use client"

import { useState, useRef } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { OptimizedImage } from "@/components/ui/optimized-image"
import { But<PERSON> } from "@/components/ui/button"
import { Camera, Trash2, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { useUploadThing } from "@/lib/uploadthing-client"

interface ProfileImageUploadProps {
  currentImage: string | null
  displayName: string
  onImageChange: (imageUrl: string | null) => void
  disabled?: boolean
}

export function ProfileImageUpload({ 
  currentImage, 
  displayName, 
  onImageChange, 
  disabled = false 
}: ProfileImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const { startUpload } = useUploadThing("profileImage", {
    onClientUploadComplete: (res) => {
      if (res?.[0]?.ufsUrl) {
        onImageChange(res[0].ufsUrl)
        setIsUploading(false)
        toast.success("Profile image updated successfully!")
      }
    },
    onUploadError: (error) => {
      console.error("UploadThing error:", error)
      // Fallback to compressed base64 if UploadThing fails
      setIsUploading(false)
      toast.error("Upload service unavailable. Using compressed image instead.")
    },
  })

  // Compress image to reduce size
  const compressImage = (file: File, maxWidth: number = 400, quality: number = 0.8): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxWidth) {
            width = (width * maxWidth) / height
            height = maxWidth
          }
        }
        
        canvas.width = width
        canvas.height = height
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)
        resolve(compressedDataUrl)
      }
      
      img.onerror = reject
      img.src = URL.createObjectURL(file)
    })
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size (10MB limit for original file)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image is too large. Please choose a file smaller than 10MB.")
      return
    }
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error("Please select a valid image file.")
      return
    }

    setIsUploading(true)

    try {
      // Try UploadThing first for files under 4MB
      if (file.size <= 4 * 1024 * 1024) {
        await startUpload([file])
      } else {
        // For larger files, compress and use as base64
        const compressedImage = await compressImage(file)
        onImageChange(compressedImage)
        setIsUploading(false)
        toast.success("Large image compressed and updated successfully!")
      }
    } catch (error) {
      console.error("Image upload error:", error)
      
      try {
        // Fallback: compress image and use base64
        const compressedImage = await compressImage(file)
        onImageChange(compressedImage)
        setIsUploading(false)
        toast.success("Image compressed and updated successfully!")
      } catch (compressionError) {
        console.error("Image compression error:", compressionError)
        setIsUploading(false)
        toast.error("Failed to process image. Please try a different image.")
      }
    }
  }

  const handleDeleteImage = () => {
    onImageChange(null)
    toast.success("Profile image removed successfully!")
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="flex items-center space-x-4">
      <div className="relative">
        <OptimizedImage
          src={currentImage}
          alt={displayName}
          width={80}
          height={80}
          className="rounded-full border-2 border-border"
          quality={90}
          priority
          fallback={
            <Avatar className="h-20 w-20">
              <AvatarFallback className="text-lg">
                {getInitials(displayName)}
              </AvatarFallback>
            </Avatar>
          }
        />
      </div>
      
      <div className="flex flex-col space-y-2">
        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleButtonClick}
            disabled={isUploading || disabled}
          >
            {isUploading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Camera className="h-4 w-4" />
            )}
            {isUploading ? 'Uploading...' : 'Change Image'}
          </Button>
          
          {currentImage && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDeleteImage}
              disabled={disabled || isUploading}
            >
              <Trash2 className="h-4 w-4" />
              Remove
            </Button>
          )}
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
          disabled={isUploading || disabled}
        />
        
        <p className="text-xs text-muted-foreground">
          JPG, PNG or GIF. Max size 10MB. Large images will be compressed.
        </p>
      </div>
    </div>
  )
}