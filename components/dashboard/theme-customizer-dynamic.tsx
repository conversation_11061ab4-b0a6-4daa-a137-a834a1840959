"use client"

import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Dynamically import the theme customizer to reduce initial bundle size
const ThemeCustomizer = dynamic(
  () => import('./theme-customizer').then(mod => ({ default: mod.ThemeCustomizer })),
  {
    loading: () => (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Theme Customization</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    ),
    ssr: false,
  }
)

const ThemePreview = dynamic(
  () => import('./theme-preview').then(mod => ({ default: mod.ThemePreview })),
  {
    loading: () => (
      <Card>
        <CardHeader>
          <CardTitle>Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-96 w-full" />
        </CardContent>
      </Card>
    ),
    ssr: false,
  }
)

export { ThemeCustomizer as DynamicThemeCustomizer, ThemePreview as DynamicThemePreview }