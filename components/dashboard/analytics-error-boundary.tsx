"use client"

import <PERSON><PERSON>, { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>n<PERSON>, ReactNode } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Bug } from 'lucide-react'
import { AnalyticsErrorFactory } from '@/lib/errors/analytics-errors'
import { analyticsLogger } from '@/lib/utils/analytics-logger'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  chartType?: string
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export class AnalyticsErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    const errorId = `analytics-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    return {
      hasError: true,
      error,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Generate unique error ID for tracking
    const errorId = `analytics-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    // Create analytics error for structured logging
    const analyticsError = AnalyticsErrorFactory.chartRenderingError(
      this.props.chartType || 'unknown',
      error
    )

    // Log the error with context
    analyticsLogger.error('Chart rendering error caught by error boundary', {
      chartType: this.props.chartType,
      errorId,
      componentStack: errorInfo.componentStack,
      errorStack: error.stack
    }, analyticsError)

    // Update state with error info
    this.setState({
      errorInfo,
      errorId
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report to external error tracking service if available
    if (typeof window !== 'undefined' && (window as { reportError?: (data: unknown) => void }).reportError) {
      (window as { reportError: (data: unknown) => void }).reportError({
        error: analyticsError,
        errorId,
        context: {
          chartType: this.props.chartType,
          componentStack: errorInfo.componentStack
        }
      })
    }
  }

  handleRetry = () => {
    analyticsLogger.info('User initiated error boundary retry', {
      chartType: this.props.chartType,
      errorId: this.state.errorId
    })

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    })
  }

  handleReportError = () => {
    if (this.state.error && this.state.errorId) {
      analyticsLogger.info('User reported error', {
        chartType: this.props.chartType,
        errorId: this.state.errorId
      })

      // Copy error details to clipboard for easy reporting
      const errorDetails = {
        errorId: this.state.errorId,
        chartType: this.props.chartType,
        error: this.state.error.message,
        stack: this.state.error.stack,
        timestamp: new Date().toISOString()
      }

      if (navigator.clipboard) {
        navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
          .then(() => {
            alert('Error details copied to clipboard. Please share this with support.')
          })
          .catch(() => {
            console.log('Error details:', errorDetails)
            alert('Error details logged to console. Please check the browser console.')
          })
      } else {
        console.log('Error details:', errorDetails)
        alert('Error details logged to console. Please check the browser console.')
      }
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              <span>Chart Error</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-red-600">
              <p className="font-medium mb-2">
                Unable to render {this.props.chartType || 'analytics'} chart
              </p>
              <p className="text-red-500">
                {this.state.error?.message || 'An unexpected error occurred while rendering the chart.'}
              </p>
              {this.state.errorId && (
                <p className="text-xs text-red-400 mt-2 font-mono">
                  Error ID: {this.state.errorId}
                </p>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Try Again</span>
              </Button>

              <Button
                onClick={this.handleReportError}
                variant="outline"
                size="sm"
                className="flex items-center space-x-2 text-red-600 border-red-200 hover:bg-red-50"
              >
                <Bug className="h-4 w-4" />
                <span>Report Error</span>
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
              <details className="mt-4">
                <summary className="text-xs text-red-500 cursor-pointer hover:text-red-700">
                  Developer Details (Development Only)
                </summary>
                <div className="mt-2 p-3 bg-red-100 rounded text-xs font-mono text-red-700 overflow-auto max-h-40">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error?.message}
                  </div>
                  <div className="mb-2">
                    <strong>Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">
                      {this.state.error?.stack}
                    </pre>
                  </div>
                  <div>
                    <strong>Component Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </div>
                </div>
              </details>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Higher-order component for wrapping individual charts
export function withAnalyticsErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  chartType: string
) {
  const WithErrorBoundary = (props: P) => {
    return (
      <AnalyticsErrorBoundary chartType={chartType}>
        <WrappedComponent {...props} />
      </AnalyticsErrorBoundary>
    )
  }

  WithErrorBoundary.displayName = `withAnalyticsErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`
  
  return WithErrorBoundary
}

// Hook for handling errors in functional components
export function useAnalyticsErrorHandler(chartType: string) {
  const handleError = React.useCallback((error: Error, context?: Record<string, unknown>) => {
    const analyticsError = AnalyticsErrorFactory.chartRenderingError(chartType, error)
    
    analyticsLogger.error('Chart error handled by hook', {
      chartType,
      ...context
    }, analyticsError)

    // You could also trigger a toast notification here
    console.error(`Error in ${chartType} chart:`, error)
  }, [chartType])

  return { handleError }
}