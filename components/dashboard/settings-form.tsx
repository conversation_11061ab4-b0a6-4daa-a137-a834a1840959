'use client'

import { useState, useTransition } from 'react'
import { useRouter } from 'next/navigation'
import { signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { UsernameInput } from '@/components/ui/username-input'
import { 
  updateUsername, 
  exportUserData, 
  deleteAccount 
} from '@/lib/actions/account'
import { useUsernameAvailability } from '@/lib/hooks/use-username-availability'
import { allowsGracefulDegradation } from '@/lib/constants/username-errors'
import { Download, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'lucide-react'
import { cn } from '@/lib/utils'

interface User {
  id: string
  email: string
  username: string
  displayName: string
  bio?: string | null
  profileImage?: string | null
}

interface SettingsFormProps {
  user: User
}

export function SettingsForm({ user }: SettingsFormProps) {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  
  // Username management state
  const [newUsername, setNewUsername] = useState(user.username)
  const { status: usernameStatus, checkAvailability, retry, canRetry } = useUsernameAvailability(user.username)
  
  // General state
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning', text: string } | null>(null)
  
  // Dialog states
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteConfirmation, setDeleteConfirmation] = useState('')

  // Handle username change
  const handleUsernameChange = (value: string) => {
    setNewUsername(value)
    // Clear any previous messages when username changes
    if (message?.type === 'error' && message.text.includes('username')) {
      setMessage(null)
    }
  }

  // Update username
  const handleUpdateUsername = async () => {
    if (newUsername === user.username) {
      setMessage({ type: 'error', text: 'Username is the same as current username' })
      return
    }
    
    // Check if error allows graceful degradation
    const canProceedWithError = usernameStatus.error && 
      usernameStatus.errorCode && 
      allowsGracefulDegradation(usernameStatus.errorCode as any)

    if (usernameStatus.error && canProceedWithError) {
      setMessage({ 
        type: 'warning', 
        text: 'Network error occurred while checking availability. Proceeding with update...' 
      })
    } else if (usernameStatus.error || !usernameStatus.available) {
      setMessage({ type: 'error', text: 'Please choose an available username' })
      return
    }

    startTransition(async () => {
      try {
        const result = await updateUsername(newUsername)
        if (result.success) {
          setMessage({ type: 'success', text: 'Username updated successfully!' })
          router.refresh()
        } else {
          setMessage({ type: 'error', text: result.error || 'Failed to update username' })
        }
      } catch {
        setMessage({ type: 'error', text: 'Failed to update username' })
      }
    })
  }

  // Export data
  const handleExportData = async () => {
    startTransition(async () => {
      try {
        const result = await exportUserData()
        if (result.success) {
          // Create and download JSON file
          const dataStr = JSON.stringify(result.data, null, 2)
          const dataBlob = new Blob([dataStr], { type: 'application/json' })
          const url = URL.createObjectURL(dataBlob)
          const link = document.createElement('a')
          link.href = url
          link.download = `linksinbio-data-${new Date().toISOString().split('T')[0]}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
          
          setMessage({ type: 'success', text: 'Data exported successfully!' })
        } else {
          setMessage({ type: 'error', text: result.error || 'Failed to export data' })
        }
      } catch {
        setMessage({ type: 'error', text: 'Failed to export data' })
      }
    })
  }

  // Delete account
  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== 'DELETE') {
      return
    }

    startTransition(async () => {
      try {
        const result = await deleteAccount()
        if (result.success) {
          // Sign out and redirect
          await signOut({ callbackUrl: '/' })
        } else {
          setMessage({ type: 'error', text: result.error || 'Failed to delete account' })
          setShowDeleteDialog(false)
        }
      } catch {
        setMessage({ type: 'error', text: 'Failed to delete account' })
        setShowDeleteDialog(false)
      }
    })
  }

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={cn(
          message.type === 'success' && 'border-green-200 bg-green-50',
          message.type === 'error' && 'border-red-200 bg-red-50',
          message.type === 'warning' && 'border-yellow-200 bg-yellow-50'
        )}>
          <AlertDescription className={cn(
            message.type === 'success' && 'text-green-800',
            message.type === 'error' && 'text-red-800',
            message.type === 'warning' && 'text-yellow-800'
          )}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      {/* Username Management */}
      <Card>
        <CardHeader>
          <CardTitle>Username</CardTitle>
          <CardDescription>
            Change your username. This will also update your profile URL.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <UsernameInput
                  id="username"
                  value={newUsername}
                  onChange={handleUsernameChange}
                  currentUsername={user.username}
                  placeholder="Enter username"
                  disabled={isPending}
                  showRequirements={true}
                  showRetryButton={true}
                  showGracefulDegradation={true}
                />
              </div>
              <Button
                onClick={handleUpdateUsername}
                disabled={
                  isPending || 
                  newUsername === user.username || 
                  usernameStatus.checking ||
                  (usernameStatus.error && 
                   usernameStatus.errorCode && 
                   !allowsGracefulDegradation(usernameStatus.errorCode as any)) ||
                  usernameStatus.available === false
                }
              >
                {isPending ? <LoadingSpinner className="h-4 w-4" /> : 'Update'}
              </Button>
            </div>
            {/* Graceful degradation message */}
            {usernameStatus.error && 
             usernameStatus.errorCode && 
             allowsGracefulDegradation(usernameStatus.errorCode as any) && 
             newUsername !== user.username && (
              <div className="flex items-center space-x-2">
                <p className="text-sm text-yellow-600">
                  Error checking availability. You can still update your username.
                </p>
                {canRetry && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={retry}
                    disabled={usernameStatus.checking}
                  >
                    {usernameStatus.checking ? <LoadingSpinner className="h-3 w-3" /> : 'Retry'}
                  </Button>
                )}
              </div>
            )}
          </div>
          <div className="text-sm text-gray-600">
            Current profile URL: <code className="bg-gray-100 px-1 rounded">/{user.username}</code>
            {newUsername !== user.username && (usernameStatus.available || (usernameStatus.error && usernameStatus.errorCode && allowsGracefulDegradation(usernameStatus.errorCode as any))) && (
              <>
                <br />
                New profile URL: <code className="bg-green-100 px-1 rounded">/{newUsername}</code>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data Export */}
      <Card>
        <CardHeader>
          <CardTitle>Export Data</CardTitle>
          <CardDescription>
            Download all your data in JSON format.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={handleExportData}
            disabled={isPending}
            variant="outline"
            className="w-full sm:w-auto"
          >
            {isPending ? (
              <LoadingSpinner className="h-4 w-4 mr-2" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Export Data
          </Button>
        </CardContent>
      </Card>

      {/* Account Deletion */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
          <CardDescription>
            Permanently delete your account and all associated data.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <DialogTrigger asChild>
              <Button variant="destructive" className="w-full sm:w-auto">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Account
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <span>Delete Account</span>
                </DialogTitle>
                <DialogDescription className="space-y-2">
                  <p>
                    This action cannot be undone. This will permanently delete your account,
                    profile, links, and all associated data.
                  </p>
                  <p>
                    Type <strong>DELETE</strong> to confirm:
                  </p>
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  value={deleteConfirmation}
                  onChange={(e) => setDeleteConfirmation(e.target.value)}
                  placeholder="Type DELETE to confirm"
                />
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteDialog(false)
                    setDeleteConfirmation('')
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteAccount}
                  disabled={deleteConfirmation !== 'DELETE' || isPending}
                >
                  {isPending ? (
                    <LoadingSpinner className="h-4 w-4 mr-2" />
                  ) : (
                    <Trash2 className="h-4 w-4 mr-2" />
                  )}
                  Delete Account
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  )
}