'use client'

import { useState, useEffect } from 'react'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { LinkForm } from './link-form'
import { reorderLinks, deleteLink, toggleLinkVisibility, duplicateLink } from '@/lib/actions/links'
import { formatScheduleStatus } from '@/lib/utils/link-scheduling'
import { toast } from 'sonner'
import { 
  GripVertical, 
  Eye, 
  EyeOff, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Copy,
  ExternalLink,
  Link as LinkIcon,
  Github,
  Twitter,
  Instagram,
  Youtube,
  Linkedin,
  Globe,
  Mail,
  Phone,
  Calendar,
  Clock
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

// Icon mapping
const ICON_MAP = {
  link: LinkIcon,
  globe: Globe,
  github: Github,
  twitter: Twitter,
  instagram: Instagram,
  youtube: Youtube,
  linkedin: Linkedin,
  mail: Mail,
  phone: Phone,
}

interface Link {
  id: string
  title: string
  url: string
  icon?: string | null
  isVisible: boolean
  order: number
  clickCount: number
  isScheduled?: boolean
  scheduleStart?: Date | null
  scheduleEnd?: Date | null
  timezone?: string | null
}

interface SortableLinkItemProps {
  link: Link
  onUpdate: () => void
}

function SortableLinkItem({ link, onUpdate }: SortableLinkItemProps) {
  const [isUpdating, setIsUpdating] = useState(false)
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: link.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const IconComponent = link.icon && link.icon !== 'none' && ICON_MAP[link.icon as keyof typeof ICON_MAP]

  const handleToggleVisibility = async () => {
    setIsUpdating(true)
    try {
      const result = await toggleLinkVisibility(link.id)
      if (result.success) {
        toast.success(link.isVisible ? 'Link hidden' : 'Link made visible')
        onUpdate()
      } else {
        toast.error(result.error || 'Failed to update visibility')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this link?')) return
    
    setIsUpdating(true)
    try {
      const result = await deleteLink(link.id)
      if (result.success) {
        toast.success('Link deleted')
        onUpdate()
      } else {
        toast.error(result.error || 'Failed to delete link')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDuplicate = async () => {
    setIsUpdating(true)
    try {
      const result = await duplicateLink(link.id)
      if (result.success) {
        toast.success('Link duplicated')
        onUpdate()
      } else {
        toast.error(result.error || 'Failed to duplicate link')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`transition-all ${isDragging ? 'opacity-50 scale-105 shadow-lg' : ''} ${
        !link.isVisible ? 'opacity-60' : ''
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          {/* Drag handle */}
          <button
            className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"
            {...attributes}
            {...listeners}
          >
            <GripVertical className="h-5 w-5" />
          </button>

          {/* Icon */}
          <div className="flex-shrink-0">
            {IconComponent ? (
              <IconComponent className="h-5 w-5 text-gray-600" />
            ) : (
              <LinkIcon className="h-5 w-5 text-gray-400" />
            )}
          </div>

          {/* Link info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm truncate">{link.title}</h3>
              {!link.isVisible && <EyeOff className="h-4 w-4 text-gray-400" />}
              {link.isScheduled && <Calendar className="h-4 w-4 text-blue-500" />}
            </div>
            <p className="text-xs text-gray-500 truncate">{link.url}</p>
            <div className="flex items-center gap-3 text-xs text-gray-400">
              <span>{link.clickCount} clicks</span>
              {link.isScheduled && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatScheduleStatus(link)}
                </span>
              )}
            </div>
          </div>

          {/* Visibility toggle */}
          <Switch
            checked={link.isVisible}
            onCheckedChange={handleToggleVisibility}
            disabled={isUpdating}
            className="flex-shrink-0"
          />

          {/* Actions menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Open Link
                </a>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <LinkForm
                  mode="edit"
                  link={link}
                  trigger={
                    <div className="flex items-center gap-2 w-full">
                      <Edit className="h-4 w-4" />
                      Edit
                    </div>
                  }
                  onSuccess={onUpdate}
                />
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate} disabled={isUpdating}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                disabled={isUpdating}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  )
}

interface LinkListProps {
  links: Link[]
  onUpdate: () => void
}

export function LinkList({ links, onUpdate }: LinkListProps) {
  const [items, setItems] = useState(links)
  const [isReordering, setIsReordering] = useState(false)

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Update items when links prop changes
  useEffect(() => {
    setItems(links)
  }, [links])

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id)
      const newIndex = items.findIndex((item) => item.id === over.id)

      const newItems = arrayMove(items, oldIndex, newIndex)
      setItems(newItems)

      // Update the order on the server
      setIsReordering(true)
      try {
        const linkIds = newItems.map(item => item.id)
        const result = await reorderLinks(linkIds)
        
        if (result.success) {
          toast.success('Links reordered')
          onUpdate()
        } else {
          // Revert the change if it failed
          setItems(items)
          toast.error(result.error || 'Failed to reorder links')
        }
      } catch (error) {
        // Revert the change if it failed
        setItems(items)
        toast.error('Something went wrong')
      } finally {
        setIsReordering(false)
      }
    }
  }

  if (items.length === 0) {
    return null
  }

  return (
    <div className="space-y-3">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          {items.map((link) => (
            <SortableLinkItem
              key={link.id}
              link={link}
              onUpdate={onUpdate}
            />
          ))}
        </SortableContext>
      </DndContext>
      
      {isReordering && (
        <div className="text-sm text-gray-500 text-center py-2">
          Updating order...
        </div>
      )}
    </div>
  )
}