"use client"

import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  LabelProps
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { TrendingUp, TrendingDown, Minus, AlertCircle, RefreshCw, BarChart3, Eye, MousePointer, Link, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { AnalyticsErrorBoundary, useAnalyticsErrorHandler } from './analytics-error-boundary'
import { analyticsLogger } from '@/lib/utils/analytics-logger'
import { 
  isValidChartData as validateChartData,
  isValidLinkPerformanceData as validateLinkPerformanceData,
  isValidReferrerData as validateReferrerData,
  sanitizeChartData,
  sanitizeLinkPerformanceData,
  sanitizeReferrerData
} from '@/lib/validations/analytics'
import React from 'react'

interface ChartData {
  date: string
  count: number
}

interface LinkPerformanceData {
  id: string
  title: string
  url: string
  totalClicks: number
  periodClicks: number
  isVisible: boolean
}

interface ReferrerData {
  referrer: string
  count: number
}

interface PieChartData {
  name: string
  value: number
  color: string
}

interface AnalyticsChartProps {
  viewsData: ChartData[]
  clicksData: ChartData[]
  linkPerformance: LinkPerformanceData[]
  topReferrers: ReferrerData[]
  period: string
  isLoading?: boolean
  error?: string
  onRetry?: () => void
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

// Enhanced data validation using centralized validation functions
const isValidChartDataWithLogging = (data: unknown, chartType?: string): data is ChartData[] => {
  const isValid = validateChartData(data)
  if (!isValid) {
    analyticsLogger.warn(`Chart data validation failed for ${chartType}`, {
      operation: 'chartDataValidation',
      chartType,
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : 'N/A'
    })
  }
  return isValid
}

const isValidLinkPerformanceDataWithLogging = (data: unknown): data is LinkPerformanceData[] => {
  const isValid = validateLinkPerformanceData(data)
  if (!isValid) {
    analyticsLogger.warn('Link performance data validation failed', {
      operation: 'linkPerformanceValidation',
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : 'N/A'
    })
  }
  return isValid
}

const isValidReferrerDataWithLogging = (data: unknown): data is ReferrerData[] => {
  const isValid = validateReferrerData(data)
  if (!isValid) {
    analyticsLogger.warn('Referrer data validation failed', {
      operation: 'referrerDataValidation',
      dataType: typeof data,
      isArray: Array.isArray(data),
      length: Array.isArray(data) ? data.length : 'N/A'
    })
  }
  return isValid
}

// Enhanced loading skeleton components with responsive design
function ChartSkeleton({ showLegend = false }: { showLegend?: boolean }) {
  return (
    <div className="h-64 space-y-3">
      {/* Chart header skeleton */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-24" />
      </div>
      
      {/* Main chart area skeleton */}
      <div className="relative">
        <Skeleton className="h-48 w-full rounded-lg" />
        
        {/* Animated pulse overlay for more realistic loading */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse rounded-lg" />
        
        {/* Chart axis lines skeleton */}
        <div className="absolute bottom-4 left-8 right-4 h-px bg-gray-200" />
        <div className="absolute top-4 bottom-8 left-8 w-px bg-gray-200" />
      </div>
      
      {/* Legend skeleton */}
      {showLegend && (
        <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-16" />
        </div>
      )}
    </div>
  )
}

function ListSkeleton({ items = 3 }: { items?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg animate-pulse">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-4 w-4 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      ))}
    </div>
  )
}

function AnalyticsChartSkeleton() {
  return (
    <div className="space-y-6">
      {/* Views Over Time Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <Skeleton className="h-6 w-48" />
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>

      {/* Clicks Over Time Skeleton */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <Skeleton className="h-6 w-48" />
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ChartSkeleton />
        </CardContent>
      </Card>

      {/* Bottom row with responsive grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Link Performance Skeleton */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <ChartSkeleton />
              <ListSkeleton items={3} />
            </div>
          </CardContent>
        </Card>

        {/* Traffic Sources Skeleton */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <ChartSkeleton showLegend />
              <ListSkeleton items={4} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Enhanced error and empty state components
function ChartError({ message, onRetry, type = 'error' }: { 
  message: string; 
  onRetry?: () => void;
  type?: 'error' | 'empty' | 'fallback';
}) {
  const getIcon = () => {
    switch (type) {
      case 'empty':
        return <BarChart3 className="h-12 w-12 text-gray-400" />
      case 'fallback':
        return <AlertCircle className="h-12 w-12 text-orange-500" />
      default:
        return <AlertCircle className="h-12 w-12 text-red-500" />
    }
  }

  const getTitle = () => {
    switch (type) {
      case 'empty':
        return 'No data available'
      case 'fallback':
        return 'Chart unavailable'
      default:
        return 'Unable to load chart'
    }
  }

  return (
    <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 space-y-4">
      {getIcon()}
      <div className="text-center max-w-sm">
        <p className="text-base sm:text-lg font-medium text-gray-900 mb-2">{getTitle()}</p>
        <p className="text-sm text-gray-600 mb-4 leading-relaxed">{message}</p>
        {onRetry && (
          <Button onClick={onRetry} variant="outline" size="sm" className="min-w-[120px]">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        )}
      </div>
    </div>
  )
}

// Enhanced empty state component for charts with no data
function ChartEmptyState({ 
  title, 
  description, 
  icon,
  actionLabel,
  onAction 
}: {
  title: string;
  description: string;
  icon?: React.ReactNode;
  actionLabel?: string;
  onAction?: () => void;
}) {
  return (
    <div className="h-64 flex items-center justify-center">
      <div className="text-center max-w-sm px-4">
        {icon && (
          <div className="flex justify-center mb-4">
            {icon}
          </div>
        )}
        <p className="text-gray-500 mb-2 font-medium">{title}</p>
        <p className="text-sm text-gray-400 leading-relaxed mb-4">{description}</p>
        {actionLabel && onAction && (
          <Button onClick={onAction} variant="outline" size="sm">
            {actionLabel}
          </Button>
        )}
      </div>
    </div>
  )
}

// Fallback component for chart rendering failures
function ChartFallback({ 
  chartType, 
  onRetry,
  showData = false,
  dataCount = 0 
}: { 
  chartType: string;
  onRetry?: () => void;
  showData?: boolean;
  dataCount?: number;
}) {
  return (
    <div className="h-64 border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-sm px-4">
        <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-3" />
        <p className="text-sm font-medium text-gray-600 mb-1">
          {chartType} temporarily unavailable
        </p>
        {showData && dataCount > 0 && (
          <p className="text-xs text-gray-500 mb-3">
            {dataCount} data points available
          </p>
        )}
        <p className="text-xs text-gray-500 mb-3">
          Chart rendering failed, but your data is safe
        </p>
        {onRetry && (
          <Button onClick={onRetry} variant="ghost" size="sm" className="text-xs">
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    </div>
  )
}

// Chart wrapper with error boundary for individual charts
function ChartWrapper({ 
  children, 
  fallback, 
  chartType 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
  chartType: string;
}) {
  const [hasError, setHasError] = React.useState(false)

  React.useEffect(() => {
    setHasError(false)
  }, [children])

  if (hasError) {
    return fallback || <ChartFallback chartType={chartType} onRetry={() => {
      setHasError(false)
    }} />
  }

  return (
    <div onError={(e) => {
      setHasError(true)
      analyticsLogger.error(`Chart rendering error in ${chartType}`, {}, new Error(`${e}`))
    }}>
      {children}
    </div>
  )
}

export function AnalyticsChart({ 
  viewsData, 
  clicksData, 
  linkPerformance, 
  topReferrers,
  period,
  isLoading = false,
  error,
  onRetry
}: AnalyticsChartProps) {
  const { handleError } = useAnalyticsErrorHandler('AnalyticsChart')

  // Log component render
  React.useEffect(() => {
    analyticsLogger.debug('AnalyticsChart rendered', {
      viewsDataLength: Array.isArray(viewsData) ? viewsData.length : 0,
      clicksDataLength: Array.isArray(clicksData) ? clicksData.length : 0,
      linkPerformanceLength: Array.isArray(linkPerformance) ? linkPerformance.length : 0,
      topReferrersLength: Array.isArray(topReferrers) ? topReferrers.length : 0,
      period,
      isLoading,
      hasError: !!error
    })
  }, [viewsData, clicksData, linkPerformance, topReferrers, period, isLoading, error])
  // Handle loading state
  if (isLoading) {
    return <AnalyticsChartSkeleton />
  }

  // Handle error state
  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-6">
            <ChartError message={error} onRetry={onRetry} />
          </CardContent>
        </Card>
      </div>
    )
  }

  // Enhanced data validation and sanitization with comprehensive error handling
  let validViewsData: ChartData[] = []
  let validClicksData: ChartData[] = []
  let validLinkPerformance: LinkPerformanceData[] = []
  let validTopReferrers: ReferrerData[] = []

  try {
    // Validate and sanitize views data
    if (isValidChartDataWithLogging(viewsData, 'views')) {
      validViewsData = sanitizeChartData(viewsData, 'views')
    } else {
      analyticsLogger.warn('Using empty views data due to validation failure', {
        operation: 'viewsDataFallback',
        originalDataType: typeof viewsData
      })
      validViewsData = []
    }

    // Validate and sanitize clicks data
    if (isValidChartDataWithLogging(clicksData, 'clicks')) {
      validClicksData = sanitizeChartData(clicksData, 'clicks')
    } else {
      analyticsLogger.warn('Using empty clicks data due to validation failure', {
        operation: 'clicksDataFallback',
        originalDataType: typeof clicksData
      })
      validClicksData = []
    }

    // Validate and sanitize link performance data
    if (isValidLinkPerformanceDataWithLogging(linkPerformance)) {
      validLinkPerformance = sanitizeLinkPerformanceData(linkPerformance)
    } else {
      analyticsLogger.warn('Using empty link performance data due to validation failure', {
        operation: 'linkPerformanceFallback',
        originalDataType: typeof linkPerformance
      })
      validLinkPerformance = []
    }

    // Validate and sanitize referrer data
    if (isValidReferrerDataWithLogging(topReferrers)) {
      validTopReferrers = sanitizeReferrerData(topReferrers)
    } else {
      analyticsLogger.warn('Using empty referrer data due to validation failure', {
        operation: 'referrerDataFallback',
        originalDataType: typeof topReferrers
      })
      validTopReferrers = []
    }

    // Log successful validation summary
    analyticsLogger.debug('Chart data validation and sanitization completed', {
      operation: 'dataValidationSummary',
      viewsCount: validViewsData.length,
      clicksCount: validClicksData.length,
      linkPerformanceCount: validLinkPerformance.length,
      referrersCount: validTopReferrers.length
    })

  } catch (error) {
    handleError(error as Error, { 
      operation: 'data validation and sanitization',
      viewsDataType: typeof viewsData,
      clicksDataType: typeof clicksData,
      linkPerformanceType: typeof linkPerformance,
      topReferrersType: typeof topReferrers
    })
    
    // Use empty arrays as fallback
    validViewsData = []
    validClicksData = []
    validLinkPerformance = []
    validTopReferrers = []

    analyticsLogger.error('Critical error during data validation, using empty fallback data', {
      operation: 'criticalDataValidationError'
    }, error as Error)
  }

  // Calculate trends with validated data
  const calculateTrend = (data: ChartData[]) => {
    if (!data || data.length < 2) return 0
    const recent = data.slice(-7).reduce((sum, item) => sum + (item.count || 0), 0)
    const previous = data.slice(-14, -7).reduce((sum, item) => sum + (item.count || 0), 0)
    if (previous === 0) return recent > 0 ? 100 : 0
    return ((recent - previous) / previous) * 100
  }

  const viewsTrend = calculateTrend(validViewsData)
  const clicksTrend = calculateTrend(validClicksData)

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-green-500" />
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-green-600'
    if (trend < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  // Prepare referrer data for pie chart with proper typing
  const referrerChartData: PieChartData[] = validTopReferrers.map((ref, index) => ({
    name: ref.referrer || 'Direct',
    value: ref.count,
    color: COLORS[index % COLORS.length]
  }))

  // Fixed pie chart label formatter with proper typing
  const renderPieLabel = (props: LabelProps & { name?: string; percent?: number }) => {
    const { name, percent } = props
    if (typeof name === 'string' && typeof percent === 'number') {
      return `${name} ${(percent * 100).toFixed(0)}%`
    }
    return ''
  }

  return (
    <div className="space-y-6">
      {/* Views Over Time */}
      <AnalyticsErrorBoundary chartType="Profile Views Over Time">
        <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-600" />
              <span className="text-base sm:text-lg">Profile Views Over Time</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              {getTrendIcon(viewsTrend)}
              <span className={`text-xs sm:text-sm ${getTrendColor(viewsTrend)}`}>
                {viewsTrend > 0 ? '+' : ''}{viewsTrend.toFixed(1)}% vs last period
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {validViewsData.length > 0 ? (
            <ChartWrapper 
              chartType="Profile Views Line Chart"
              fallback={<ChartFallback chartType="Profile Views Chart" showData dataCount={validViewsData.length} />}
            >
              <div className="h-64 sm:h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={validViewsData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={formatDate}
                      className="text-xs"
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis 
                      className="text-xs" 
                      tick={{ fontSize: 12 }}
                      width={40}
                    />
                    <Tooltip 
                      labelFormatter={(value) => formatDate(value as string)}
                      formatter={(value) => [value, 'Views']}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '14px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="count" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
                      activeDot={{ r: 5, stroke: '#3b82f6', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </ChartWrapper>
          ) : (
            <ChartEmptyState
              title="No view data available"
              description="Data will appear here once people start viewing your profile. Share your profile link to start tracking views!"
              icon={<Eye className="h-8 w-8 text-gray-400" />}
            />
          )}
        </CardContent>
      </Card>
      </AnalyticsErrorBoundary>

      {/* Clicks Over Time */}
      <AnalyticsErrorBoundary chartType="Link Clicks Over Time">
        <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <CardTitle className="flex items-center gap-2">
              <MousePointer className="h-5 w-5 text-green-600" />
              <span className="text-base sm:text-lg">Link Clicks Over Time</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              {getTrendIcon(clicksTrend)}
              <span className={`text-xs sm:text-sm ${getTrendColor(clicksTrend)}`}>
                {clicksTrend > 0 ? '+' : ''}{clicksTrend.toFixed(1)}% vs last period
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {validClicksData.length > 0 ? (
            <ChartWrapper 
              chartType="Link Clicks Line Chart"
              fallback={<ChartFallback chartType="Link Clicks Chart" showData dataCount={validClicksData.length} />}
            >
              <div className="h-64 sm:h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={validClicksData} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={formatDate}
                      className="text-xs"
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis 
                      className="text-xs" 
                      tick={{ fontSize: 12 }}
                      width={40}
                    />
                    <Tooltip 
                      labelFormatter={(value) => formatDate(value as string)}
                      formatter={(value) => [value, 'Clicks']}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '14px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="count" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                      activeDot={{ r: 5, stroke: '#10b981', strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </ChartWrapper>
          ) : (
            <ChartEmptyState
              title="No click data available"
              description="Data will appear here once people start clicking your links. Add some links to your profile to start tracking clicks!"
              icon={<MousePointer className="h-8 w-8 text-gray-400" />}
            />
          )}
        </CardContent>
      </Card>
      </AnalyticsErrorBoundary>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Link Performance */}
        <AnalyticsErrorBoundary chartType="Link Performance">
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Link className="h-5 w-5 text-purple-600" />
              <span className="text-base sm:text-lg">Top Performing Links</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {validLinkPerformance.length > 0 ? (
              <div className="space-y-4">
                <ChartWrapper 
                  chartType="Link Performance Bar Chart"
                  fallback={<ChartFallback chartType="Link Performance Chart" showData dataCount={validLinkPerformance.length} />}
                >
                  <div className="h-64 sm:h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart 
                        data={validLinkPerformance.slice(0, 5)} 
                        margin={{ top: 5, right: 5, left: 5, bottom: 60 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="title" 
                          className="text-xs"
                          tick={{ fontSize: 10 }}
                          interval={0}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                        />
                        <YAxis 
                          className="text-xs" 
                          tick={{ fontSize: 12 }}
                          width={40}
                        />
                        <Tooltip 
                          formatter={(value, name) => [value, name === 'totalClicks' ? 'Total Clicks' : 'Period Clicks']}
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            fontSize: '14px'
                          }}
                        />
                        <Bar dataKey="totalClicks" fill="#3b82f6" name="totalClicks" radius={[2, 2, 0, 0]} />
                        <Bar dataKey="periodClicks" fill="#10b981" name="periodClicks" radius={[2, 2, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </ChartWrapper>
                
                {/* Link Performance List - Enhanced for mobile */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {validLinkPerformance.slice(0, 5).map((link, index) => (
                    <div key={link.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <span className="text-sm font-medium text-gray-500 flex-shrink-0">#{index + 1}</span>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm truncate" title={link.title}>
                            {link.title}
                          </p>
                          <p className="text-xs text-gray-500 truncate" title={link.url}>
                            {link.url}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        <Badge variant={link.isVisible ? "default" : "secondary"} className="text-xs">
                          {link.totalClicks} clicks
                        </Badge>
                        {!link.isVisible && (
                          <Badge variant="outline" className="text-xs hidden sm:inline-flex">Hidden</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <ChartEmptyState
                title="No links to analyze yet"
                description="Add some links to your profile to see performance data and track which ones get the most clicks."
                icon={<Link className="h-8 w-8 text-gray-400" />}
              />
            )}
          </CardContent>
        </Card>
        </AnalyticsErrorBoundary>

        {/* Traffic Sources */}
        <AnalyticsErrorBoundary chartType="Traffic Sources">
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-orange-600" />
              <span className="text-base sm:text-lg">Traffic Sources</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {referrerChartData.length > 0 ? (
              <div className="space-y-4">
                <ChartWrapper 
                  chartType="Traffic Sources Pie Chart"
                  fallback={<ChartFallback chartType="Traffic Sources Chart" showData dataCount={referrerChartData.length} />}
                >
                  <div className="h-64 sm:h-72">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={referrerChartData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={renderPieLabel}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {referrerChartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip 
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            fontSize: '14px'
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </ChartWrapper>
                
                {/* Referrer List - Enhanced for mobile */}
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {validTopReferrers.map((referrer, index) => (
                    <div key={`referrer-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-center space-x-3 min-w-0 flex-1">
                        <div 
                          className="w-3 h-3 rounded-full flex-shrink-0" 
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        />
                        <span className="text-sm font-medium truncate" title={referrer.referrer}>
                          {referrer.referrer || 'Direct'}
                        </span>
                      </div>
                      <Badge variant="outline" className="text-xs flex-shrink-0">
                        {referrer.count} views
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <ChartEmptyState
                title="No referrer data yet"
                description="Traffic sources will appear here as people visit your profile from different websites and platforms."
                icon={<Globe className="h-8 w-8 text-gray-400" />}
              />
            )}
          </CardContent>
        </Card>
        </AnalyticsErrorBoundary>
      </div>
    </div>
  )
}