'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ErrorDisplay, ValidationErrorDisplay, ServerErrorDisplay } from '@/components/ui/error-display'
import { ErrorFallback } from '@/components/ui/error-fallback'
import { ComponentErrorBoundary } from '@/components/error-boundaries/app-error-boundary'
import { useErrorHandler, useFormSubmission, useAsyncOperation } from '@/lib/hooks/use-error-handler'
import { ErrorFactory } from '@/lib/errors/app-errors'
import { createLink } from '@/lib/actions/links'

// Example component demonstrating error handling patterns
export function ErrorHandlingExample() {
  const [formData, setFormData] = useState({ title: '', url: '' })
  const { handleError, isError, errorMessage, clearError } = useErrorHandler({
    context: 'error-handling-example'
  })

  // Example: Form submission with error handling
  const {
    submit: submitForm,
    isSubmitting,
    isSuccess,
    error: formError,
    clearError: clearFormError
  } = useFormSubmission(async (data: typeof formData) => {
    // Simulate form validation
    if (!data.title.trim()) {
      throw ErrorFactory.missingRequiredField('title')
    }
    if (!data.url.trim()) {
      throw ErrorFactory.missingRequiredField('url')
    }
    if (!data.url.startsWith('http')) {
      throw ErrorFactory.invalidLinkUrl(data.url)
    }

    // Call server action
    return await createLink(data)
  })

  // Example: Async operation with error handling
  const {
    execute: fetchData,
    isLoading,
    error: fetchError,
    clearError: clearFetchError
  } = useAsyncOperation(async () => {
    const response = await fetch('/api/some-endpoint')
    if (!response.ok) {
      throw ErrorFactory.networkError()
    }
    return response.json()
  })

  // Example: Manual error handling
  const triggerError = (errorType: string) => {
    try {
      switch (errorType) {
        case 'validation':
          throw ErrorFactory.validationError('username', 'Username must be at least 3 characters')
        case 'unauthorized':
          throw ErrorFactory.unauthorized()
        case 'not-found':
          throw ErrorFactory.profileNotFound('123')
        case 'server':
          throw ErrorFactory.internalError('Something went wrong on the server')
        case 'network':
          throw ErrorFactory.networkError()
        default:
          throw new Error('Unknown error type')
      }
    } catch (error) {
      handleError(error, { errorType })
    }
  }

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Error Handling Examples</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Manual Error Display */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Manual Error Triggers</h3>
            <div className="flex flex-wrap gap-2 mb-4">
              <Button
                onClick={() => triggerError('validation')}
                variant="outline"
                size="sm"
              >
                Validation Error
              </Button>
              <Button
                onClick={() => triggerError('unauthorized')}
                variant="outline"
                size="sm"
              >
                Unauthorized Error
              </Button>
              <Button
                onClick={() => triggerError('not-found')}
                variant="outline"
                size="sm"
              >
                Not Found Error
              </Button>
              <Button
                onClick={() => triggerError('server')}
                variant="outline"
                size="sm"
              >
                Server Error
              </Button>
              <Button
                onClick={() => triggerError('network')}
                variant="outline"
                size="sm"
              >
                Network Error
              </Button>
            </div>

            {isError && (
              <ErrorDisplay
                error={new Error(errorMessage || 'Unknown error')}
                title="Manual Error"
                showRetry
                showReport
                onRetry={clearError}
                onDismiss={clearError}
                className="mb-4"
              />
            )}
          </div>

          {/* Form Submission Example */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Form Submission with Error Handling</h3>
            <form
              onSubmit={(e) => {
                e.preventDefault()
                submitForm(formData)
              }}
              className="space-y-4"
            >
              <div>
                <Label htmlFor="title">Link Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter link title"
                />
              </div>

              <div>
                <Label htmlFor="url">Link URL</Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://example.com"
                />
              </div>

              {formError && (
                <ValidationErrorDisplay
                  error={formError}
                  onDismiss={clearFormError}
                />
              )}

              {isSuccess && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-700">
                  Link created successfully!
                </div>
              )}

              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Link'}
              </Button>
            </form>
          </div>

          {/* Async Operation Example */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Async Operation with Error Handling</h3>
            <Button
              onClick={() => fetchData()}
              disabled={isLoading}
              className="mb-4"
            >
              {isLoading ? 'Loading...' : 'Fetch Data'}
            </Button>

            {fetchError && (
              <ServerErrorDisplay
                error={fetchError}
                onRetry={() => fetchData()}
              />
            )}
          </div>

          {/* Error Boundary Example */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Error Boundary Example</h3>
            <ComponentErrorBoundary context="error-boundary-example">
              <ErrorProneComponent />
            </ComponentErrorBoundary>
          </div>

          {/* Error Fallback Examples */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Error Fallback UI Examples</h3>
            <div className="space-y-4">
              <ErrorFallback
                error={ErrorFactory.unauthorized()}
                variant="inline"
              />
              
              <ErrorFallback
                error={ErrorFactory.profileNotFound('123')}
                variant="card"
                size="sm"
                showReportButton
              />
              
              <ErrorFallback
                error={ErrorFactory.networkError()}
                variant="card"
                showHomeButton
                resetError={() => console.log('Reset error')}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Example component that throws errors for demonstration
function ErrorProneComponent() {
  const [shouldError, setShouldError] = useState(false)

  if (shouldError) {
    throw ErrorFactory.internalError('This component intentionally threw an error')
  }

  return (
    <div className="p-4 border rounded-md">
      <p className="mb-2">This component is wrapped in an error boundary.</p>
      <Button
        onClick={() => setShouldError(true)}
        variant="destructive"
        size="sm"
      >
        Throw Error
      </Button>
    </div>
  )
}