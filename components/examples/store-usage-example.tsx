"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  useProfileData, 
  useLinksData, 
  useThemeData,
  useOptimisticUpdates,
  useGlobalLoadingState,
  useGlobalErrorState,
  clearAllErrors
} from '@/lib/stores'

/**
 * Example component demonstrating store usage with optimistic updates
 * This is for reference and testing purposes
 */
export function StoreUsageExample() {
  const [displayName, setDisplayName] = useState('')
  const [linkTitle, setLinkTitle] = useState('')
  const [linkUrl, setLinkUrl] = useState('')

  // Get current data from stores
  const currentUser = useProfileData()
  const currentLinks = useLinksData()
  const currentTheme = useThemeData()

  // Get optimistic update functions
  const { profile, links, theme } = useOptimisticUpdates()

  // Get global loading and error states
  const isGlobalLoading = useGlobalLoadingState()
  const globalErrors = useGlobalErrorState()

  const handleUpdateProfile = async () => {
    if (!displayName.trim()) return
    
    await profile.updateProfile({
      displayName: displayName.trim(),
      bio: currentUser?.bio || null,
      profileImage: currentUser?.profileImage || null
    })
    
    setDisplayName('')
  }

  const handleCreateLink = async () => {
    if (!linkTitle.trim() || !linkUrl.trim()) return
    
    await links.createLink({
      title: linkTitle.trim(),
      url: linkUrl.trim(),
      icon: null
    })
    
    setLinkTitle('')
    setLinkUrl('')
  }

  const handleUpdateTheme = async () => {
    await theme.updateTheme({
      primaryColor: '#ff6b6b',
      secondaryColor: '#4ecdc4'
    })
  }

  const handleApplyPreset = async () => {
    await theme.applyPreset('Dark')
  }

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Store Usage Example</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Global Loading State */}
          {isGlobalLoading && (
            <div className="text-sm text-blue-600">
              Loading...
            </div>
          )}

          {/* Global Error State */}
          {globalErrors && (
            <div className="space-y-2">
              {globalErrors.map((error, index) => (
                <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                  {error}
                </div>
              ))}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearAllErrors}
              >
                Clear Errors
              </Button>
            </div>
          )}

          {/* Current User Data */}
          <div className="space-y-2">
            <h3 className="font-semibold">Current User:</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(currentUser, null, 2)}
            </pre>
          </div>

          {/* Profile Update */}
          <div className="space-y-2">
            <h3 className="font-semibold">Update Profile:</h3>
            <div className="flex gap-2">
              <Input
                placeholder="Display name"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
              />
              <Button 
                onClick={handleUpdateProfile}
                disabled={!displayName.trim() || profile.isLoading}
              >
                Update
              </Button>
            </div>
          </div>

          {/* Current Links */}
          <div className="space-y-2">
            <h3 className="font-semibold">Current Links ({currentLinks.length}):</h3>
            <div className="space-y-1 max-h-32 overflow-auto">
              {currentLinks.map((link) => (
                <div key={link.id} className="text-sm bg-gray-50 p-2 rounded flex justify-between">
                  <span>{link.title} - {link.url}</span>
                  <div className="space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => links.toggleLinkVisibility(link.id)}
                    >
                      {link.isVisible ? 'Hide' : 'Show'}
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => links.deleteLink(link.id)}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Create Link */}
          <div className="space-y-2">
            <h3 className="font-semibold">Create Link:</h3>
            <div className="flex gap-2">
              <Input
                placeholder="Link title"
                value={linkTitle}
                onChange={(e) => setLinkTitle(e.target.value)}
              />
              <Input
                placeholder="Link URL"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
              />
              <Button 
                onClick={handleCreateLink}
                disabled={!linkTitle.trim() || !linkUrl.trim() || links.isLoading}
              >
                Create
              </Button>
            </div>
          </div>

          {/* Current Theme */}
          <div className="space-y-2">
            <h3 className="font-semibold">Current Theme:</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(currentTheme, null, 2)}
            </pre>
          </div>

          {/* Theme Updates */}
          <div className="space-y-2">
            <h3 className="font-semibold">Theme Actions:</h3>
            <div className="flex gap-2">
              <Button 
                onClick={handleUpdateTheme}
                disabled={theme.isLoading}
              >
                Update Colors
              </Button>
              <Button 
                onClick={handleApplyPreset}
                disabled={theme.isLoading}
              >
                Apply Dark Preset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}