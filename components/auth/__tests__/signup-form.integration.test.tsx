import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { signIn } from 'next-auth/react'
import { SignUpForm } from '../signup-form'

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

jest.mock('next-auth/react', () => ({
  signIn: jest.fn(),
}))

// Mock the username availability hook
jest.mock('@/lib/hooks/use-username-availability', () => ({
  useUsernameAvailability: jest.fn(() => ({
    status: { checking: false, available: undefined, error: undefined },
    checkAvailability: jest.fn(),
    reset: jest.fn(),
  })),
}))

// Mock fetch for API calls
global.fetch = jest.fn()

const mockPush = jest.fn()
const mockSignIn = signIn as jest.MockedFunction<typeof signIn>
const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('SignUpForm Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    })
  })

  afterEach(() => {
    jest.resetAllMocks()
  })

  it('should render signup form with enhanced username input', () => {
    render(<SignUpForm />)
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/display name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
  })

  it('should prevent form submission when username is being checked', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: true, available: undefined, error: undefined },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Button should be disabled and show checking state
    const submitButton = screen.getByRole('button', { name: /checking username/i })
    expect(submitButton).toBeDisabled()

    // Try to submit form
    fireEvent.click(submitButton)

    // Should not make API call
    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should prevent form submission when username is unavailable', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: false, available: false, error: 'Username not available' },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'takenuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Button should be disabled
    const submitButton = screen.getByRole('button', { name: /create account/i })
    expect(submitButton).toBeDisabled()

    // Try to submit form
    fireEvent.click(submitButton)

    // Should not make API call
    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should allow form submission when username is available', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: false, available: true, error: undefined },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    // Mock successful API responses
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    } as Response)

    mockSignIn.mockResolvedValueOnce({
      error: null,
      status: 200,
      ok: true,
      url: null,
    })

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'availableuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Button should be enabled
    const submitButton = screen.getByRole('button', { name: /create account/i })
    expect(submitButton).not.toBeDisabled()

    // Submit form
    fireEvent.click(submitButton)

    // Should make API call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
          username: 'availableuser',
          displayName: 'Test User',
        }),
      })
    })

    // Should sign in user after successful registration
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith('credentials', {
        email: '<EMAIL>',
        password: 'password123',
        redirect: false,
      })
    })

    // Should redirect to dashboard
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })
  })

  it('should show error when username validation fails during submission', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: false, available: false, error: 'Username not available' },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    render(<SignUpForm />)
    
    // Fill out form with unavailable username
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'takenuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Force submit by programmatically calling the form submit
    const form = screen.getByRole('button', { name: /create account/i }).closest('form')
    if (form) {
      fireEvent.submit(form)
    }

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/please choose an available username/i)).toBeInTheDocument()
    })
  })

  it('should show error when trying to submit while username is being checked', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: true, available: undefined, error: undefined },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'checkinguser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Force submit by programmatically calling the form submit
    const form = screen.getByRole('button', { name: /checking username/i }).closest('form')
    if (form) {
      fireEvent.submit(form)
    }

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/please wait for username availability check to complete/i)).toBeInTheDocument()
    })
  })

  it('should handle API errors during signup', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: false, available: true, error: undefined },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    // Mock API error
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ error: 'Username already exists' }),
    } as Response)

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Should show API error
    await waitFor(() => {
      expect(screen.getByText(/username already exists/i)).toBeInTheDocument()
    })
  })

  it('should handle sign-in errors after successful registration', async () => {
    const { useUsernameAvailability } = require('@/lib/hooks/use-username-availability')
    useUsernameAvailability.mockReturnValue({
      status: { checking: false, available: true, error: undefined },
      checkAvailability: jest.fn(),
      reset: jest.fn(),
    })

    // Mock successful registration but failed sign-in
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true }),
    } as Response)

    mockSignIn.mockResolvedValueOnce({
      error: 'Sign-in failed',
      status: 401,
      ok: false,
      url: null,
    })

    render(<SignUpForm />)
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/username/i), {
      target: { value: 'testuser' }
    })
    fireEvent.change(screen.getByLabelText(/display name/i), {
      target: { value: 'Test User' }
    })
    fireEvent.change(screen.getByLabelText(/^password$/i), {
      target: { value: 'password123' }
    })
    fireEvent.change(screen.getByLabelText(/confirm password/i), {
      target: { value: 'password123' }
    })

    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /create account/i }))

    // Should show sign-in error
    await waitFor(() => {
      expect(screen.getByText(/registration successful, but sign-in failed/i)).toBeInTheDocument()
    })
  })
})