'use client'

import { signOut } from 'next-auth/react'
import { Button, ButtonProps } from '@/components/ui/button'
import { LogOut } from 'lucide-react'
import { forwardRef } from 'react'

interface SignOutButtonProps extends ButtonProps {
  showIcon?: boolean
}

export const SignOutButton = forwardRef<HTMLButtonElement, SignOutButtonProps>(
  ({ showIcon = true, children, ...props }, ref) => {
    const handleSignOut = () => {
      signOut({ callbackUrl: '/' })
    }

    return (
      <Button ref={ref} variant="outline" onClick={handleSignOut} {...props}>
        {showIcon && <LogOut className="mr-2 h-4 w-4" />}
        {children || 'Sign Out'}
      </Button>
    )
  }
)

SignOutButton.displayName = 'SignOutButton'