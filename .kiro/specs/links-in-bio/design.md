# Design Document

## Overview

LinksInBio is a modern web application built with Next.js 15, React 19, and shadcn/ui that enables users to create customizable profile pages with multiple links. The application follows a server-first architecture using Next.js App Router, Server Components, and Server Actions for optimal performance and SEO.

The system is designed with a clean separation of concerns, utilizing modern React patterns, TypeScript for type safety, and a component-based architecture that ensures maintainability and scalability.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Client Browser] --> B[Next.js App Router]
    B --> C[Server Components]
    B --> D[Client Components]
    C --> E[Server Actions]
    E --> F[Database Layer]
    F --> G[SQLite/Postgres]
    
    B --> H[Authentication Layer]
    H --> I[NextAuth.js]
    
    B --> J[File Storage]
    J --> K[Uploadthing]
    
    L[Public Profile Pages] --> M[Static Generation]
    M --> N[CDN/Vercel Edge]
```

### Technology Stack Implementation

- **Framework**: Next.js 15 with App Router for file-based routing and server-side rendering
- **React**: Version 19 with Server Components for optimal performance
- **UI Framework**: shadcn/ui with Tailwind CSS v4 for consistent, accessible components
- **State Management**: Zustand for client-side state management
- **Database**: Prisma ORM with SQLite (development) / Postgres (production)
- **Authentication**: NextAuth.js with multiple providers
- **File Uploads**: Uploadthing for profile images and backgrounds
- **Deployment**: Vercel with edge functions and CDN

## Components and Interfaces

### Core Components Structure

```
components/
├── ui/                     # shadcn/ui components
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   └── ...
├── auth/                   # Authentication components
│   ├── login-form.tsx
│   ├── signup-form.tsx
│   └── auth-provider.tsx
├── dashboard/              # Dashboard-specific components
│   ├── link-manager.tsx
│   ├── profile-editor.tsx
│   ├── theme-customizer.tsx
│   └── analytics-chart.tsx
├── profile/                # Public profile components
│   ├── profile-page.tsx
│   ├── link-card.tsx
│   └── theme-wrapper.tsx
└── shared/                 # Shared components
    ├── header.tsx
    ├── navigation.tsx
    └── loading-states.tsx
```

### Key Interfaces

```typescript
// User and Profile Types
interface User {
  id: string
  email: string
  username: string
  displayName: string
  bio?: string
  profileImage?: string
  createdAt: Date
  updatedAt: Date
}

interface Profile {
  id: string
  userId: string
  slug: string
  theme: ProfileTheme
  backgroundType: 'color' | 'gradient' | 'image'
  backgroundValue: string
  isPublic: boolean
  viewCount: number
}

interface Link {
  id: string
  profileId: string
  title: string
  url: string
  icon?: string
  isVisible: boolean
  order: number
  clickCount: number
  createdAt: Date
}

interface ProfileTheme {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  textColor: string
  fontFamily: string
  preset?: string
}
```

### API Routes Structure

```
app/api/
├── auth/                   # NextAuth.js routes
│   └── [...nextauth]/
├── profile/
│   ├── route.ts           # GET, PUT profile data
│   └── [slug]/
│       └── route.ts       # GET public profile
├── links/
│   ├── route.ts           # GET, POST links
│   ├── [id]/
│   │   └── route.ts       # PUT, DELETE specific link
│   └── reorder/
│       └── route.ts       # PUT reorder links
├── analytics/
│   ├── views/
│   │   └── route.ts       # POST track profile view
│   └── clicks/
│       └── route.ts       # POST track link click
└── upload/
    └── route.ts           # POST file upload
```

## Data Models

### Database Schema (Prisma)

```prisma
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  displayName String
  bio         String?
  profileImage String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  profile     Profile?
  accounts    Account[]
  sessions    Session[]
}

model Profile {
  id              String   @id @default(cuid())
  userId          String   @unique
  slug            String   @unique
  theme           Json     // ProfileTheme object
  backgroundType  String   @default("color")
  backgroundValue String   @default("#ffffff")
  isPublic        Boolean  @default(true)
  viewCount       Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  links           Link[]
}

model Link {
  id          String   @id @default(cuid())
  profileId   String
  title       String
  url         String
  icon        String?
  isVisible   Boolean  @default(true)
  order       Int
  clickCount  Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  
  @@index([profileId, order])
}

// NextAuth.js required models
model Account {
  // ... NextAuth.js account model
}

model Session {
  // ... NextAuth.js session model
}
```

### State Management (Zustand)

```typescript
// stores/profile-store.ts
interface ProfileStore {
  profile: Profile | null
  links: Link[]
  isLoading: boolean
  
  // Actions
  setProfile: (profile: Profile) => void
  updateProfile: (updates: Partial<Profile>) => void
  addLink: (link: Omit<Link, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateLink: (id: string, updates: Partial<Link>) => void
  deleteLink: (id: string) => void
  reorderLinks: (links: Link[]) => void
  toggleLinkVisibility: (id: string) => void
}

// stores/theme-store.ts
interface ThemeStore {
  currentTheme: ProfileTheme
  presets: ProfileTheme[]
  
  // Actions
  setTheme: (theme: ProfileTheme) => void
  updateThemeProperty: (key: keyof ProfileTheme, value: string) => void
  applyPreset: (preset: string) => void
}
```

## Error Handling

### Error Boundaries and Handling Strategy

```typescript
// components/error-boundary.tsx
class ProfileErrorBoundary extends React.Component {
  // Handle profile-specific errors
}

// lib/error-handler.ts
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
  }
}

// Error types
export const ErrorCodes = {
  PROFILE_NOT_FOUND: 'PROFILE_NOT_FOUND',
  INVALID_SLUG: 'INVALID_SLUG',
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UPLOAD_FAILED: 'UPLOAD_FAILED'
} as const
```

### API Error Responses

```typescript
// Standard error response format
interface ErrorResponse {
  error: {
    message: string
    code?: string
    details?: any
  }
  timestamp: string
  path: string
}

// Server Actions error handling
export async function createLink(data: CreateLinkData) {
  try {
    // ... implementation
  } catch (error) {
    if (error instanceof AppError) {
      return { error: error.message, code: error.code }
    }
    return { error: 'Internal server error' }
  }
}
```

## Testing Strategy

### Testing Approach

1. **Unit Tests**: Jest + React Testing Library for components
2. **Integration Tests**: API routes and Server Actions
3. **E2E Tests**: Playwright for critical user flows
4. **Visual Regression**: Chromatic for UI consistency

### Test Structure

```
__tests__/
├── components/
│   ├── auth/
│   ├── dashboard/
│   └── profile/
├── api/
│   ├── profile.test.ts
│   ├── links.test.ts
│   └── analytics.test.ts
├── lib/
│   ├── utils.test.ts
│   └── validation.test.ts
└── e2e/
    ├── auth-flow.spec.ts
    ├── profile-creation.spec.ts
    └── link-management.spec.ts
```

### Key Test Scenarios

- User authentication flows (signup, login, logout)
- Profile creation and customization
- Link CRUD operations and reordering
- Theme application and persistence
- Public profile rendering and analytics tracking
- Error handling and edge cases
- Mobile responsiveness and accessibility

## Security Considerations

### Authentication & Authorization

- NextAuth.js with secure session management
- CSRF protection enabled by default
- Secure HTTP-only cookies for sessions
- Rate limiting on API endpoints
- Input validation and sanitization

### Data Protection

- User data encryption at rest
- Secure file upload validation
- XSS prevention through React's built-in protections
- SQL injection prevention via Prisma ORM
- Environment variable protection for secrets

### Privacy

- Analytics data anonymization
- GDPR compliance for data export/deletion
- Optional profile visibility controls
- Secure slug generation to prevent enumeration

## Performance Optimizations

### Next.js Optimizations

- Server Components for reduced client-side JavaScript
- Static generation for public profiles
- Image optimization with next/image
- Font optimization with next/font
- Bundle analysis and code splitting

### Database Optimizations

- Proper indexing on frequently queried fields
- Connection pooling for database efficiency
- Caching strategies for public profiles
- Optimistic updates for better UX

### Caching Strategy

- Static generation for public profiles
- API route caching for analytics data
- Client-side caching with SWR patterns
- CDN caching for static assets

This design provides a solid foundation for implementing the LinksInBio application with modern best practices, scalability considerations, and a focus on performance and user experience.