# Design Document

## Overview

The Link Scheduling and Conditional Links feature extends the existing LinksInBio application with advanced link management capabilities. This system enables users to create time-limited links that appear only during specific periods and conditional links that adapt based on visitor context (referrer, location, device, time). The design leverages the existing Next.js 15 architecture while introducing new data models, rule evaluation engines, and management interfaces.

The system is built with performance as a priority, using server-side rule evaluation, efficient caching strategies, and minimal client-side JavaScript to ensure fast loading times even with complex conditional logic.

## Architecture

### Extended High-Level Architecture

```mermaid
graph TB
    A[Client Browser] --> B[Next.js App Router]
    B --> C[Server Components]
    B --> D[Client Components]
    C --> E[Server Actions]
    E --> F[Database Layer]
    F --> G[SQLite/Postgres]
    
    B --> H[Authentication Layer]
    H --> I[NextAuth.js]
    
    B --> J[File Storage]
    J --> K[Uploadthing]
    
    L[Public Profile Pages] --> M[Rule Evaluation Engine]
    M --> N[Visitor Context Detection]
    N --> O[Geolocation Service]
    N --> P[Device Detection]
    N --> Q[Time/Schedule Evaluation]
    
    M --> R[Link Filtering & Rendering]
    R --> S[CDN/Vercel Edge]
    
    T[Analytics Engine] --> U[Conditional Analytics]
    U --> V[Rule Performance Tracking]
```

### New Technology Integrations

- **Geolocation**: IP-based location detection using Vercel's edge functions or MaxMind GeoLite2
- **Device Detection**: User-agent parsing with `ua-parser-js` library
- **Time Zone Handling**: `date-fns-tz` for accurate timezone-aware scheduling
- **Rule Engine**: Custom rule evaluation system with priority-based matching
- **Caching**: Redis or Vercel KV for rule evaluation caching

## Components and Interfaces

### Extended Components Structure

```
components/
├── ui/                     # shadcn/ui components
│   ├── button.tsx
│   ├── input.tsx
│   ├── card.tsx
│   ├── date-time-picker.tsx    # New: DateTime selection
│   ├── condition-builder.tsx   # New: Visual rule builder
│   ├── rule-tester.tsx        # New: Rule testing interface
│   └── ...
├── dashboard/              # Dashboard-specific components
│   ├── link-manager.tsx
│   ├── conditional-link-form.tsx  # New: Enhanced link form
│   ├── rule-builder.tsx          # New: Rule configuration
│   ├── schedule-picker.tsx       # New: Time scheduling
│   ├── condition-preview.tsx     # New: Rule preview
│   └── conditional-analytics.tsx # New: Enhanced analytics
├── profile/                # Public profile components
│   ├── profile-page.tsx
│   ├── conditional-link-renderer.tsx # New: Smart link rendering
│   └── visitor-context-provider.tsx  # New: Context detection
└── lib/
    ├── rule-engine/        # New: Rule evaluation system
    │   ├── evaluator.ts
    │   ├── conditions.ts
    │   └── scheduler.ts
    └── visitor-detection/  # New: Visitor context detection
        ├── device.ts
        ├── location.ts
        └── referrer.ts
```

### Enhanced Data Models

```typescript
// Extended Link interface with conditional rules
interface Link {
  id: string
  profileId: string
  title: string
  url: string
  icon?: string
  isVisible: boolean
  order: number
  clickCount: number
  
  // New scheduling fields
  isScheduled: boolean
  scheduleStart?: Date
  scheduleEnd?: Date
  timezone?: string
  
  // New conditional rules
  hasConditions: boolean
  conditions: LinkCondition[]
  defaultBehavior: 'show' | 'hide'
  
  createdAt: Date
  updatedAt: Date
}

// Link condition system
interface LinkCondition {
  id: string
  linkId: string
  type: ConditionType
  priority: number
  isActive: boolean
  
  // Condition-specific data
  referrerRules?: ReferrerRule[]
  locationRules?: LocationRule[]
  deviceRules?: DeviceRule[]
  timeRules?: TimeRule[]
  
  // Action when condition matches
  action: ConditionAction
  
  createdAt: Date
  updatedAt: Date
}

type ConditionType = 'referrer' | 'location' | 'device' | 'time' | 'schedule'

interface ConditionAction {
  type: 'show' | 'hide' | 'redirect'
  value?: string // For redirect actions
  alternateTitle?: string
  alternateIcon?: string
}

// Specific rule types
interface ReferrerRule {
  domains: string[]
  matchType: 'exact' | 'contains' | 'regex'
  caseSensitive: boolean
}

interface LocationRule {
  countries?: string[]
  regions?: string[]
  cities?: string[]
  excludeCountries?: string[]
}

interface DeviceRule {
  types: ('mobile' | 'tablet' | 'desktop')[]
  platforms: ('ios' | 'android' | 'windows' | 'macos' | 'linux')[]
  browsers?: ('chrome' | 'firefox' | 'safari' | 'edge')[]
}

interface TimeRule {
  daysOfWeek: number[] // 0-6, Sunday = 0
  timeRanges: TimeRange[]
  timezone: string
}

interface TimeRange {
  start: string // HH:mm format
  end: string   // HH:mm format
}

// Visitor context for rule evaluation
interface VisitorContext {
  referrer?: string
  userAgent: string
  ipAddress: string
  country?: string
  region?: string
  city?: string
  device: {
    type: 'mobile' | 'tablet' | 'desktop'
    platform: string
    browser: string
  }
  timestamp: Date
  timezone?: string
}

// Rule evaluation result
interface RuleEvaluationResult {
  linkId: string
  shouldShow: boolean
  matchedConditions: string[]
  appliedAction: ConditionAction
  evaluationTime: number
}
```

### Database Schema Extensions

```prisma
// Extended Link model
model Link {
  id          String   @id @default(cuid())
  profileId   String
  title       String
  url         String
  icon        String?
  isVisible   Boolean  @default(true)
  order       Int
  clickCount  Int      @default(0)
  
  // Scheduling fields
  isScheduled     Boolean   @default(false)
  scheduleStart   DateTime?
  scheduleEnd     DateTime?
  timezone        String?   @default("UTC")
  
  // Conditional rules
  hasConditions   Boolean   @default(false)
  defaultBehavior String    @default("show") // 'show' | 'hide'
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  profile     Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  linkClicks  LinkClick[]
  conditions  LinkCondition[]
  
  @@index([profileId, order])
  @@index([isScheduled, scheduleStart, scheduleEnd])
}

// New LinkCondition model
model LinkCondition {
  id          String   @id @default(cuid())
  linkId      String
  type        String   // 'referrer' | 'location' | 'device' | 'time'
  priority    Int      @default(0)
  isActive    Boolean  @default(true)
  
  // JSON fields for flexible rule storage
  rules       Json     // Condition-specific rules
  action      Json     // Action to take when condition matches
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  link        Link     @relation(fields: [linkId], references: [id], onDelete: Cascade)
  
  @@index([linkId, priority])
  @@index([type, isActive])
}

// Enhanced analytics for conditional links
model LinkClick {
  id        String   @id @default(cuid())
  linkId    String
  profileId String
  timestamp DateTime @default(now())
  userAgent String?
  ipHash    String?
  referrer  String?
  country   String?
  
  // New fields for conditional analytics
  conditionId     String?  // Which condition triggered this link
  conditionType   String?  // Type of condition that matched
  visitorContext  Json?    // Anonymized visitor context
  
  link      Link     @relation(fields: [linkId], references: [id], onDelete: Cascade)
  profile   Profile  @relation(fields: [profileId], references: [id], onDelete: Cascade)
  
  @@index([linkId, timestamp])
  @@index([profileId, timestamp])
  @@index([conditionId])
}
```

## Rule Evaluation Engine

### Core Rule Evaluator

```typescript
// lib/rule-engine/evaluator.ts
export class RuleEvaluator {
  constructor(
    private visitorContext: VisitorContext,
    private currentTime: Date = new Date()
  ) {}

  async evaluateLink(link: Link): Promise<RuleEvaluationResult> {
    const startTime = performance.now()
    
    // Check scheduling first (highest priority)
    if (link.isScheduled) {
      const scheduleResult = this.evaluateSchedule(link)
      if (!scheduleResult.shouldShow) {
        return {
          linkId: link.id,
          shouldShow: false,
          matchedConditions: ['schedule'],
          appliedAction: { type: 'hide' },
          evaluationTime: performance.now() - startTime
        }
      }
    }
    
    // Evaluate conditional rules by priority
    if (link.hasConditions && link.conditions.length > 0) {
      const sortedConditions = link.conditions
        .filter(c => c.isActive)
        .sort((a, b) => b.priority - a.priority)
      
      for (const condition of sortedConditions) {
        const conditionResult = await this.evaluateCondition(condition)
        if (conditionResult.matches) {
          return {
            linkId: link.id,
            shouldShow: conditionResult.action.type === 'show',
            matchedConditions: [condition.id],
            appliedAction: conditionResult.action,
            evaluationTime: performance.now() - startTime
          }
        }
      }
    }
    
    // Apply default behavior
    const shouldShow = link.defaultBehavior === 'show'
    return {
      linkId: link.id,
      shouldShow,
      matchedConditions: [],
      appliedAction: { type: shouldShow ? 'show' : 'hide' },
      evaluationTime: performance.now() - startTime
    }
  }

  private evaluateSchedule(link: Link): { shouldShow: boolean } {
    if (!link.scheduleStart && !link.scheduleEnd) {
      return { shouldShow: true }
    }
    
    const now = this.currentTime
    const timezone = link.timezone || 'UTC'
    
    // Convert times to the link's timezone for comparison
    const nowInTimezone = convertToTimezone(now, timezone)
    
    if (link.scheduleStart && nowInTimezone < link.scheduleStart) {
      return { shouldShow: false }
    }
    
    if (link.scheduleEnd && nowInTimezone > link.scheduleEnd) {
      return { shouldShow: false }
    }
    
    return { shouldShow: true }
  }

  private async evaluateCondition(condition: LinkCondition): Promise<{
    matches: boolean
    action: ConditionAction
  }> {
    switch (condition.type) {
      case 'referrer':
        return this.evaluateReferrerCondition(condition)
      case 'location':
        return await this.evaluateLocationCondition(condition)
      case 'device':
        return this.evaluateDeviceCondition(condition)
      case 'time':
        return this.evaluateTimeCondition(condition)
      default:
        return { matches: false, action: { type: 'hide' } }
    }
  }

  private evaluateReferrerCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as ReferrerRule
    const referrer = this.visitorContext.referrer
    
    if (!referrer) {
      return { matches: false, action: condition.action as ConditionAction }
    }
    
    const matches = rules.domains.some(domain => {
      switch (rules.matchType) {
        case 'exact':
          return rules.caseSensitive 
            ? referrer === domain
            : referrer.toLowerCase() === domain.toLowerCase()
        case 'contains':
          return rules.caseSensitive
            ? referrer.includes(domain)
            : referrer.toLowerCase().includes(domain.toLowerCase())
        case 'regex':
          const flags = rules.caseSensitive ? 'g' : 'gi'
          return new RegExp(domain, flags).test(referrer)
        default:
          return false
      }
    })
    
    return {
      matches,
      action: condition.action as ConditionAction
    }
  }

  private async evaluateLocationCondition(condition: LinkCondition): Promise<{
    matches: boolean
    action: ConditionAction
  }> {
    const rules = condition.rules as LocationRule
    const { country, region, city } = this.visitorContext
    
    // Check exclusions first
    if (rules.excludeCountries && country && rules.excludeCountries.includes(country)) {
      return { matches: false, action: condition.action as ConditionAction }
    }
    
    // Check inclusions
    let matches = false
    
    if (rules.countries && country) {
      matches = rules.countries.includes(country)
    }
    
    if (!matches && rules.regions && region) {
      matches = rules.regions.includes(region)
    }
    
    if (!matches && rules.cities && city) {
      matches = rules.cities.includes(city)
    }
    
    return {
      matches,
      action: condition.action as ConditionAction
    }
  }

  private evaluateDeviceCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as DeviceRule
    const device = this.visitorContext.device
    
    let matches = false
    
    // Check device type
    if (rules.types && rules.types.includes(device.type)) {
      matches = true
    }
    
    // Check platform
    if (!matches && rules.platforms && rules.platforms.includes(device.platform as any)) {
      matches = true
    }
    
    // Check browser
    if (!matches && rules.browsers && rules.browsers.includes(device.browser as any)) {
      matches = true
    }
    
    return {
      matches,
      action: condition.action as ConditionAction
    }
  }

  private evaluateTimeCondition(condition: LinkCondition): {
    matches: boolean
    action: ConditionAction
  } {
    const rules = condition.rules as TimeRule
    const now = convertToTimezone(this.currentTime, rules.timezone)
    
    const dayOfWeek = now.getDay()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    // Check day of week
    if (!rules.daysOfWeek.includes(dayOfWeek)) {
      return { matches: false, action: condition.action as ConditionAction }
    }
    
    // Check time ranges
    const timeMatches = rules.timeRanges.some(range => {
      return currentTime >= range.start && currentTime <= range.end
    })
    
    return {
      matches: timeMatches,
      action: condition.action as ConditionAction
    }
  }
}
```

### Visitor Context Detection

```typescript
// lib/visitor-detection/context.ts
export async function detectVisitorContext(
  request: Request,
  headers: Headers
): Promise<VisitorContext> {
  const userAgent = headers.get('user-agent') || ''
  const referrer = headers.get('referer') || undefined
  const ipAddress = getClientIP(request)
  
  // Device detection
  const device = parseUserAgent(userAgent)
  
  // Location detection (using Vercel's geo headers or IP lookup)
  const location = await detectLocation(ipAddress, headers)
  
  return {
    referrer,
    userAgent,
    ipAddress: hashIP(ipAddress), // Hash for privacy
    country: location.country,
    region: location.region,
    city: location.city,
    device,
    timestamp: new Date(),
    timezone: location.timezone
  }
}

function parseUserAgent(userAgent: string) {
  const parser = new UAParser(userAgent)
  const result = parser.getResult()
  
  return {
    type: result.device.type || 'desktop',
    platform: result.os.name?.toLowerCase() || 'unknown',
    browser: result.browser.name?.toLowerCase() || 'unknown'
  }
}

async function detectLocation(ipAddress: string, headers: Headers) {
  // Try Vercel's geo headers first (available on Edge Runtime)
  const country = headers.get('x-vercel-ip-country')
  const region = headers.get('x-vercel-ip-country-region')
  const city = headers.get('x-vercel-ip-city')
  const timezone = headers.get('x-vercel-ip-timezone')
  
  if (country) {
    return { country, region, city, timezone }
  }
  
  // Fallback to IP geolocation service
  try {
    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`)
    const data = await response.json()
    return {
      country: data.country_code,
      region: data.region,
      city: data.city,
      timezone: data.timezone
    }
  } catch (error) {
    console.error('Location detection failed:', error)
    return {}
  }
}
```

## User Interface Components

### Conditional Link Form

```typescript
// components/dashboard/conditional-link-form.tsx
interface ConditionalLinkFormProps {
  link?: Link
  onSave: (linkData: Partial<Link>) => void
  onCancel: () => void
}

export function ConditionalLinkForm({ link, onSave, onCancel }: ConditionalLinkFormProps) {
  const [showScheduling, setShowScheduling] = useState(link?.isScheduled || false)
  const [showConditions, setShowConditions] = useState(link?.hasConditions || false)
  const [conditions, setConditions] = useState<LinkCondition[]>(link?.conditions || [])
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic link fields */}
      <div className="space-y-4">
        <Input
          label="Link Title"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          required
        />
        <Input
          label="URL"
          type="url"
          value={formData.url}
          onChange={(e) => setFormData({ ...formData, url: e.target.value })}
          required
        />
      </div>
      
      {/* Scheduling section */}
      <div className="border rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Schedule Link</h3>
          <Switch
            checked={showScheduling}
            onCheckedChange={setShowScheduling}
          />
        </div>
        
        {showScheduling && (
          <SchedulePicker
            startDate={formData.scheduleStart}
            endDate={formData.scheduleEnd}
            timezone={formData.timezone}
            onChange={(schedule) => setFormData({ ...formData, ...schedule })}
          />
        )}
      </div>
      
      {/* Conditional rules section */}
      <div className="border rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Conditional Rules</h3>
          <Switch
            checked={showConditions}
            onCheckedChange={setShowConditions}
          />
        </div>
        
        {showConditions && (
          <div className="space-y-4">
            <RuleBuilder
              conditions={conditions}
              onChange={setConditions}
            />
            
            <div className="flex items-center space-x-2">
              <Label>Default behavior when no rules match:</Label>
              <Select
                value={formData.defaultBehavior}
                onValueChange={(value) => setFormData({ ...formData, defaultBehavior: value })}
              >
                <SelectItem value="show">Show Link</SelectItem>
                <SelectItem value="hide">Hide Link</SelectItem>
              </Select>
            </div>
            
            <RuleTester
              conditions={conditions}
              onTest={(context) => testRules(conditions, context)}
            />
          </div>
        )}
      </div>
      
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Link
        </Button>
      </div>
    </form>
  )
}
```

### Rule Builder Component

```typescript
// components/dashboard/rule-builder.tsx
interface RuleBuilderProps {
  conditions: LinkCondition[]
  onChange: (conditions: LinkCondition[]) => void
}

export function RuleBuilder({ conditions, onChange }: RuleBuilderProps) {
  const addCondition = (type: ConditionType) => {
    const newCondition: LinkCondition = {
      id: generateId(),
      linkId: '', // Will be set when saving
      type,
      priority: conditions.length,
      isActive: true,
      rules: getDefaultRulesForType(type),
      action: { type: 'show' },
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    onChange([...conditions, newCondition])
  }
  
  const updateCondition = (id: string, updates: Partial<LinkCondition>) => {
    onChange(conditions.map(c => c.id === id ? { ...c, ...updates } : c))
  }
  
  const removeCondition = (id: string) => {
    onChange(conditions.filter(c => c.id !== id))
  }
  
  return (
    <div className="space-y-4">
      {conditions.map((condition, index) => (
        <ConditionEditor
          key={condition.id}
          condition={condition}
          priority={index + 1}
          onChange={(updates) => updateCondition(condition.id, updates)}
          onRemove={() => removeCondition(condition.id)}
        />
      ))}
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full">
            <Plus className="w-4 h-4 mr-2" />
            Add Condition
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => addCondition('referrer')}>
            <Globe className="w-4 h-4 mr-2" />
            Referrer-based
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => addCondition('location')}>
            <MapPin className="w-4 h-4 mr-2" />
            Location-based
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => addCondition('device')}>
            <Smartphone className="w-4 h-4 mr-2" />
            Device-based
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => addCondition('time')}>
            <Clock className="w-4 h-4 mr-2" />
            Time-based
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
```

## Performance Optimizations

### Caching Strategy

```typescript
// lib/cache/rule-cache.ts
export class RuleCache {
  private static instance: RuleCache
  private cache = new Map<string, RuleEvaluationResult>()
  private ttl = 5 * 60 * 1000 // 5 minutes
  
  static getInstance(): RuleCache {
    if (!RuleCache.instance) {
      RuleCache.instance = new RuleCache()
    }
    return RuleCache.instance
  }
  
  getCacheKey(linkId: string, context: VisitorContext): string {
    // Create a cache key based on relevant context properties
    const contextHash = hashObject({
      referrer: context.referrer,
      country: context.country,
      deviceType: context.device.type,
      hour: new Date(context.timestamp).getHours()
    })
    
    return `${linkId}:${contextHash}`
  }
  
  get(key: string): RuleEvaluationResult | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    // Check if cache entry is still valid
    if (Date.now() - cached.evaluationTime > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached
  }
  
  set(key: string, result: RuleEvaluationResult): void {
    this.cache.set(key, {
      ...result,
      evaluationTime: Date.now()
    })
  }
}
```

### Database Optimizations

```sql
-- Additional indexes for conditional link queries
CREATE INDEX idx_link_conditions_active ON LinkCondition(linkId, isActive, priority);
CREATE INDEX idx_link_schedule ON Link(isScheduled, scheduleStart, scheduleEnd);
CREATE INDEX idx_link_conditions_type ON LinkCondition(type, isActive);

-- Composite index for analytics queries
CREATE INDEX idx_linkclick_conditional ON LinkClick(conditionId, conditionType, timestamp);
```

## Error Handling

### Rule Evaluation Error Handling

```typescript
// lib/rule-engine/error-handler.ts
export class RuleEvaluationError extends Error {
  constructor(
    message: string,
    public linkId: string,
    public conditionId?: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'RuleEvaluationError'
  }
}

export function handleRuleEvaluationError(
  error: RuleEvaluationError,
  fallbackBehavior: 'show' | 'hide' = 'show'
): RuleEvaluationResult {
  // Log error for monitoring
  console.error('Rule evaluation failed:', {
    linkId: error.linkId,
    conditionId: error.conditionId,
    message: error.message,
    originalError: error.originalError
  })
  
  // Return safe fallback result
  return {
    linkId: error.linkId,
    shouldShow: fallbackBehavior === 'show',
    matchedConditions: [],
    appliedAction: { type: fallbackBehavior },
    evaluationTime: 0
  }
}
```

## Testing Strategy

### Rule Engine Testing

```typescript
// __tests__/rule-engine/evaluator.test.ts
describe('RuleEvaluator', () => {
  describe('schedule evaluation', () => {
    it('should hide link before start time', () => {
      const link = createMockLink({
        isScheduled: true,
        scheduleStart: new Date('2025-01-01T10:00:00Z'),
        scheduleEnd: new Date('2025-01-01T18:00:00Z')
      })
      
      const evaluator = new RuleEvaluator(
        createMockVisitorContext(),
        new Date('2025-01-01T09:00:00Z')
      )
      
      const result = evaluator.evaluateLink(link)
      expect(result.shouldShow).toBe(false)
      expect(result.matchedConditions).toContain('schedule')
    })
  })
  
  describe('referrer conditions', () => {
    it('should match exact referrer domain', () => {
      const condition = createMockCondition({
        type: 'referrer',
        rules: {
          domains: ['instagram.com'],
          matchType: 'exact',
          caseSensitive: false
        }
      })
      
      const context = createMockVisitorContext({
        referrer: 'https://instagram.com/user'
      })
      
      const evaluator = new RuleEvaluator(context)
      const result = evaluator.evaluateCondition(condition)
      
      expect(result.matches).toBe(true)
    })
  })
})
```

## Security Considerations

### Privacy Protection

- **IP Address Hashing**: All IP addresses are hashed before storage
- **Location Data**: Only country/region level data is stored, no precise coordinates
- **User Agent Sanitization**: Remove potentially identifying information from user agents
- **Data Retention**: Implement automatic cleanup of old visitor context data

### Input Validation

```typescript
// lib/validations/conditional-links.ts
export const linkConditionSchema = z.object({
  type: z.enum(['referrer', 'location', 'device', 'time']),
  priority: z.number().min(0).max(100),
  isActive: z.boolean(),
  rules: z.object({}).passthrough(), // Validated based on type
  action: z.object({
    type: z.enum(['show', 'hide', 'redirect']),
    value: z.string().optional(),
    alternateTitle: z.string().optional(),
    alternateIcon: z.string().optional()
  })
})

export const scheduleSchema = z.object({
  isScheduled: z.boolean(),
  scheduleStart: z.date().optional(),
  scheduleEnd: z.date().optional(),
  timezone: z.string().optional()
}).refine(data => {
  if (data.isScheduled) {
    return data.scheduleStart && data.scheduleEnd && data.scheduleStart < data.scheduleEnd
  }
  return true
}, {
  message: "Schedule start must be before end time when scheduling is enabled"
})
```

This design provides a comprehensive foundation for implementing advanced link scheduling and conditional functionality while maintaining the performance and user experience standards of the existing LinksInBio application.