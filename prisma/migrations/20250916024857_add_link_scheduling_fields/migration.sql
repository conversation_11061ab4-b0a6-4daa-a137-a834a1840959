-- AlterTable
ALTER TABLE "LinkClick" ADD COLUMN "conditionId" TEXT;
ALTER TABLE "LinkClick" ADD COLUMN "conditionType" TEXT;
ALTER TABLE "LinkClick" ADD COLUMN "visitorContext" JSONB;

-- CreateTable
CREATE TABLE "LinkCondition" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "linkId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "rules" JSONB NOT NULL,
    "action" JSONB NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "LinkCondition_linkId_fkey" FOREIGN KEY ("linkId") REFERENCES "Link" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Link" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "profileId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "icon" TEXT,
    "isVisible" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "isScheduled" BOOLEAN NOT NULL DEFAULT false,
    "scheduleStart" DATETIME,
    "scheduleEnd" DATETIME,
    "timezone" TEXT DEFAULT 'UTC',
    "hasConditions" BOOLEAN NOT NULL DEFAULT false,
    "defaultBehavior" TEXT NOT NULL DEFAULT 'show',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Link_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_Link" ("clickCount", "createdAt", "icon", "id", "isVisible", "order", "profileId", "title", "updatedAt", "url") SELECT "clickCount", "createdAt", "icon", "id", "isVisible", "order", "profileId", "title", "updatedAt", "url" FROM "Link";
DROP TABLE "Link";
ALTER TABLE "new_Link" RENAME TO "Link";
CREATE INDEX "Link_profileId_order_idx" ON "Link"("profileId", "order");
CREATE INDEX "Link_isScheduled_scheduleStart_scheduleEnd_idx" ON "Link"("isScheduled", "scheduleStart", "scheduleEnd");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE INDEX "LinkCondition_linkId_priority_idx" ON "LinkCondition"("linkId", "priority");

-- CreateIndex
CREATE INDEX "LinkCondition_type_isActive_idx" ON "LinkCondition"("type", "isActive");

-- CreateIndex
CREATE INDEX "LinkClick_conditionId_idx" ON "LinkClick"("conditionId");
