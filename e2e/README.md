# End-to-End Tests for Username Validation

This directory contains comprehensive end-to-end tests for the username validation functionality, covering all aspects from basic user flows to performance, accessibility, and cross-browser compatibility.

## Test Suites

### 1. Signup Flow Tests (`signup-flow.spec.ts`)
Tests the complete user signup process with real-time username validation:
- Form rendering and basic functionality
- Username requirements display
- Real-time format validation
- Username availability checking with debouncing
- Error handling (unavailable usernames, network errors, timeouts)
- Form submission prevention/allowance based on validation state
- Complete successful signup flow
- API error handling
- Mobile device compatibility

### 2. Profile Update Flow Tests (`profile-update-flow.spec.ts`)
Tests username validation in the profile settings context:
- Profile form rendering with current data
- Skipping availability check for unchanged usernames
- Availability checking for new usernames
- Form submission prevention for unavailable usernames
- Network error handling with warnings
- Successful profile updates
- API error handling
- Concurrent username changes
- Case-insensitive username handling

### 3. Error Scenarios and Recovery (`error-scenarios.spec.ts`)
Tests comprehensive error handling and recovery mechanisms:
- Intermittent network failures with retry functionality
- Server errors with appropriate messaging
- Rate limiting handling
- Malformed API responses
- Offline/online state transitions
- Exponential backoff for retries
- Error state clearing on input changes
- Concurrent error and success scenarios
- Form state preservation during errors

### 4. Performance Tests (`performance-tests.spec.ts`)
Tests performance characteristics under various conditions:
- Debouncing effectiveness with rapid input changes
- Concurrent request handling
- Performance under simulated load
- Cache behavior and effectiveness
- Memory usage during extended use
- Timeout scenario handling
- UI responsiveness during heavy load

### 5. Accessibility Tests (`accessibility-tests.spec.ts`)
Tests compliance with accessibility standards:
- WCAG 2.1 compliance
- Proper ARIA attributes and relationships
- Keyboard navigation support
- Screen reader announcements
- High contrast mode support
- Focus management
- Reduced motion preferences
- Voice control compatibility
- Dynamic content accessibility

### 6. Cross-Browser Tests (`cross-browser-tests.spec.ts`)
Tests functionality across different browsers and devices:
- Debouncing consistency across browsers
- Input event handling variations
- Focus event behavior
- Validation timing consistency
- API timeout handling
- Rapid input change handling
- Network condition variations
- Form submission behavior
- Viewport responsiveness
- JavaScript timing variations

## Running the Tests

### Prerequisites

1. Install dependencies:
```bash
bun install
```

2. Install Playwright browsers:
```bash
bunx playwright install
```

### Running Individual Test Suites

```bash
# Run signup flow tests
bunx playwright test e2e/signup-flow.spec.ts

# Run profile update tests
bunx playwright test e2e/profile-update-flow.spec.ts

# Run error scenario tests
bunx playwright test e2e/error-scenarios.spec.ts

# Run performance tests
bunx playwright test e2e/performance-tests.spec.ts

# Run accessibility tests
bunx playwright test e2e/accessibility-tests.spec.ts

# Run cross-browser tests
bunx playwright test e2e/cross-browser-tests.spec.ts
```

### Running All Tests

```bash
# Run all e2e tests
bun run test:e2e

# Run with UI mode for debugging
bun run test:e2e:ui

# Run in headed mode (visible browser)
bun run test:e2e:headed

# Run in debug mode
bun run test:e2e:debug
```

### Comprehensive Test Runner

For a complete test run with detailed reporting:

```bash
# Run comprehensive test suite
bun run e2e/run-comprehensive-tests.ts
```

This will:
- Check prerequisites
- Run all test suites in order
- Generate detailed performance metrics
- Create JSON and HTML reports
- Provide pass/fail summary

## Test Configuration

The tests are configured via `playwright.config.ts` in the project root:

- **Browsers**: Tests run on Chromium, Firefox, and WebKit
- **Mobile**: Tests include mobile Chrome and Safari
- **Timeouts**: Configured per test type
- **Retries**: 2 retries on CI, 0 locally
- **Reports**: HTML reports with screenshots and videos on failure

## Test Utilities

The `utils/test-helpers.ts` file provides utilities for:
- Mocking API responses (success, error, timeout, rate limiting)
- Filling forms with test data
- Waiting for validation states
- Asserting UI states
- Performance measurement
- Accessibility testing
- Mobile simulation

## Environment Setup

Tests expect the development server to be running on `http://localhost:3000`. The Playwright config includes a web server setup that automatically starts the dev server.

### Environment Variables

You can customize test behavior with environment variables:

```bash
# Run tests in CI mode (more retries, parallel execution)
CI=true bun run test:e2e

# Run tests with custom base URL
PLAYWRIGHT_BASE_URL=http://localhost:3001 bun run test:e2e
```

## Debugging Tests

### Visual Debugging
```bash
# Open Playwright UI for interactive debugging
bun run test:e2e:ui

# Run specific test in headed mode
bunx playwright test e2e/signup-flow.spec.ts --headed --debug
```

### Screenshots and Videos
Failed tests automatically capture:
- Screenshots at the point of failure
- Video recordings of the entire test
- Network request logs
- Console output

### Trace Viewer
```bash
# Generate and view traces
bunx playwright test --trace on
bunx playwright show-trace trace.zip
```

## Performance Benchmarks

The performance tests establish benchmarks for:
- **Debouncing**: < 3 API calls for 7+ rapid input changes
- **Response Time**: < 500ms average API response
- **Memory Usage**: Stable during 50+ username changes
- **UI Responsiveness**: < 100ms input lag during API calls

## Accessibility Standards

Tests verify compliance with:
- **WCAG 2.1 AA**: Color contrast, keyboard navigation, screen reader support
- **ARIA**: Proper labels, roles, and state management
- **Focus Management**: Logical tab order and focus indicators
- **Announcements**: Status changes announced to assistive technology

## Browser Support

Tests verify functionality on:
- **Desktop**: Chrome, Firefox, Safari
- **Mobile**: Chrome on Android, Safari on iOS
- **Viewport**: Responsive behavior from 320px to 1920px
- **Features**: Modern JavaScript, CSS Grid, Flexbox

## Continuous Integration

For CI environments, tests are configured to:
- Run in parallel across multiple workers
- Retry failed tests automatically
- Generate artifacts (screenshots, videos, reports)
- Fail fast on critical errors
- Provide detailed exit codes

## Troubleshooting

### Common Issues

1. **Tests timing out**: Increase timeout in test or config
2. **Flaky tests**: Add proper wait conditions and stabilize timing
3. **Browser not found**: Run `bunx playwright install`
4. **Port conflicts**: Change base URL in config
5. **Network issues**: Check mock configurations

### Debug Commands

```bash
# Check Playwright installation
bunx playwright --version

# List available browsers
bunx playwright install --dry-run

# Test specific browser
bunx playwright test --project=chromium

# Generate test report
bunx playwright show-report
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use the test helpers for common operations
3. Include accessibility checks where appropriate
4. Add performance assertions for timing-critical features
5. Test across multiple browsers when relevant
6. Document any new test utilities or patterns

## Coverage

The e2e tests cover:
- ✅ Complete user signup flow with username validation
- ✅ Profile update flow with username changes  
- ✅ Error scenarios and recovery mechanisms
- ✅ Performance under concurrent load
- ✅ Accessibility compliance (WCAG 2.1 AA)
- ✅ Cross-browser compatibility
- ✅ Mobile device support
- ✅ Network condition variations
- ✅ API timeout and error handling
- ✅ Form state management
- ✅ Real-time validation feedback
- ✅ Debouncing behavior
- ✅ Cache effectiveness
- ✅ Memory usage patterns