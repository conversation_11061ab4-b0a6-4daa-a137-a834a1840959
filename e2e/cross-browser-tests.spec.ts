import { test, expect } from '@playwright/test'

test.describe('Cross-browser Username Validation', () => {

  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/signup')
  })

  test('should debounce username checks consistently', async ({ page, browserName }) => {
      let requestCount = 0
      const requestTimes: number[] = []
      
      // Mock API to track debouncing behavior
      await page.route('/api/account/username/availability*', async route => {
        requestCount++
        requestTimes.push(Date.now())
        
        const url = new URL(route.request().url())
        const username = url.searchParams.get('username')
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      })
      
      const usernameInput = page.getByLabel('Username')
      
      // Rapid typing simulation
      const startTime = Date.now()
      await usernameInput.fill('t')
      await page.waitForTimeout(50)
      await usernameInput.fill('te')
      await page.waitForTimeout(50)
      await usernameInput.fill('tes')
      await page.waitForTimeout(50)
      await usernameInput.fill('test')
      await page.waitForTimeout(50)
      await usernameInput.fill('testu')
      await page.waitForTimeout(50)
      await usernameInput.fill('testus')
      await page.waitForTimeout(50)
      await usernameInput.fill('testuse')
      await page.waitForTimeout(50)
      await usernameInput.fill('testuser')
      
      // Wait for debouncing to complete
      await page.waitForTimeout(500)
      
      const endTime = Date.now()
      const totalTime = endTime - startTime
      
      // Should have debounced effectively across all browsers
      expect(requestCount).toBeLessThan(8) // Less than number of changes
      expect(requestCount).toBeGreaterThan(0) // But at least one request
      
      // Should show final result
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
      
      console.log(`${browserName}: ${requestCount} requests in ${totalTime}ms`)
    })

    test('should handle input events consistently', async ({ page, browserName, isMobile }) => {
      let eventCount = 0
      
      // Track input events
      await page.evaluate(() => {
        const input = document.querySelector('input[aria-label="Username"]') as HTMLInputElement
        if (input) {
          input.addEventListener('input', () => {
            (window as any).inputEventCount = ((window as any).inputEventCount || 0) + 1
          })
        }
      })
      
      const usernameInput = page.getByLabel('Username')
      
      if (isMobile) {
        // Mobile-specific input behavior
        await usernameInput.tap()
        await page.keyboard.type('mobiletest')
      } else {
        // Desktop input behavior
        await usernameInput.click()
        await page.keyboard.type('desktoptest')
      }
      
      // Get event count
      eventCount = await page.evaluate(() => (window as any).inputEventCount || 0)
      
      // Should have received input events
      expect(eventCount).toBeGreaterThan(0)
      
      // Mock API response
      await page.route('/api/account/username/availability*', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username: isMobile ? 'mobiletest' : 'desktoptest' })
        })
      })
      
      await page.waitForTimeout(500)
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
      
      console.log(`${browserName} (${isMobile ? 'mobile' : 'desktop'}): ${eventCount} input events`)
    })

    test('should handle focus events consistently', async ({ page, isMobile }) => {
      const usernameInput = page.getByLabel('Username')
      
      if (isMobile) {
        // Mobile focus behavior
        await usernameInput.tap()
      } else {
        // Desktop focus behavior
        await usernameInput.focus()
      }
      
      // Requirements should appear on focus across all browsers
      await expect(page.getByText('Username requirements:')).toBeVisible()
      
      // Focus away
      if (isMobile) {
        await page.getByLabel('Email').tap()
      } else {
        await page.getByLabel('Email').focus()
      }
      
      // Requirements should disappear
      await expect(page.getByText('Username requirements:')).not.toBeVisible()
    })

    test('should handle validation timing consistently', async ({ page }) => {
      const usernameInput = page.getByLabel('Username')
      
      // Test validation error timing
      const startTime = Date.now()
      await usernameInput.fill('ab') // Invalid
      
      // Should show validation error quickly
      await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
      
      const validationTime = Date.now() - startTime
      
      // Validation should be fast across all browsers
      expect(validationTime).toBeLessThan(1000)
      
      // Test clearing validation
      const clearStartTime = Date.now()
      await usernameInput.clear()
      
      // Error should clear quickly
      await expect(page.getByText('Username must be at least 3 characters')).not.toBeVisible()
      
      const clearTime = Date.now() - clearStartTime
      expect(clearTime).toBeLessThan(500)
      
      console.log(`${browserName}: Validation ${validationTime}ms, Clear ${clearTime}ms`)
    })

    test('should handle API timeouts consistently', async ({ page, browserName }) => {
      const usernameInput = page.getByLabel('Username')
      
      // Mock timeout scenario
      await page.route('/api/account/username/availability*', async route => {
        // Don't respond to simulate timeout
        return
      })
      
      const startTime = Date.now()
      await usernameInput.fill('timeouttest')
      
      // Should show timeout error within reasonable time across all browsers
      await expect(page.getByText('Request timed out. Please try again.')).toBeVisible({ timeout: 10000 })
      
      const timeoutTime = Date.now() - startTime
      
      // Timeout handling should be consistent
      expect(timeoutTime).toBeGreaterThan(4000) // Should wait reasonable time
      expect(timeoutTime).toBeLessThan(8000) // But not too long
      
      console.log(`${browserName}: Timeout detected in ${timeoutTime}ms`)
    })

    test('should handle rapid input changes consistently', async ({ page, browserName }) => {
      let requestCount = 0
      
      // Mock API
      await page.route('/api/account/username/availability*', async route => {
        requestCount++
        const url = new URL(route.request().url())
        const username = url.searchParams.get('username')
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      })
      
      const usernameInput = page.getByLabel('Username')
      
      // Simulate very rapid changes
      const changes = ['a', 'ab', 'abc', 'abcd', 'abcde', 'abcdef', 'abcdefg']
      
      for (const change of changes) {
        await usernameInput.fill(change)
        await page.waitForTimeout(10) // Very fast changes
      }
      
      // Wait for debouncing
      await page.waitForTimeout(1000)
      
      // Should have made minimal requests due to debouncing
      expect(requestCount).toBeLessThan(changes.length)
      
      // Should show final result
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
      
      console.log(`${browserName}: ${requestCount} requests for ${changes.length} rapid changes`)
    })

    test('should handle network conditions consistently', async ({ page }) => {
      const usernameInput = page.getByLabel('Username')
      
      // Test slow network
      await page.route('/api/account/username/availability*', async route => {
        await page.waitForTimeout(1000) // Simulate slow network
        
        const url = new URL(route.request().url())
        const username = url.searchParams.get('username')
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      })
      
      await usernameInput.fill('slownetwork')
      
      // Should show loading state
      await expect(page.getByText('Checking availability...')).toBeVisible()
      
      // Should eventually show result
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 15000 })
      
      // Test network error recovery
      await page.route('/api/account/username/availability*', async route => {
        await route.abort('failed')
      })
      
      await usernameInput.clear()
      await usernameInput.fill('networkerror')
      
      await page.waitForTimeout(500)
      await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
      
      // Test recovery
      await page.route('/api/account/username/availability*', async route => {
        const url = new URL(route.request().url())
        const username = url.searchParams.get('username')
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      })
      
      await page.getByRole('button', { name: 'Retry' }).click()
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    })

    test('should handle form submission consistently', async ({ page }) => {
      // Mock available username
      await page.route('/api/account/username/availability*', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username: 'submittest' })
        })
      })
      
      // Fill form
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Username').fill('submittest')
      await page.getByLabel('Display Name').fill('Test User')
      await page.getByLabel('Password', { exact: true }).fill('password123')
      await page.getByLabel('Confirm Password').fill('password123')
      
      // Wait for username validation
      await page.waitForTimeout(500)
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
      
      // Submit button should be enabled
      const submitButton = page.getByRole('button', { name: 'Create Account' })
      await expect(submitButton).toBeEnabled()
      
      // Should be able to submit (we'll mock the API to prevent actual submission)
      await page.route('/api/auth/signup', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        })
      })
      
      await submitButton.click()
      
      // Should show loading state consistently across browsers
      await expect(page.getByRole('button', { name: /creating account|signing up/i })).toBeVisible()
    })

    test('should handle viewport changes consistently', async ({ page, isMobile }) => {
      const usernameInput = page.getByLabel('Username')
      
      // Test initial viewport
      await usernameInput.focus()
      await expect(page.getByText('Username requirements:')).toBeVisible()
      
      if (!isMobile) {
        // Test viewport resize on desktop
        await page.setViewportSize({ width: 400, height: 600 }) // Mobile-like size
        
        // Should still work in smaller viewport
        await usernameInput.fill('viewporttest')
        
        // Mock API
        await page.route('/api/account/username/availability*', async route => {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ available: true, username: 'viewporttest' })
          })
        })
        
        await page.waitForTimeout(500)
        await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
        
        // Resize back
        await page.setViewportSize({ width: 1280, height: 720 })
        
        // Should still show result
        await expect(page.getByText('Username available')).toBeVisible()
      }
    })

    test('should handle JavaScript timing consistently', async ({ page }) => {
      // Test that debouncing works with different JavaScript execution timing
      let requestCount = 0
      
      await page.route('/api/account/username/availability*', async route => {
        requestCount++
        const url = new URL(route.request().url())
        const username = url.searchParams.get('username')
        
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      })
      
      const usernameInput = page.getByLabel('Username')
      
      // Use different timing patterns
      await usernameInput.fill('timing1')
      await page.waitForTimeout(100)
      await usernameInput.fill('timing12')
      await page.waitForTimeout(200)
      await usernameInput.fill('timing123')
      await page.waitForTimeout(50)
      await usernameInput.fill('timing1234')
      
      // Wait for final debounce
      await page.waitForTimeout(500)
      
      // Should handle timing variations consistently
      expect(requestCount).toBeLessThan(4)
      await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
      
      console.log(`${browserName}: ${requestCount} requests with varied timing`)
    })
})

// Additional cross-browser integration test
test.describe('Cross-browser Integration', () => {
  test('should work consistently across all supported browsers', async ({ page, browserName }) => {
    await page.goto('/auth/signup')
    
    // Test basic functionality that should work everywhere
    const usernameInput = page.getByLabel('Username')
    
    // Mock API
    await page.route('/api/account/username/availability*', async route => {
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    // Test core functionality
    await usernameInput.fill('crossbrowser')
    await page.waitForTimeout(500)
    
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Test form integration
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Display Name').fill('Cross Browser')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Submit button should be enabled
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeEnabled()
    
    console.log(`✓ Cross-browser test passed on ${browserName}`)
  })
})