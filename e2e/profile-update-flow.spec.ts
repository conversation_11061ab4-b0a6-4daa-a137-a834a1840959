import { test, expect } from '@playwright/test'
import { injectAxe, checkA11y } from 'axe-playwright'

test.describe('Profile Update Flow with Username Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.addInitScript(() => {
      window.localStorage.setItem('auth-token', 'mock-token')
    })
    
    // Mock session data
    await page.route('/api/auth/session', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: '1',
            email: '<EMAIL>',
            username: 'currentuser',
            displayName: 'Current User'
          }
        })
      })
    })
    
    // Mock current user profile data
    await page.route('/api/profile', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          username: 'currentuser',
          displayName: 'Current User',
          bio: 'Current bio'
        })
      })
    })
    
    await page.goto('/dashboard/settings')
    await injectAxe(page)
  })

  test('should display profile settings form with current username', async ({ page }) => {
    // Wait for form to load
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Username')).toBeVisible()
    await expect(page.getByLabel('Display Name')).toBeVisible()
    
    // Should show current values
    await expect(page.getByLabel('Email')).toHaveValue('<EMAIL>')
    await expect(page.getByLabel('Username')).toHaveValue('currentuser')
    await expect(page.getByLabel('Display Name')).toHaveValue('Current User')
  })

  test('should skip availability check when username matches current username', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Clear and retype the same username
    await usernameInput.clear()
    await usernameInput.fill('currentuser')
    
    // Should not show checking state
    await page.waitForTimeout(500)
    await expect(page.getByText('Checking availability...')).not.toBeVisible()
    
    // Should show as valid (no error state)
    await expect(page.getByText('Username available')).toBeVisible()
  })

  test('should check availability when username is changed', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'newusername' })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Change to a new username
    await usernameInput.clear()
    await usernameInput.fill('newusername')
    
    // Should show checking state
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    // Wait for availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should prevent form submission when new username is unavailable', async ({ page }) => {
    // Mock unavailable username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: false, username: 'takenusername' })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Change to unavailable username
    await usernameInput.clear()
    await usernameInput.fill('takenusername')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username is already taken')).toBeVisible({ timeout: 10000 })
    
    // Submit button should be disabled
    const saveButton = page.getByRole('button', { name: 'Save Changes' })
    await expect(saveButton).toBeDisabled()
    
    // Try to submit - should not work
    await saveButton.click()
    
    // Should still be on settings page
    await expect(page).toHaveURL('/dashboard/settings')
  })

  test('should allow form submission with network error warning', async ({ page }) => {
    // Mock network error for username check
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    // Mock successful profile update
    await page.route('/api/profile', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        })
      } else {
        await route.continue()
      }
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Change username
    await usernameInput.clear()
    await usernameInput.fill('networkerror')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Submit button should be enabled with warning
    const saveButton = page.getByRole('button', { name: 'Save Changes' })
    await expect(saveButton).toBeEnabled()
    
    // Should show warning message
    await expect(page.getByText('Unable to verify username availability. Proceeding may result in an error.')).toBeVisible()
    
    // Should allow submission
    await saveButton.click()
    
    // Should show success message
    await expect(page.getByText('Profile updated successfully')).toBeVisible({ timeout: 10000 })
  })

  test('should handle successful profile update with new username', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'updateduser' })
      })
    })
    
    // Mock successful profile update
    await page.route('/api/profile', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        })
      } else {
        await route.continue()
      }
    })
    
    // Update profile information
    await page.getByLabel('Username').clear()
    await page.getByLabel('Username').fill('updateduser')
    await page.getByLabel('Display Name').clear()
    await page.getByLabel('Display Name').fill('Updated User')
    
    // Wait for username availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Submit form
    await page.getByRole('button', { name: 'Save Changes' }).click()
    
    // Should show success message
    await expect(page.getByText('Profile updated successfully')).toBeVisible({ timeout: 10000 })
  })

  test('should handle profile update API errors', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'errorupdate' })
      })
    })
    
    // Mock profile update error
    await page.route('/api/profile', async route => {
      if (route.request().method() === 'PUT') {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Username already exists' })
        })
      } else {
        await route.continue()
      }
    })
    
    // Update username
    await page.getByLabel('Username').clear()
    await page.getByLabel('Username').fill('errorupdate')
    
    // Wait for availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Submit form
    await page.getByRole('button', { name: 'Save Changes' }).click()
    
    // Should show error message
    await expect(page.getByText('Username already exists')).toBeVisible({ timeout: 10000 })
  })

  test('should validate username format in profile settings', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Test invalid format
    await usernameInput.clear()
    await usernameInput.fill('ab')
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
    
    // Test invalid characters
    await usernameInput.clear()
    await usernameInput.fill('user@name')
    await expect(page.getByText('Username can only contain letters, numbers, hyphens, and underscores')).toBeVisible()
    
    // Submit button should be disabled for invalid format
    await expect(page.getByRole('button', { name: 'Save Changes' })).toBeDisabled()
  })

  test('should handle case-insensitive username changes', async ({ page }) => {
    // Mock available username (case variation)
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'CurrentUser' })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Change case of current username
    await usernameInput.clear()
    await usernameInput.fill('CurrentUser')
    
    // Should check availability (different case)
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should show loading states during form submission', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'loadingtest' })
      })
    })
    
    // Mock slow profile update
    await page.route('/api/profile', async route => {
      if (route.request().method() === 'PUT') {
        await page.waitForTimeout(2000)
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ success: true })
        })
      } else {
        await route.continue()
      }
    })
    
    // Update username
    await page.getByLabel('Username').clear()
    await page.getByLabel('Username').fill('loadingtest')
    
    // Wait for availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Submit form
    await page.getByRole('button', { name: 'Save Changes' }).click()
    
    // Should show loading state
    await expect(page.getByRole('button', { name: 'Saving...' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'Saving...' })).toBeDisabled()
    
    // Should complete successfully
    await expect(page.getByText('Profile updated successfully')).toBeVisible({ timeout: 10000 })
  })

  test('should preserve other form fields when username validation fails', async ({ page }) => {
    // Mock unavailable username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: false, username: 'unavailable' })
      })
    })
    
    // Fill out form
    await page.getByLabel('Username').clear()
    await page.getByLabel('Username').fill('unavailable')
    await page.getByLabel('Display Name').clear()
    await page.getByLabel('Display Name').fill('New Display Name')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username is already taken')).toBeVisible({ timeout: 10000 })
    
    // Other fields should retain their values
    await expect(page.getByLabel('Display Name')).toHaveValue('New Display Name')
    await expect(page.getByLabel('Email')).toHaveValue('<EMAIL>')
  })

  test('should be accessible', async ({ page }) => {
    // Check accessibility of the profile settings form
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    })
    
    // Test keyboard navigation
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Email')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Username')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Display Name')).toBeFocused()
  })

  test('should handle concurrent username changes', async ({ page }) => {
    let requestCount = 0
    
    // Mock API to track concurrent requests
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await page.waitForTimeout(100) // Small delay to simulate network
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ 
          available: true, 
          username,
          requestId: requestCount 
        })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Rapidly change username multiple times
    await usernameInput.clear()
    await usernameInput.fill('test1')
    await usernameInput.fill('test12')
    await usernameInput.fill('test123')
    
    // Wait for debouncing and final result
    await page.waitForTimeout(1000)
    
    // Should show result for the final username
    await expect(page.getByText('Username available')).toBeVisible()
    
    // Should have made fewer requests than changes due to debouncing
    expect(requestCount).toBeLessThan(3)
  })
})