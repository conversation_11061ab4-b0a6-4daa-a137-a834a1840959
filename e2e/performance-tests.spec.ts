import { test, expect } from '@playwright/test'

test.describe('Username Validation Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/signup')
  })

  test('should handle rapid username changes with debouncing', async ({ page }) => {
    let requestCount = 0
    const requestTimes: number[] = []
    
    // Mock API to track request frequency
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      requestTimes.push(Date.now())
      
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Rapidly type username (simulating fast typing)
    const startTime = Date.now()
    await usernameInput.fill('u')
    await usernameInput.fill('us')
    await usernameInput.fill('use')
    await usernameInput.fill('user')
    await usernameInput.fill('user1')
    await usernameInput.fill('user12')
    await usernameInput.fill('user123')
    
    // Wait for debouncing to complete
    await page.waitForTimeout(1000)
    
    const endTime = Date.now()
    const totalTime = endTime - startTime
    
    // Should have made significantly fewer requests than changes due to debouncing
    expect(requestCount).toBeLessThan(7) // Less than the number of changes
    expect(requestCount).toBeGreaterThan(0) // But at least one request
    
    // Final result should be shown
    await expect(page.getByText('Username available')).toBeVisible()
    
    console.log(`Performance test: ${requestCount} requests in ${totalTime}ms`)
  })

  test('should handle concurrent requests from multiple inputs', async ({ page }) => {
    // Navigate to a page with multiple username inputs (simulate multiple forms)
    await page.evaluate(() => {
      // Add a second username input to test concurrent behavior
      const form = document.querySelector('form')
      if (form) {
        const secondInput = document.createElement('input')
        secondInput.setAttribute('type', 'text')
        secondInput.setAttribute('id', 'username2')
        secondInput.setAttribute('name', 'username2')
        secondInput.setAttribute('aria-label', 'Second Username')
        form.appendChild(secondInput)
      }
    })
    
    let requestCount = 0
    const concurrentRequests: string[] = []
    
    // Mock API to track concurrent requests
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      concurrentRequests.push(username || '')
      
      // Add small delay to simulate network latency
      await page.waitForTimeout(100)
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const firstInput = page.getByLabel('Username')
    const secondInput = page.getByLabel('Second Username')
    
    // Trigger concurrent requests
    await Promise.all([
      firstInput.fill('concurrent1'),
      secondInput.fill('concurrent2')
    ])
    
    // Wait for both requests to complete
    await page.waitForTimeout(1000)
    
    // Should have handled both requests
    expect(requestCount).toBeGreaterThanOrEqual(1)
    expect(concurrentRequests.length).toBeGreaterThanOrEqual(1)
    
    // Both should show success
    await expect(page.getByText('Username available')).toBeVisible()
  })

  test('should maintain performance under load simulation', async ({ page }) => {
    let requestCount = 0
    const responseTimes: number[] = []
    
    // Mock API with variable response times
    await page.route('/api/account/username/availability*', async route => {
      const startTime = Date.now()
      requestCount++
      
      // Simulate variable server load
      const delay = Math.random() * 200 + 50 // 50-250ms delay
      await page.waitForTimeout(delay)
      
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
      
      const endTime = Date.now()
      responseTimes.push(endTime - startTime)
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Simulate user typing behavior with pauses
    const usernames = ['test', 'testuser', 'testuser1', 'testuser12', 'testuser123']
    
    for (const username of usernames) {
      await usernameInput.clear()
      await usernameInput.fill(username)
      await page.waitForTimeout(400) // Simulate thinking time
    }
    
    // Wait for final request
    await page.waitForTimeout(1000)
    
    // Should have made requests for valid usernames
    expect(requestCount).toBeGreaterThan(0)
    
    // Calculate performance metrics
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
    const maxResponseTime = Math.max(...responseTimes)
    
    console.log(`Load test: ${requestCount} requests, avg: ${avgResponseTime}ms, max: ${maxResponseTime}ms`)
    
    // Performance assertions
    expect(avgResponseTime).toBeLessThan(500) // Average should be reasonable
    expect(maxResponseTime).toBeLessThan(1000) // Max should not be too high
    
    // Final state should be correct
    await expect(page.getByText('Username available')).toBeVisible()
  })

  test('should handle cache performance correctly', async ({ page }) => {
    let requestCount = 0
    const cachedUsernames = new Set<string>()
    
    // Mock API to track cache behavior
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      if (username) {
        cachedUsernames.add(username.toLowerCase())
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username, cached: false })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Test same username multiple times
    await usernameInput.fill('cachetest')
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    const firstRequestCount = requestCount
    
    // Clear and retype same username
    await usernameInput.clear()
    await usernameInput.fill('cachetest')
    await page.waitForTimeout(500)
    
    // Should use cache (no additional request)
    expect(requestCount).toBe(firstRequestCount)
    
    // Should still show available
    await expect(page.getByText('Username available')).toBeVisible()
    
    // Test case insensitive caching
    await usernameInput.clear()
    await usernameInput.fill('CACHETEST')
    await page.waitForTimeout(500)
    
    // Should use cache for case variation
    expect(requestCount).toBe(firstRequestCount)
  })

  test('should handle memory usage efficiently during extended use', async ({ page }) => {
    let requestCount = 0
    
    // Mock API
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Simulate extended usage with many different usernames
    const baseUsername = 'memorytest'
    
    for (let i = 0; i < 50; i++) {
      const username = `${baseUsername}${i}`
      await usernameInput.clear()
      await usernameInput.fill(username)
      await page.waitForTimeout(100) // Quick succession
    }
    
    // Wait for final request
    await page.waitForTimeout(1000)
    
    // Should have made requests but with debouncing
    expect(requestCount).toBeLessThan(50) // Debouncing should reduce requests
    expect(requestCount).toBeGreaterThan(0)
    
    // Memory usage test - page should still be responsive
    const finalUsername = 'finaltest'
    await usernameInput.clear()
    await usernameInput.fill(finalUsername)
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    console.log(`Memory test: ${requestCount} requests for 50 username changes`)
  })

  test('should handle timeout scenarios efficiently', async ({ page }) => {
    let requestCount = 0
    const timeoutRequests: string[] = []
    
    // Mock API with timeouts
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      if (username?.includes('timeout')) {
        timeoutRequests.push(username)
        // Don't respond to simulate timeout
        return
      }
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Test timeout scenario
    await usernameInput.fill('timeoutuser')
    
    // Should show timeout error within reasonable time
    await expect(page.getByText('Request timed out. Please try again.')).toBeVisible({ timeout: 10000 })
    
    // Change to non-timeout username
    await usernameInput.clear()
    await usernameInput.fill('normaluser')
    
    // Should recover quickly
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 5000 })
    
    expect(timeoutRequests.length).toBe(1)
    expect(requestCount).toBeGreaterThanOrEqual(2)
  })

  test('should maintain UI responsiveness during heavy load', async ({ page }) => {
    let requestCount = 0
    
    // Mock slow API responses
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      
      // Simulate heavy server load
      await page.waitForTimeout(500)
      
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Start typing
    await usernameInput.fill('slowresponse')
    
    // UI should remain responsive during API call
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    // Should be able to interact with other form elements
    const emailInput = page.getByLabel('Email')
    await emailInput.fill('<EMAIL>')
    await expect(emailInput).toHaveValue('<EMAIL>')
    
    // Should be able to change username while previous request is pending
    await usernameInput.clear()
    await usernameInput.fill('newusername')
    
    // Should show new checking state
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    // Eventually should show result
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 15000 })
    
    // Should have made requests but cancelled previous ones
    expect(requestCount).toBeGreaterThan(0)
  })
})