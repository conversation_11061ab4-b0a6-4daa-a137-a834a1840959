import { test, expect } from '@playwright/test'

test.describe('Username Validation Error Scenarios and Recovery', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/signup')
  })

  test('should handle intermittent network failures with retry', async ({ page }) => {
    let attemptCount = 0
    
    // Mock API to fail first few attempts, then succeed
    await page.route('/api/account/username/availability*', async route => {
      attemptCount++
      
      if (attemptCount <= 2) {
        // Fail first 2 attempts
        await route.abort('failed')
      } else {
        // Succeed on 3rd attempt
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username: 'retryuser' })
        })
      }
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('retryuser')
    
    // Should show network error initially
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Should show retry button
    const retryButton = page.getByRole('button', { name: 'Retry' })
    await expect(retryButton).toBeVisible()
    
    // Click retry
    await retryButton.click()
    
    // Should show checking state
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    // Should eventually succeed
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should handle server errors with appropriate messages', async ({ page }) => {
    // Mock server error
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('servererror')
    
    await page.waitForTimeout(500)
    
    // Should show server error message
    await expect(page.getByText('Server error occurred. Please try again later.')).toBeVisible({ timeout: 10000 })
    
    // Should show retry option
    await expect(page.getByRole('button', { name: 'Retry' })).toBeVisible()
  })

  test('should handle rate limiting gracefully', async ({ page }) => {
    // Mock rate limiting response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 429,
        contentType: 'application/json',
        headers: {
          'Retry-After': '60'
        },
        body: JSON.stringify({ error: 'Too many requests' })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('ratelimited')
    
    await page.waitForTimeout(500)
    
    // Should show rate limiting message
    await expect(page.getByText('Too many requests. Please wait before trying again.')).toBeVisible({ timeout: 10000 })
    
    // Retry button should be disabled initially
    const retryButton = page.getByRole('button', { name: 'Retry' })
    await expect(retryButton).toBeDisabled()
  })

  test('should handle malformed API responses', async ({ page }) => {
    // Mock malformed response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: 'invalid json'
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('malformed')
    
    await page.waitForTimeout(500)
    
    // Should show generic error message
    await expect(page.getByText('An unexpected error occurred. Please try again.')).toBeVisible({ timeout: 10000 })
    
    // Should allow retry
    await expect(page.getByRole('button', { name: 'Retry' })).toBeVisible()
  })

  test('should recover from offline state', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Simulate going offline
    await page.context().setOffline(true)
    
    await usernameInput.fill('offlineuser')
    await page.waitForTimeout(500)
    
    // Should show network error
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Go back online
    await page.context().setOffline(false)
    
    // Mock successful response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'offlineuser' })
      })
    })
    
    // Click retry
    await page.getByRole('button', { name: 'Retry' }).click()
    
    // Should recover and show success
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should handle exponential backoff for retries', async ({ page }) => {
    let attemptCount = 0
    const attemptTimes: number[] = []
    
    // Mock API to track retry timing
    await page.route('/api/account/username/availability*', async route => {
      attemptCount++
      attemptTimes.push(Date.now())
      
      if (attemptCount <= 3) {
        await route.abort('failed')
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username: 'backoffuser' })
        })
      }
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('backoffuser')
    
    // Wait for initial error
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Trigger retries
    const retryButton = page.getByRole('button', { name: 'Retry' })
    await retryButton.click()
    
    // Wait for second error
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    await retryButton.click()
    
    // Wait for third error
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    await retryButton.click()
    
    // Should eventually succeed
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 15000 })
    
    // Verify exponential backoff (each retry should take longer)
    expect(attemptTimes.length).toBeGreaterThan(1)
  })

  test('should clear error state when username is changed', async ({ page }) => {
    // Mock error response
    await page.route('/api/account/username/availability*', async route => {
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      if (username === 'erroruser') {
        await route.abort('failed')
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      }
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Trigger error
    await usernameInput.fill('erroruser')
    await page.waitForTimeout(500)
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Change username - should clear error
    await usernameInput.clear()
    await usernameInput.fill('gooduser')
    
    // Error should be cleared
    await expect(page.getByText('Network error. Please check your connection and try again.')).not.toBeVisible()
    
    // Should show success
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should handle concurrent error and success scenarios', async ({ page }) => {
    let requestCount = 0
    
    // Mock API with mixed responses
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      // Simulate race condition - some requests fail, others succeed
      if (requestCount % 2 === 0) {
        await route.abort('failed')
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username })
        })
      }
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Rapidly change username to trigger concurrent requests
    await usernameInput.fill('concurrent1')
    await usernameInput.fill('concurrent12')
    await usernameInput.fill('concurrent123')
    
    // Wait for final result
    await page.waitForTimeout(1000)
    
    // Should show either success or error, but not both
    const hasSuccess = await page.getByText('Username available').isVisible()
    const hasError = await page.getByText('Network error. Please check your connection and try again.').isVisible()
    
    expect(hasSuccess || hasError).toBe(true)
    expect(hasSuccess && hasError).toBe(false)
  })

  test('should handle validation errors during network recovery', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Start with invalid username
    await usernameInput.fill('ab')
    
    // Should show validation error
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
    
    // Mock network error for valid username
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    // Change to valid username that will trigger network error
    await usernameInput.clear()
    await usernameInput.fill('networktest')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // Change back to invalid username
    await usernameInput.clear()
    await usernameInput.fill('xy')
    
    // Should clear network error and show validation error
    await expect(page.getByText('Network error. Please check your connection and try again.')).not.toBeVisible()
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
  })

  test('should maintain form state during error recovery', async ({ page }) => {
    // Fill out entire form
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Display Name').fill('Test User')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Mock network error for username
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    const usernameInput = page.getByLabel('Username')
    await usernameInput.fill('erroruser')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // All other form fields should retain their values
    await expect(page.getByLabel('Email')).toHaveValue('<EMAIL>')
    await expect(page.getByLabel('Display Name')).toHaveValue('Test User')
    await expect(page.getByLabel('Password', { exact: true })).toHaveValue('password123')
    await expect(page.getByLabel('Confirm Password')).toHaveValue('password123')
    
    // Mock successful response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'erroruser' })
      })
    })
    
    // Retry should work
    await page.getByRole('button', { name: 'Retry' }).click()
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Form should still be complete and submittable
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeEnabled()
  })
})