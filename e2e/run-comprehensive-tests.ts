#!/usr/bin/env bun

/**
 * Comprehensive test runner for username validation functionality
 * 
 * This script runs all end-to-end tests in a structured way and provides
 * detailed reporting on test results and performance metrics.
 */

import { execSync } from 'child_process'
import { existsSync, writeFileSync } from 'fs'
import { join } from 'path'

interface TestSuite {
  name: string
  file: string
  description: string
  timeout?: number
}

const testSuites: TestSuite[] = [
  {
    name: 'Signup Flow',
    file: 'signup-flow.spec.ts',
    description: 'Complete signup flow with username validation',
    timeout: 60000
  },
  {
    name: 'Profile Update Flow',
    file: 'profile-update-flow.spec.ts',
    description: 'Profile update flow with username changes',
    timeout: 60000
  },
  {
    name: '<PERSON>rro<PERSON>',
    file: 'error-scenarios.spec.ts',
    description: 'Error handling and recovery mechanisms',
    timeout: 90000
  },
  {
    name: 'Performance Tests',
    file: 'performance-tests.spec.ts',
    description: 'Performance tests for concurrent username checks',
    timeout: 120000
  },
  {
    name: 'Accessibility Tests',
    file: 'accessibility-tests.spec.ts',
    description: 'Accessibility compliance and screen reader support',
    timeout: 90000
  },
  {
    name: 'Cross-browser Tests',
    file: 'cross-browser-tests.spec.ts',
    description: 'Cross-browser compatibility for debouncing behavior',
    timeout: 180000
  }
]

interface TestResult {
  suite: string
  passed: number
  failed: number
  skipped: number
  duration: number
  errors: string[]
}

class ComprehensiveTestRunner {
  private results: TestResult[] = []
  private startTime: number = 0

  async run() {
    console.log('🚀 Starting Comprehensive Username Validation Test Suite')
    console.log('=' .repeat(60))
    
    this.startTime = Date.now()

    // Check prerequisites
    await this.checkPrerequisites()

    // Run each test suite
    for (const suite of testSuites) {
      await this.runTestSuite(suite)
    }

    // Generate report
    await this.generateReport()
  }

  private async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...')

    // Check if Playwright is installed
    try {
      execSync('bunx playwright --version', { stdio: 'pipe' })
      console.log('✅ Playwright is installed')
    } catch (error) {
      console.error('❌ Playwright is not installed. Run: bun add --dev @playwright/test')
      process.exit(1)
    }

    // Check if browsers are installed
    try {
      execSync('bunx playwright install --dry-run', { stdio: 'pipe' })
      console.log('✅ Playwright browsers are installed')
    } catch (error) {
      console.log('⚠️  Installing Playwright browsers...')
      execSync('bunx playwright install', { stdio: 'inherit' })
    }

    // Check if development server can start
    console.log('✅ Prerequisites check complete')
    console.log('')
  }

  private async runTestSuite(suite: TestSuite) {
    console.log(`🧪 Running ${suite.name}`)
    console.log(`   ${suite.description}`)
    
    const startTime = Date.now()
    let result: TestResult = {
      suite: suite.name,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      errors: []
    }

    try {
      const command = `bunx playwright test e2e/${suite.file} --reporter=json`
      const output = execSync(command, { 
        stdio: 'pipe',
        timeout: suite.timeout || 60000,
        encoding: 'utf8'
      })

      // Parse Playwright JSON output
      const jsonOutput = JSON.parse(output)
      
      if (jsonOutput.suites) {
        for (const suiteResult of jsonOutput.suites) {
          for (const spec of suiteResult.specs) {
            for (const test of spec.tests) {
              for (const testResult of test.results) {
                switch (testResult.status) {
                  case 'passed':
                    result.passed++
                    break
                  case 'failed':
                    result.failed++
                    result.errors.push(`${test.title}: ${testResult.error?.message || 'Unknown error'}`)
                    break
                  case 'skipped':
                    result.skipped++
                    break
                }
              }
            }
          }
        }
      }

      result.duration = Date.now() - startTime

      if (result.failed === 0) {
        console.log(`   ✅ Passed (${result.passed} tests, ${result.duration}ms)`)
      } else {
        console.log(`   ❌ Failed (${result.failed} failures, ${result.passed} passed, ${result.duration}ms)`)
        result.errors.forEach(error => console.log(`      - ${error}`))
      }

    } catch (error) {
      result.failed = 1
      result.duration = Date.now() - startTime
      result.errors.push(`Suite execution failed: ${error}`)
      console.log(`   ❌ Suite failed to execute: ${error}`)
    }

    this.results.push(result)
    console.log('')
  }

  private async generateReport() {
    const totalDuration = Date.now() - this.startTime
    const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0)
    const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0)
    const totalSkipped = this.results.reduce((sum, r) => sum + r.skipped, 0)
    const totalTests = totalPassed + totalFailed + totalSkipped

    console.log('📊 Test Results Summary')
    console.log('=' .repeat(60))
    console.log(`Total Tests: ${totalTests}`)
    console.log(`Passed: ${totalPassed} (${((totalPassed / totalTests) * 100).toFixed(1)}%)`)
    console.log(`Failed: ${totalFailed} (${((totalFailed / totalTests) * 100).toFixed(1)}%)`)
    console.log(`Skipped: ${totalSkipped} (${((totalSkipped / totalTests) * 100).toFixed(1)}%)`)
    console.log(`Total Duration: ${(totalDuration / 1000).toFixed(2)}s`)
    console.log('')

    // Detailed results by suite
    console.log('📋 Detailed Results by Suite')
    console.log('-' .repeat(60))
    
    for (const result of this.results) {
      const total = result.passed + result.failed + result.skipped
      const passRate = total > 0 ? ((result.passed / total) * 100).toFixed(1) : '0.0'
      
      console.log(`${result.suite}:`)
      console.log(`  Tests: ${total} | Passed: ${result.passed} | Failed: ${result.failed} | Skipped: ${result.skipped}`)
      console.log(`  Pass Rate: ${passRate}% | Duration: ${(result.duration / 1000).toFixed(2)}s`)
      
      if (result.errors.length > 0) {
        console.log(`  Errors:`)
        result.errors.forEach(error => console.log(`    - ${error}`))
      }
      console.log('')
    }

    // Generate JSON report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests,
        totalPassed,
        totalFailed,
        totalSkipped,
        totalDuration,
        passRate: ((totalPassed / totalTests) * 100).toFixed(1)
      },
      suites: this.results
    }

    const reportPath = join(process.cwd(), 'e2e-test-report.json')
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`📄 Detailed report saved to: ${reportPath}`)

    // Generate HTML report if possible
    try {
      execSync('bunx playwright show-report --reporter=html', { stdio: 'pipe' })
      console.log('🌐 HTML report available at: playwright-report/index.html')
    } catch (error) {
      console.log('⚠️  HTML report generation failed')
    }

    console.log('')

    // Exit with appropriate code
    if (totalFailed > 0) {
      console.log('❌ Some tests failed. Check the report for details.')
      process.exit(1)
    } else {
      console.log('✅ All tests passed successfully!')
      process.exit(0)
    }
  }
}

// Run the comprehensive test suite
if (require.main === module) {
  const runner = new ComprehensiveTestRunner()
  runner.run().catch(error => {
    console.error('💥 Test runner failed:', error)
    process.exit(1)
  })
}

export { ComprehensiveTestRunner }