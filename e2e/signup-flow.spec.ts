import { test, expect } from '@playwright/test'
import { injectAxe, checkA11y } from 'axe-playwright'

test.describe('Signup Flow with Username Validation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/signup')
    await injectAxe(page)
  })

  test('should display signup form with username input', async ({ page }) => {
    // Verify form elements are present
    await expect(page.getByLabel('Email')).toBeVisible()
    await expect(page.getByLabel('Username')).toBeVisible()
    await expect(page.getByLabel('Display Name')).toBeVisible()
    await expect(page.getByLabel('Password', { exact: true })).toBeVisible()
    await expect(page.getByLabel('Confirm Password')).toBeVisible()
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeVisible()
  })

  test('should show username requirements on focus', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Focus on username input
    await usernameInput.focus()
    
    // Check that requirements are displayed
    await expect(page.getByText('Username requirements:')).toBeVisible()
    await expect(page.getByText('3-30 characters long')).toBeVisible()
    await expect(page.getByText('Only letters, numbers, hyphens, and underscores')).toBeVisible()
    await expect(page.getByText('Cannot start or end with special characters')).toBeVisible()
    await expect(page.getByText('No consecutive special characters')).toBeVisible()
    
    // Focus away and check requirements are hidden
    await page.getByLabel('Email').focus()
    await expect(page.getByText('Username requirements:')).not.toBeVisible()
  })

  test('should validate username format in real-time', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Test too short username
    await usernameInput.fill('ab')
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
    
    // Test invalid characters
    await usernameInput.fill('user@name')
    await expect(page.getByText('Username can only contain letters, numbers, hyphens, and underscores')).toBeVisible()
    
    // Test starting with special character
    await usernameInput.fill('_username')
    await expect(page.getByText('Username cannot start with special characters')).toBeVisible()
    
    // Test ending with special character
    await usernameInput.fill('username_')
    await expect(page.getByText('Username cannot end with special characters')).toBeVisible()
    
    // Test consecutive special characters
    await usernameInput.fill('user__name')
    await expect(page.getByText('Username cannot contain consecutive special characters')).toBeVisible()
  })

  test('should check username availability with debouncing', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Type a valid username
    await usernameInput.fill('testuser123')
    
    // Should show loading state
    await expect(page.getByText('Checking availability...')).toBeVisible()
    
    // Wait for debounce and API call
    await page.waitForTimeout(500)
    
    // Should show availability result (assuming username is available)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Verify checkmark icon is visible
    await expect(page.locator('[aria-label="Username available"]')).toBeVisible()
  })

  test('should handle unavailable username', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Mock API response for unavailable username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: false, username: 'admin' })
      })
    })
    
    await usernameInput.fill('admin')
    await page.waitForTimeout(500)
    
    // Should show unavailable message
    await expect(page.getByText('Username is already taken')).toBeVisible({ timeout: 10000 })
    
    // Verify error icon is visible
    await expect(page.locator('[aria-label="Username unavailable"]')).toBeVisible()
    
    // Submit button should be disabled
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeDisabled()
  })

  test('should handle network errors gracefully', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Mock network error
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    await usernameInput.fill('networktest')
    await page.waitForTimeout(500)
    
    // Should show network error message
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
  })

  test('should handle API timeout', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Mock slow API response (timeout)
    await page.route('/api/account/username/availability*', async route => {
      // Delay response beyond timeout
      await page.waitForTimeout(6000)
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'timeouttest' })
      })
    })
    
    await usernameInput.fill('timeouttest')
    await page.waitForTimeout(500)
    
    // Should show timeout error
    await expect(page.getByText('Request timed out. Please try again.')).toBeVisible({ timeout: 10000 })
  })

  test('should prevent form submission when username is being checked', async ({ page }) => {
    // Fill out all form fields
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Username').fill('checkinguser')
    await page.getByLabel('Display Name').fill('Test User')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Mock slow API response to keep checking state
    await page.route('/api/account/username/availability*', async route => {
      await page.waitForTimeout(2000)
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'checkinguser' })
      })
    })
    
    // Wait for checking state
    await expect(page.getByText('Checking availability...')).toBeVisible({ timeout: 1000 })
    
    // Submit button should show checking state and be disabled
    await expect(page.getByRole('button', { name: 'Checking username...' })).toBeDisabled()
    
    // Try to submit form - should not work
    await page.getByRole('button', { name: 'Checking username...' }).click()
    
    // Should still be on signup page
    await expect(page).toHaveURL('/auth/signup')
  })

  test('should prevent form submission when username is unavailable', async ({ page }) => {
    // Mock unavailable username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: false, username: 'takenuser' })
      })
    })
    
    // Fill out form with unavailable username
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Username').fill('takenuser')
    await page.getByLabel('Display Name').fill('Test User')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username is already taken')).toBeVisible({ timeout: 10000 })
    
    // Submit button should be disabled
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeDisabled()
    
    // Try to submit form - should not work
    await page.getByRole('button', { name: 'Create Account' }).click()
    
    // Should still be on signup page
    await expect(page).toHaveURL('/auth/signup')
  })

  test('should complete successful signup flow', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'newuser123' })
      })
    })
    
    // Mock successful signup API
    await page.route('/api/auth/signup', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })
    
    // Mock successful sign-in
    await page.route('/api/auth/signin', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ url: '/dashboard' })
      })
    })
    
    // Fill out form
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Username').fill('newuser123')
    await page.getByLabel('Display Name').fill('New User')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Wait for username availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Submit form
    await page.getByRole('button', { name: 'Create Account' }).click()
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard', { timeout: 10000 })
  })

  test('should handle signup API errors', async ({ page }) => {
    // Mock available username
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'erroruser' })
      })
    })
    
    // Mock signup API error
    await page.route('/api/auth/signup', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Username already exists' })
      })
    })
    
    // Fill out form
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Username').fill('erroruser')
    await page.getByLabel('Display Name').fill('Error User')
    await page.getByLabel('Password', { exact: true }).fill('password123')
    await page.getByLabel('Confirm Password').fill('password123')
    
    // Wait for username availability check
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Submit form
    await page.getByRole('button', { name: 'Create Account' }).click()
    
    // Should show error message
    await expect(page.getByText('Username already exists')).toBeVisible({ timeout: 10000 })
    
    // Should still be on signup page
    await expect(page).toHaveURL('/auth/signup')
  })

  test('should be accessible', async ({ page }) => {
    // Check accessibility of the signup form
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    })
    
    // Test keyboard navigation
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Email')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Username')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Display Name')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Password', { exact: true })).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Confirm Password')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByRole('button', { name: 'Create Account' })).toBeFocused()
  })

  test('should work on mobile devices', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip('This test is only for mobile devices')
    }
    
    // Test mobile-specific behavior
    const usernameInput = page.getByLabel('Username')
    
    // Touch interaction
    await usernameInput.tap()
    await expect(page.getByText('Username requirements:')).toBeVisible()
    
    // Virtual keyboard should not interfere with validation
    await usernameInput.fill('mobileuser')
    await page.waitForTimeout(500)
    
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
  })
})