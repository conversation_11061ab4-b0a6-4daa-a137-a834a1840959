import { Page, expect } from '@playwright/test'

/**
 * Test utilities for username validation end-to-end tests
 */

export class UsernameTestHelpers {
  constructor(private page: Page) {}

  /**
   * Mock successful username availability check
   */
  async mockAvailableUsername(username: string, cached = false) {
    await this.page.route('/api/account/username/availability*', async route => {
      const url = new URL(route.request().url())
      const requestedUsername = url.searchParams.get('username')
      
      if (requestedUsername === username) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: true, username, cached })
        })
      } else {
        await route.continue()
      }
    })
  }

  /**
   * Mock unavailable username
   */
  async mockUnavailableUsername(username: string) {
    await this.page.route('/api/account/username/availability*', async route => {
      const url = new URL(route.request().url())
      const requestedUsername = url.searchParams.get('username')
      
      if (requestedUsername === username) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ available: false, username })
        })
      } else {
        await route.continue()
      }
    })
  }

  /**
   * Mock network error for username check
   */
  async mockNetworkError(username?: string) {
    await this.page.route('/api/account/username/availability*', async route => {
      if (username) {
        const url = new URL(route.request().url())
        const requestedUsername = url.searchParams.get('username')
        
        if (requestedUsername === username) {
          await route.abort('failed')
        } else {
          await route.continue()
        }
      } else {
        await route.abort('failed')
      }
    })
  }

  /**
   * Mock API timeout
   */
  async mockTimeout(username?: string) {
    await this.page.route('/api/account/username/availability*', async route => {
      if (username) {
        const url = new URL(route.request().url())
        const requestedUsername = url.searchParams.get('username')
        
        if (requestedUsername === username) {
          // Don't respond to simulate timeout
          return
        }
      }
      // Don't respond to any request to simulate timeout
    })
  }

  /**
   * Mock server error
   */
  async mockServerError(username?: string, status = 500, message = 'Internal server error') {
    await this.page.route('/api/account/username/availability*', async route => {
      if (username) {
        const url = new URL(route.request().url())
        const requestedUsername = url.searchParams.get('username')
        
        if (requestedUsername === username) {
          await route.fulfill({
            status,
            contentType: 'application/json',
            body: JSON.stringify({ error: message })
          })
        } else {
          await route.continue()
        }
      } else {
        await route.fulfill({
          status,
          contentType: 'application/json',
          body: JSON.stringify({ error: message })
        })
      }
    })
  }

  /**
   * Mock rate limiting
   */
  async mockRateLimit(retryAfter = 60) {
    await this.page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 429,
        contentType: 'application/json',
        headers: {
          'Retry-After': retryAfter.toString()
        },
        body: JSON.stringify({ error: 'Too many requests' })
      })
    })
  }

  /**
   * Fill signup form with valid data
   */
  async fillSignupForm(data: {
    email?: string
    username?: string
    displayName?: string
    password?: string
    confirmPassword?: string
  } = {}) {
    const defaults = {
      email: '<EMAIL>',
      username: 'testuser',
      displayName: 'Test User',
      password: 'password123',
      confirmPassword: 'password123'
    }

    const formData = { ...defaults, ...data }

    if (formData.email) {
      await this.page.getByLabel('Email').fill(formData.email)
    }
    if (formData.username) {
      await this.page.getByLabel('Username').fill(formData.username)
    }
    if (formData.displayName) {
      await this.page.getByLabel('Display Name').fill(formData.displayName)
    }
    if (formData.password) {
      await this.page.getByLabel('Password', { exact: true }).fill(formData.password)
    }
    if (formData.confirmPassword) {
      await this.page.getByLabel('Confirm Password').fill(formData.confirmPassword)
    }
  }

  /**
   * Wait for username availability check to complete
   */
  async waitForUsernameCheck(timeout = 10000) {
    // Wait for either success or error state
    await Promise.race([
      this.page.getByText('Username available').waitFor({ timeout }),
      this.page.getByText('Username is already taken').waitFor({ timeout }),
      this.page.getByText('Network error. Please check your connection and try again.').waitFor({ timeout }),
      this.page.getByText('Request timed out. Please try again.').waitFor({ timeout }),
      this.page.getByText('Server error occurred. Please try again later.').waitFor({ timeout })
    ])
  }

  /**
   * Assert username input has correct validation state
   */
  async assertValidationState(state: 'valid' | 'invalid' | 'checking' | 'available' | 'unavailable' | 'error') {
    const usernameInput = this.page.getByLabel('Username')

    switch (state) {
      case 'valid':
        await expect(usernameInput).toHaveAttribute('aria-invalid', 'false')
        break
      case 'invalid':
        await expect(usernameInput).toHaveAttribute('aria-invalid', 'true')
        break
      case 'checking':
        await expect(this.page.getByText('Checking availability...')).toBeVisible()
        break
      case 'available':
        await expect(this.page.getByText('Username available')).toBeVisible()
        await expect(this.page.locator('[aria-label="Username available"]')).toBeVisible()
        break
      case 'unavailable':
        await expect(this.page.getByText('Username is already taken')).toBeVisible()
        await expect(this.page.locator('[aria-label="Username unavailable"]')).toBeVisible()
        break
      case 'error':
        await expect(this.page.getByRole('button', { name: 'Retry' })).toBeVisible()
        break
    }
  }

  /**
   * Assert submit button state
   */
  async assertSubmitButtonState(state: 'enabled' | 'disabled' | 'checking') {
    const submitButton = this.page.getByRole('button', { name: /create account|checking username/i })

    switch (state) {
      case 'enabled':
        await expect(submitButton).toBeEnabled()
        await expect(submitButton).toHaveText('Create Account')
        break
      case 'disabled':
        await expect(submitButton).toBeDisabled()
        break
      case 'checking':
        await expect(submitButton).toBeDisabled()
        await expect(submitButton).toHaveText(/checking username/i)
        break
    }
  }

  /**
   * Mock successful signup flow
   */
  async mockSuccessfulSignup() {
    await this.page.route('/api/auth/signup', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true })
      })
    })

    await this.page.route('/api/auth/signin', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ url: '/dashboard' })
      })
    })
  }

  /**
   * Mock authentication for profile tests
   */
  async mockAuthentication(userData = {
    id: '1',
    email: '<EMAIL>',
    username: 'currentuser',
    displayName: 'Current User'
  }) {
    await this.page.addInitScript(() => {
      window.localStorage.setItem('auth-token', 'mock-token')
    })

    await this.page.route('/api/auth/session', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ user: userData })
      })
    })

    await this.page.route('/api/profile', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ ...userData, bio: 'Current bio' })
        })
      } else {
        await route.continue()
      }
    })
  }

  /**
   * Create request counter for performance testing
   */
  createRequestCounter() {
    let requestCount = 0
    const requestTimes: number[] = []

    return {
      setupMock: async (responseDelay = 0) => {
        await this.page.route('/api/account/username/availability*', async route => {
          requestCount++
          requestTimes.push(Date.now())

          if (responseDelay > 0) {
            await this.page.waitForTimeout(responseDelay)
          }

          const url = new URL(route.request().url())
          const username = url.searchParams.get('username')

          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ available: true, username })
          })
        })
      },
      getCount: () => requestCount,
      getTimes: () => [...requestTimes],
      reset: () => {
        requestCount = 0
        requestTimes.length = 0
      }
    }
  }

  /**
   * Simulate typing with realistic delays
   */
  async typeWithDelay(selector: string, text: string, delay = 100) {
    const element = this.page.locator(selector)
    await element.clear()
    
    for (const char of text) {
      await element.type(char)
      await this.page.waitForTimeout(delay)
    }
  }

  /**
   * Check accessibility compliance
   */
  async checkAccessibility() {
    // This would integrate with axe-playwright
    const { injectAxe, checkA11y } = await import('axe-playwright')
    await injectAxe(this.page)
    await checkA11y(this.page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    })
  }

  /**
   * Simulate mobile device behavior
   */
  async simulateMobileInput(text: string) {
    const usernameInput = this.page.getByLabel('Username')
    
    // Mobile devices often use tap instead of click
    await usernameInput.tap()
    
    // Mobile keyboards might have different timing
    await this.page.keyboard.type(text, { delay: 150 })
  }

  /**
   * Test keyboard navigation
   */
  async testKeyboardNavigation() {
    // Test tab order
    await this.page.keyboard.press('Tab')
    await expect(this.page.getByLabel('Email')).toBeFocused()
    
    await this.page.keyboard.press('Tab')
    await expect(this.page.getByLabel('Username')).toBeFocused()
    
    await this.page.keyboard.press('Tab')
    await expect(this.page.getByLabel('Display Name')).toBeFocused()
    
    // Test reverse navigation
    await this.page.keyboard.press('Shift+Tab')
    await expect(this.page.getByLabel('Username')).toBeFocused()
  }
}

/**
 * Performance measurement utilities
 */
export class PerformanceHelpers {
  private measurements: Map<string, number[]> = new Map()

  startMeasurement(name: string): () => number {
    const startTime = Date.now()
    return () => {
      const duration = Date.now() - startTime
      if (!this.measurements.has(name)) {
        this.measurements.set(name, [])
      }
      this.measurements.get(name)!.push(duration)
      return duration
    }
  }

  getStats(name: string) {
    const times = this.measurements.get(name) || []
    if (times.length === 0) return null

    const avg = times.reduce((a, b) => a + b, 0) / times.length
    const min = Math.min(...times)
    const max = Math.max(...times)
    const median = times.sort((a, b) => a - b)[Math.floor(times.length / 2)]

    return { avg, min, max, median, count: times.length }
  }

  logStats(name: string) {
    const stats = this.getStats(name)
    if (stats) {
      console.log(`${name}: avg=${stats.avg}ms, min=${stats.min}ms, max=${stats.max}ms, median=${stats.median}ms, count=${stats.count}`)
    }
  }

  clear() {
    this.measurements.clear()
  }
}