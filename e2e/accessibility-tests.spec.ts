import { test, expect } from '@playwright/test'
import { injectAxe, checkA11y } from 'axe-playwright'

test.describe('Username Input Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/signup')
    await injectAxe(page)
  })

  test('should meet WCAG accessibility standards', async ({ page }) => {
    // Check overall page accessibility
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true },
      rules: {
        // Focus on form and input accessibility
        'color-contrast': { enabled: true },
        'keyboard': { enabled: true },
        'focus-order-semantics': { enabled: true },
        'aria-required-attr': { enabled: true },
        'aria-valid-attr-value': { enabled: true },
        'form-field-multiple-labels': { enabled: true },
        'label': { enabled: true }
      }
    })
  })

  test('should have proper ARIA attributes', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Check basic ARIA attributes
    await expect(usernameInput).toHaveAttribute('aria-describedby', 'username-status')
    await expect(usernameInput).toHaveAttribute('aria-invalid', 'false')
    await expect(usernameInput).toHaveAttribute('type', 'text')
    await expect(usernameInput).toHaveAttribute('autoComplete', 'username')
    
    // Check that status region exists
    const statusRegion = page.locator('#username-status')
    await expect(statusRegion).toHaveAttribute('role', 'status')
    await expect(statusRegion).toHaveAttribute('aria-live', 'polite')
  })

  test('should update ARIA attributes based on validation state', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Test invalid state
    await usernameInput.fill('ab') // Too short
    await expect(usernameInput).toHaveAttribute('aria-invalid', 'true')
    
    // Test valid state
    await usernameInput.clear()
    await usernameInput.fill('validuser')
    
    // Mock successful availability check
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'validuser' })
      })
    })
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    await expect(usernameInput).toHaveAttribute('aria-invalid', 'false')
  })

  test('should support keyboard navigation', async ({ page }) => {
    // Test tab order
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Email')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Username')).toBeFocused()
    
    // Test that requirements appear on focus
    await expect(page.getByText('Username requirements:')).toBeVisible()
    
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Display Name')).toBeFocused()
    
    // Requirements should be hidden when focus moves away
    await expect(page.getByText('Username requirements:')).not.toBeVisible()
    
    // Test reverse tab order
    await page.keyboard.press('Shift+Tab')
    await expect(page.getByLabel('Username')).toBeFocused()
    await expect(page.getByText('Username requirements:')).toBeVisible()
  })

  test('should announce status changes to screen readers', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    const statusRegion = page.locator('#username-status')
    
    // Test checking state announcement
    await page.route('/api/account/username/availability*', async route => {
      await page.waitForTimeout(1000) // Delay to test loading state
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'testuser' })
      })
    })
    
    await usernameInput.fill('testuser')
    
    // Should announce checking state
    await expect(statusRegion).toContainText('Checking availability...')
    await expect(statusRegion).toHaveAttribute('aria-live', 'polite')
    
    // Should announce result
    await expect(statusRegion).toContainText('Username available', { timeout: 10000 })
  })

  test('should handle error announcements properly', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    const statusRegion = page.locator('#username-status')
    
    // Test validation error announcement
    await usernameInput.fill('ab')
    await expect(statusRegion).toContainText('Username must be at least 3 characters')
    
    // Test network error announcement
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    await usernameInput.clear()
    await usernameInput.fill('networkerror')
    
    await page.waitForTimeout(500)
    await expect(statusRegion).toContainText('Network error. Please check your connection and try again.', { timeout: 10000 })
    
    // Test unavailable username announcement
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: false, username: 'taken' })
      })
    })
    
    await usernameInput.clear()
    await usernameInput.fill('taken')
    
    await page.waitForTimeout(500)
    await expect(statusRegion).toContainText('Username is already taken', { timeout: 10000 })
  })

  test('should support high contrast mode', async ({ page }) => {
    // Simulate high contrast mode
    await page.emulateMedia({ colorScheme: 'dark' })
    
    const usernameInput = page.getByLabel('Username')
    
    // Test that elements are still visible and functional
    await expect(usernameInput).toBeVisible()
    
    // Test validation states in high contrast
    await usernameInput.fill('ab')
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
    
    // Test success state
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'contrasttest' })
      })
    })
    
    await usernameInput.clear()
    await usernameInput.fill('contrasttest')
    
    await page.waitForTimeout(500)
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Check accessibility in high contrast mode
    await checkA11y(page, null, {
      rules: {
        'color-contrast': { enabled: true }
      }
    })
  })

  test('should work with screen reader simulation', async ({ page }) => {
    // Simulate screen reader behavior by checking aria-describedby relationships
    const usernameInput = page.getByLabel('Username')
    
    // Focus on input
    await usernameInput.focus()
    
    // Check that describedby points to requirements when focused
    const describedBy = await usernameInput.getAttribute('aria-describedby')
    expect(describedBy).toContain('username-requirements')
    expect(describedBy).toContain('username-status')
    
    // Check that requirements are properly labeled
    const requirementsRegion = page.locator('#username-requirements')
    await expect(requirementsRegion).toHaveAttribute('role', 'region')
    await expect(requirementsRegion).toHaveAttribute('aria-label', 'Username requirements')
    
    // Test that all requirement items are properly structured
    const requirementItems = requirementsRegion.locator('li')
    const itemCount = await requirementItems.count()
    expect(itemCount).toBeGreaterThan(0)
    
    // Each requirement should be readable
    for (let i = 0; i < itemCount; i++) {
      const item = requirementItems.nth(i)
      const text = await item.textContent()
      expect(text).toBeTruthy()
      expect(text!.length).toBeGreaterThan(0)
    }
  })

  test('should handle focus management correctly', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Focus on username input
    await usernameInput.focus()
    await expect(usernameInput).toBeFocused()
    
    // Requirements should be visible
    await expect(page.getByText('Username requirements:')).toBeVisible()
    
    // Type invalid username
    await usernameInput.fill('ab')
    
    // Focus should remain on input even with error
    await expect(usernameInput).toBeFocused()
    
    // Error should be announced but not steal focus
    await expect(page.getByText('Username must be at least 3 characters')).toBeVisible()
    
    // Tab away and back
    await page.keyboard.press('Tab')
    await expect(page.getByLabel('Display Name')).toBeFocused()
    
    await page.keyboard.press('Shift+Tab')
    await expect(usernameInput).toBeFocused()
    
    // Requirements should reappear
    await expect(page.getByText('Username requirements:')).toBeVisible()
  })

  test('should support reduced motion preferences', async ({ page }) => {
    // Simulate reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' })
    
    const usernameInput = page.getByLabel('Username')
    
    // Mock API response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'motiontest' })
      })
    })
    
    await usernameInput.fill('motiontest')
    
    // Should still show loading and success states without animations
    await expect(page.getByText('Checking availability...')).toBeVisible()
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Visual indicators should still be present
    await expect(page.locator('[aria-label="Username available"]')).toBeVisible()
  })

  test('should work with voice control simulation', async ({ page }) => {
    // Simulate voice control by using accessible names and roles
    const usernameInput = page.getByLabel('Username')
    
    // Should be findable by accessible name
    await expect(page.getByRole('textbox', { name: 'Username' })).toBeVisible()
    
    // Should be able to interact via accessible name
    await page.getByRole('textbox', { name: 'Username' }).fill('voicetest')
    
    // Status should be findable by role
    await expect(page.getByRole('status')).toBeVisible()
    
    // Mock API response
    await page.route('/api/account/username/availability*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username: 'voicetest' })
      })
    })
    
    await page.waitForTimeout(500)
    
    // Success indicator should be findable by accessible name
    await expect(page.getByLabelText('Username available')).toBeVisible({ timeout: 10000 })
  })

  test('should handle multiple error states accessibly', async ({ page }) => {
    const usernameInput = page.getByLabel('Username')
    
    // Test validation error
    await usernameInput.fill('ab')
    await expect(usernameInput).toHaveAttribute('aria-invalid', 'true')
    
    let errorMessage = page.getByText('Username must be at least 3 characters')
    await expect(errorMessage).toBeVisible()
    
    // Clear validation error and trigger network error
    await page.route('/api/account/username/availability*', async route => {
      await route.abort('failed')
    })
    
    await usernameInput.clear()
    await usernameInput.fill('networkerror')
    
    await page.waitForTimeout(500)
    
    // Should clear previous error and show new one
    await expect(page.getByText('Username must be at least 3 characters')).not.toBeVisible()
    await expect(page.getByText('Network error. Please check your connection and try again.')).toBeVisible({ timeout: 10000 })
    
    // aria-invalid should still be true
    await expect(usernameInput).toHaveAttribute('aria-invalid', 'true')
    
    // Retry button should be accessible
    const retryButton = page.getByRole('button', { name: 'Retry' })
    await expect(retryButton).toBeVisible()
    await expect(retryButton).toBeEnabled()
    
    // Should be keyboard accessible
    await retryButton.focus()
    await expect(retryButton).toBeFocused()
  })

  test('should maintain accessibility during dynamic updates', async ({ page }) => {
    let requestCount = 0
    
    // Mock API with delays to test dynamic updates
    await page.route('/api/account/username/availability*', async route => {
      requestCount++
      await page.waitForTimeout(200)
      
      const url = new URL(route.request().url())
      const username = url.searchParams.get('username')
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ available: true, username })
      })
    })
    
    const usernameInput = page.getByLabel('Username')
    
    // Rapidly change username
    await usernameInput.fill('dynamic1')
    await usernameInput.fill('dynamic12')
    await usernameInput.fill('dynamic123')
    
    // Should maintain accessibility throughout updates
    await expect(usernameInput).toHaveAttribute('aria-describedby')
    
    // Status region should always be present
    const statusRegion = page.locator('#username-status')
    await expect(statusRegion).toHaveAttribute('role', 'status')
    await expect(statusRegion).toHaveAttribute('aria-live', 'polite')
    
    // Should eventually show final result
    await expect(page.getByText('Username available')).toBeVisible({ timeout: 10000 })
    
    // Final accessibility check
    await checkA11y(page, null, {
      rules: {
        'aria-valid-attr-value': { enabled: true },
        'aria-required-attr': { enabled: true }
      }
    })
  })
})