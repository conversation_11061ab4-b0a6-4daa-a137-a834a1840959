import { test, expect, devices } from '@playwright/test'

test.describe('Accessibility and Mobile Responsiveness', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication for testing
    await page.goto('/dashboard')
  })

  test.describe('Desktop Accessibility', () => {
    test('should have proper keyboard navigation', async ({ page }) => {
      // Test skip links
      await page.keyboard.press('Tab')
      const skipLink = page.locator('text=Skip to main content')
      await expect(skipLink).toBeFocused()
      
      // Test navigation menu keyboard access
      await page.keyboard.press('Tab')
      const firstNavItem = page.locator('[role="menuitem"]').first()
      await expect(firstNavItem).toBeFocused()
      
      // Test arrow key navigation
      await page.keyboard.press('ArrowDown')
      const secondNavItem = page.locator('[role="menuitem"]').nth(1)
      await expect(secondNavItem).toBeFocused()
    })

    test('should have proper focus indicators', async ({ page }) => {
      // Check that all interactive elements have visible focus
      const interactiveElements = page.locator('button, a, input, select, textarea')
      const count = await interactiveElements.count()
      
      for (let i = 0; i < Math.min(count, 10); i++) {
        const element = interactiveElements.nth(i)
        await element.focus()
        
        // Check that element has focus styles
        const focusStyles = await element.evaluate(el => {
          const styles = window.getComputedStyle(el)
          return {
            outline: styles.outline,
            boxShadow: styles.boxShadow,
            borderColor: styles.borderColor
          }
        })
        
        // At least one focus indicator should be present
        const hasFocusIndicator = 
          focusStyles.outline !== 'none' || 
          focusStyles.boxShadow !== 'none' ||
          focusStyles.borderColor !== 'initial'
        
        expect(hasFocusIndicator).toBeTruthy()
      }
    })

    test('should have proper ARIA labels and landmarks', async ({ page }) => {
      // Check main landmark
      const main = page.locator('main')
      await expect(main).toHaveAttribute('id', 'main-content')
      
      // Check navigation landmark
      const nav = page.locator('nav[role="navigation"]')
      await expect(nav).toBeVisible()
      
      // Check that buttons have accessible names
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i)
        const accessibleName = await button.evaluate(btn => {
          return btn.textContent?.trim() || 
                 btn.getAttribute('aria-label') || 
                 btn.getAttribute('title') ||
                 ''
        })
        expect(accessibleName.length).toBeGreaterThan(0)
      }
    })
  })

  test.describe('Mobile Responsiveness', () => {
    test.use({ ...devices['iPhone 12'] })

    test('should have proper touch target sizes', async ({ page }) => {
      // Check that all interactive elements meet minimum touch target size
      const interactiveElements = page.locator('button, a[href], input, select')
      const count = await interactiveElements.count()
      
      for (let i = 0; i < count; i++) {
        const element = interactiveElements.nth(i)
        const boundingBox = await element.boundingBox()
        
        if (boundingBox) {
          expect(boundingBox.width).toBeGreaterThanOrEqual(44)
          expect(boundingBox.height).toBeGreaterThanOrEqual(44)
        }
      }
    })

    test('should have mobile-friendly navigation', async ({ page }) => {
      // Check that mobile menu button is visible
      const mobileMenuButton = page.locator('button[aria-label*="navigation menu"]')
      await expect(mobileMenuButton).toBeVisible()
      
      // Test mobile menu functionality
      await mobileMenuButton.click()
      
      // Check that menu is expanded
      await expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'true')
      
      // Check that navigation items are visible
      const navItems = page.locator('[role="menuitem"]')
      await expect(navItems.first()).toBeVisible()
      
      // Test closing menu with escape key
      await page.keyboard.press('Escape')
      await expect(mobileMenuButton).toHaveAttribute('aria-expanded', 'false')
    })

    test('should have responsive text and layout', async ({ page }) => {
      // Check that text is readable (minimum 16px on mobile)
      const textElements = page.locator('p, span, div').filter({ hasText: /.+/ })
      const count = await textElements.count()
      
      for (let i = 0; i < Math.min(count, 10); i++) {
        const element = textElements.nth(i)
        const fontSize = await element.evaluate(el => {
          return parseFloat(window.getComputedStyle(el).fontSize)
        })
        
        // Most text should be at least 16px on mobile
        if (fontSize > 0) {
          expect(fontSize).toBeGreaterThanOrEqual(14) // Allow some flexibility
        }
      }
    })

    test('should not have horizontal scrolling', async ({ page }) => {
      // Check that page doesn't scroll horizontally
      const scrollWidth = await page.evaluate(() => document.body.scrollWidth)
      const clientWidth = await page.evaluate(() => document.body.clientWidth)
      
      expect(scrollWidth).toBeLessThanOrEqual(clientWidth + 5) // Allow small tolerance
    })
  })

  test.describe('Public Profile Accessibility', () => {
    test('should have accessible profile page', async ({ page }) => {
      // Navigate to a public profile (assuming test data exists)
      await page.goto('/testuser')
      
      // Check heading hierarchy
      const h1 = page.locator('h1')
      await expect(h1).toBeVisible()
      
      // Check that links have proper labels
      const linkButtons = page.locator('button[aria-label*="Visit"]')
      const linkCount = await linkButtons.count()
      
      for (let i = 0; i < linkCount; i++) {
        const link = linkButtons.nth(i)
        const ariaLabel = await link.getAttribute('aria-label')
        expect(ariaLabel).toContain('Visit')
        expect(ariaLabel).toContain('Opens in new tab')
      }
      
      // Check that profile has proper structure
      const main = page.locator('main')
      await expect(main).toHaveAttribute('role', 'main')
      
      const navigation = page.locator('nav[aria-label*="links"]')
      if (await navigation.count() > 0) {
        await expect(navigation).toBeVisible()
      }
    })
  })

  test.describe('Form Accessibility', () => {
    test('should have accessible forms', async ({ page }) => {
      // Navigate to links page
      await page.goto('/dashboard/links')
      
      // Open link creation form
      const addLinkButton = page.locator('button', { hasText: 'Add Link' })
      await addLinkButton.click()
      
      // Check form accessibility
      const titleInput = page.locator('input[id="title"]')
      const titleLabel = page.locator('label[for="title"]')
      
      await expect(titleLabel).toBeVisible()
      await expect(titleInput).toHaveAttribute('aria-describedby')
      
      // Check required field indicators
      const requiredIndicator = page.locator('span[aria-label="required"]')
      await expect(requiredIndicator).toBeVisible()
      
      // Test form validation
      const submitButton = page.locator('button[type="submit"]')
      await submitButton.click()
      
      // Check that error messages are announced
      const errorMessage = page.locator('[role="alert"]')
      if (await errorMessage.count() > 0) {
        await expect(errorMessage).toBeVisible()
      }
    })
  })

  test.describe('Color Contrast and Visual Accessibility', () => {
    test('should have sufficient color contrast', async ({ page }) => {
      // This is a basic check - in practice you'd use specialized tools
      // Check that text is visible against backgrounds
      const textElements = page.locator('p, span, h1, h2, h3, button')
      const count = await textElements.count()
      
      for (let i = 0; i < Math.min(count, 5); i++) {
        const element = textElements.nth(i)
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el)
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
            opacity: computed.opacity
          }
        })
        
        // Basic check that text isn't transparent
        expect(parseFloat(styles.opacity)).toBeGreaterThan(0.1)
      }
    })

    test('should support high contrast mode', async ({ page }) => {
      // Simulate high contrast mode
      await page.emulateMedia({ colorScheme: 'dark', forcedColors: 'active' })
      
      // Check that interactive elements are still visible
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < Math.min(buttonCount, 3); i++) {
        const button = buttons.nth(i)
        await expect(button).toBeVisible()
      }
    })
  })

  test.describe('Reduced Motion Support', () => {
    test('should respect reduced motion preferences', async ({ page }) => {
      // Simulate reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' })
      
      // Check that animations are reduced or disabled
      const animatedElements = page.locator('[class*="transition"], [class*="animate"]')
      const count = await animatedElements.count()
      
      // This is a basic check - in practice you'd verify actual animation durations
      expect(count).toBeGreaterThanOrEqual(0) // Just ensure page loads without errors
    })
  })
})